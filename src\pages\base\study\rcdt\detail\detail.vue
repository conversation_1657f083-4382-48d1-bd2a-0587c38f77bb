<template>
	<view>
		<view class="top u-flex u-row-between u-p-30 u-border-top">
			<text style="color: #2979ff;">[{{ typeName }}](左滑进入下一题)</text>
			<view>
				<text style="color: #2979ff;">{{ current + 1 }}</text>
				<text>/{{ all }}</text>
			</view>
		</view>
		<swiper class="swiper" :current="current" :duration="200" @animationfinish="animationfinish">
			<swiper-item v-for="(item,idx) in questionList" :key="idx">
				<view class="question u-p-l-30 u-p-r-30">
					<text>{{item.questionBank.content}}</text>
					<!-- 单选题 -->
					<view class="u-p-t-30" v-if="item.questionBank.type == 1">
						<view v-for="option in optionList" :key="option">
							<view class="u-flex u-col-center u-m-b-30" style="color: #696969;" v-if="item.questionBank[`choice${option}`]" @click="handleRadioAnswer(option, idx, item.answer)">
								<text class="option" :class="{ check: answerList[idx].userAnswer == option}" style="border-radius: 50%;">{{ option }}</text>
								<text class="u-flex-1">{{item.questionBank[`choice${option}`]}}</text>
							</view>
						</view>
					</view>
					<!-- 多选题 -->
					<view class="u-p-t-30" v-if="item.questionBank.type == 2">
						<view v-for="option in optionList" :key="option">
							<view class="u-flex u-col-center u-m-b-30" style="color: #696969;" v-if="item.questionBank[`choice${option}`]" @click="handleCheckAnswer(option, idx, item.answer)">
								<text class="option" :class="{ check: answerList[idx].userAnswer.includes(option) }">{{ option }}</text>
								<text class="u-flex-1">{{item.questionBank[`choice${option}`]}}</text>
							</view>
						</view>
					</view>
					<!-- 判断题 -->
					<view class="u-p-t-30" v-if="item.questionBank.type == 3">
						<view class="u-flex u-col-center u-m-b-30" style="color: #696969;" @click="handleRadioAnswer('T', idx, item.answer)">
							<view class="option" :class="{ check: answerList[idx].userAnswer == 'T'}" style="border-radius: 50%;">
								<u-icon name="checkmark" size="24"></u-icon><!-- ✔ -->
							</view>
							<text class="u-flex-1">正确</text>
						</view>
						<view class="u-flex u-col-center u-m-b-30" style="color: #696969;" @click="handleRadioAnswer('F', idx, item.answer)">
							<view class="option" :class="{ check: answerList[idx].userAnswer == 'F'}" style="border-radius: 50%;">
								<u-icon name="close" size="24"></u-icon><!-- ✖ -->
							</view>
							<text class="u-flex-1">错误</text>
						</view>
					</view>
				</view>
				<!-- 提交按钮 -->
				<view class="btn-box u-flex" v-if="questionList.length - 1 == idx || isAll">
					<u-button type="primary" class="u-flex-1" @click="showAnswerList = true">结束答题</u-button>
				</view>
			</swiper-item>
		</swiper>
		<!-- 答题卡 -->
		<u-popup v-model="showAnswerList" duration="100" mode="right" length="750rpx">
			<view class="answer-list">
				<view class="item" v-for="(item,idx) in answerList" :key="idx" @click="hadnleJump(idx)">
					<text class="item-inner" :class="{ checked: item.status != 0 }">{{ idx + 1 }}</text>
				</view>
			</view>
			<view class="sub-btn">
				<u-button type="primary" class="u-flex-1" @click="handleSubmit">提交试卷</u-button>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				typeName: '单选题',
				all: 1,
				current: 0,
				questionList: [],
				optionList: ['A','B','C','D','E','F','G','H'],
				answerList: [],
				lastTransitionX: null,
				showAnswerList: false,
				examId: ''
			}
		},
		computed: {
			isAll() {
				// 全部题目都已经回答过了更改为true
				return this.answerList.every(item => item.status != 0)
			}
		},
		methods: {
			fetchData(examId) {
				this.$loading()
				this.$u.api.getQuestionInfo({ examId, userId: this.vuex_id }).then(res => {
					uni.hideLoading()
					this.questionList = res.questionList
					this.answerList = res.questionList.map(item => {
						return {
							id: item.id,
							userAnswer: '',
							answerDate: '',
							status: 0,
							examId: res.data.id
						}
					})
					this.all = res.questionList.length
				}).catch(() => {
					uni.hideLoading()
				})
			},
			animationfinish(e) {
				this.current = e.detail.current
				this.typeName = this.questionList[this.current].questionBank.typeName
			},
			handleRadioAnswer(option, idx, answer) {
				// 单选/判断
				const timestamp = new Date().getTime()
				this.answerList[idx].userAnswer = option
				this.answerList[idx].answerDate = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss');
				this.answerList[idx].status = option == answer ? 1 : 2
			},
			handleCheckAnswer(option, idx, answer) {
				// 多选
				const timestamp = new Date().getTime()
				let userAnswer = this.answerList[idx].userAnswer ? this.answerList[idx].userAnswer.split('') : []
				let optionIdx = userAnswer.indexOf(option)
				if (optionIdx == -1) {
					userAnswer.push(option)
				} else {
					userAnswer.splice(optionIdx, 1)
				}
				this.answerList[idx].userAnswer = userAnswer.sort().join('')
				this.answerList[idx].answerDate = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss');
				this.answerList[idx].status = this.answerList[idx].userAnswer == answer ? 1 : 2
			},
			hadnleJump(idx) {
				this.current = idx
				this.showAnswerList = false
			},
			handleSubmit() {
				uni.showModal({
					title: '提示',
					content: this.isAll ? '是否确认提交试卷？' : '还有题目未完成，是否确认提交？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading('正在提交')
							this.$u.api.finishQuestion(this.answerList).then(res => {
								this.mToase('提交成功')
								this.$u.route({ type: 'redirectTo', url: 'pages/base/study/rcdt/answer/answer', params: { id: this.examId, isShow: true } })
								uni.hideLoading()
							}).catch(() => {
								uni.hideLoading()
							})
						}
					}
				})
			}
		},
		onLoad(params) {
			this.examId = params.id
			this.fetchData(this.examId)
		},
		onBackPress(e) {
			if (this.showAnswerList && e.from== 'backbutton') {
				this.showAnswerList = false
				return true
			} else if (e.from== 'backbutton') {
				uni.showModal({
					title: '提示',
					content: '是否确认退出答题？',
					success: ({ confirm }) => {
						if (confirm) {
							uni.navigateBack()
						}
					}
				})
				return true
			}
		},
		onNavigationBarButtonTap() {
			// 点击右上角按钮
			this.showAnswerList = true
		}
	}
</script>

<style lang="scss">
page {
	background-color: #fff;
}
.top {
	height: 100rpx;
}
.swiper {
	min-height: calc(100vh - 100rpx);
	/* #ifdef H5 */
	min-height: calc(100vh - 100rpx - 44px);
	/* #endif */
	.option {
		width: 50rpx;
		height: 50rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		border: 1px solid #c1c1c1;
		border-radius: 10rpx;
		flex-shrink: 1;
		margin-right: 10rpx;
		.u-icon {
			width: 50rpx;
			height: 50rpx;
			justify-content: center;
		}
		&.check {
			background-color: #007AFF;
			border-color: #007AFF;
			color: #fff;
		}
	}
}
.btn-box {
	width: 100%;
	position: absolute;
	bottom: 0;
	left: 0;
	background: #fff;
	padding: 30rpx;
}
.answer-list {
	width: 100%;
	height: calc(100% - 140rpx);
	overflow: auto;
	.item {
		width: 150rpx;
		height: 150rpx;
		display: inline-block;
		line-height: 150rpx;
		text-align: center;
		.item-inner {
			width: 75rpx;
			height: 75rpx;
			border: 1px solid #82848A;
			border-radius: 50%;
			display: inline-block;
			line-height: 75rpx;
			text-align: center;
			&.checked {
				border-color: #007AFF;
				background-color: #007AFF;
				color: #fff;
			}
		}
	}
}
.sub-btn {
	height: 140rpx;
	background-color: #fff;
	padding: 30rpx;
}
</style>

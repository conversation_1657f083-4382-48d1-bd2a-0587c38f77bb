<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="代班人:" prop="userName" required>
					<u-input v-model="form.userName" type="popup" placeholder="代班人" />
				</u-form-item>
				<u-form-item label="代班人部门:" prop="deptName" required>
					<u-input v-model="form.deptName" type="popup" placeholder="代班人部门" />
				</u-form-item>
				<u-form-item label="被代班人:" prop="substituteUserName" required>
					<u-input v-model="form.substituteUserName" type="popup" placeholder="被代班人" />
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('substituteUserId')">选择人员</u-button>
				</u-form-item>
				<u-form-item label="被代班人部门:" prop="substituteDeptName" required>
					<u-input v-model="form.substituteDeptName" type="popup" placeholder="被代班人部门" />
				</u-form-item>
				<u-form-item label="代班日期:" prop="happenTime" required>
					<u-input v-model="form.happenTime" type="popup" placeholder="请选择代班日期" @click="showHappenTime = true"  />
					<u-icon name="calendar"  @click="showHappenTime = true"  size="40"></u-icon>
					<u-picker v-model="showHappenTime" mode="time" :default-time="form.happenTime" :params="params"  @confirm="confirmHappenTime"></u-picker>
				</u-form-item>
				<u-form-item label="申请理由:" prop="title" label-position="top" :border-bottom="false">
					<u-input v-model="form.title" type="textarea" placeholder="请输入申请理由" />
				</u-form-item>	
			</view>
	
		</u-form>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex">
			<u-button class="u-flex-1 u-m-r-20" type="primary" shape="circle" :loading="aloading" :custom-style="subStyle" @click="handleSubmit(1)">暂存</u-button>
			<u-button class="u-flex-1" type="primary" shape="circle" :loading="aloading" :custom-style="subStyle" @click="handleOver">提交</u-button>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import gps from '@/common/gps.js'
	export default {
		data() {
			return {
				aloading: false,
				params: { year: true, month: true, day: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				form: {},
				rules: {
					userName: [{ required: true, message: '请选择代班人', trigger: ['change', 'blur'] }],
					deptName: [{ required: true, message: '请选择代班人部门', trigger: ['change', 'blur'] }],
					substituteUserName: [{ required: true, message: '请选择被代班人', trigger: ['change', 'blur'] }],
					substituteDeptName: [{ required: true, message: '请选择被代班人部门', trigger: ['change', 'blur'] }],
					happenTime: [{ required: true, message: '请选择代班日期', trigger: 'change' }],
					// title: [{ required: true, message: '请填写申请理由', trigger: ['change', 'blur'] }],
				},
				showHappenTime: false
			}
		},
		computed: {
			
		},
		methods: {
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				if (!isFail) {
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					this.$implement()
				}
			},
			confirmHappenTime(time){
				const { year, month, day} = time
				const now = new Date(`${year}-${month}-${day}`)
				if(new Date() > now) return this.mToase('代班时间不早于当前时间！')
				// this.form = { ...this.form, businessStartTime: `${year}-${month}-${day}` }
				this.form = { ...this.form, happenTime: `${year}-${month}-${day}` }
			},

			handleChooseHandleUser(type) {				
				let params = {}
				if (this.form[type]) {
					params.defaultCheckedKeys = this.form[type]
					params.defaultExpandedKeys = this.form[type]
				}
				params.showRadio = 1
				params.isJurisdiction = 0
				params.typeList = '1'
				params.type = type
				this.$u.route({ url: 'pages/common/selectUser/selectUser', params })
			},
			setUserData(checks,type,parent) {
				// 选择好人员后的回调
				let names = [], ids = [], parents = {}
				if (checks && checks.length) checks.forEach(item => {
					names.push(item.label)
					ids.push(item.id)
					parents = parent
				})
				
				this.form = { ...this.form, substituteUserName: names.join(','), substituteUserId: ids.join(',') , substituteDeptId: parents.data.id, substituteDeptName: parents.label}
			
			},
			handleSubmit(status) {
				// if(this.form.status){
				// 	if(this.vuex_id != this.form.userId) return this.mToase('您不是督查人，没有操作权限！')
				// }
				this.aloading = true
				this.$loading()
				const params = { ...this.form, happenTime: `${this.form.happenTime} 00:00:00`, status }
				let methodsFn = this.form.id != null ? this.$u.api.updateSubstituteList : this.$u.api.addSubstituteList
				methodsFn(params).then(res => {
					uni.hideLoading()
					uni.showToast({title: '操作成功'})
					this.$implement()
					this.aloading = false
				}).catch(() => {
					uni.hideLoading()
					this.aloading = false
				})
			},
			handleOver() {
				this.$refs.uForm.validate(valid => {
					if (valid) {
						this.handleSubmit(2)
					}
				})
				
			}
		},
		async onLoad(params) {
			if (params.id) {
				this.$loading()
				const standardRes = await this.$u.api.getSubstitute({}, params.id)
				
				/* 数据请求完毕 */
				uni.hideLoading()
				if (standardRes.code == 200) {
					/* 表单数据 */
					this.form = standardRes.data
				}
			} else {
				const timestamp = new Date().getTime()
				const happenTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd')
				this.form = { happenTime, userId:this.vuex_id, userName: this.vuex_nickName, deptName:this.vuex_deptName, deptId:this.vuex_deptId }
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.pos-r {
	height: 50rpx;
	line-height: 50rpx;
	font-size: 24rpx;
	background-color: #2979ff;
	border-radius: 10rpx;
	position: absolute;
	top: 30rpx;
	right: 0;
	color: #FFFFFF;
	padding: 0 15rpx;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

<template>
	<view>
		<view class="top-search u-flex u-col-center u-border-top u-border-bottom">
			<view class="top-search-left u-flex u-flex-1">
				<u-image src="@/static/img/top-search.png" width="35rpx" height="35rpx"></u-image>
				<u-input v-model="searchValue" type="text" class="u-flex-1 u-m-l-20"></u-input>
			</view>
			<view class="top-search-right">
				<view class="top-search-right-btn u-flex u-row-center u-col-center" @click="fetchData">
					<u-image src="@/static/img/filter.png" width="28rpx" height="28rpx" class="img"></u-image>
					<text>筛选</text>
				</view>
			</view>
		</view>
		<view class="content">
			<ly-tree 
				ref="tree"
				node-key="checkStandardId"
				defaultExpandAll
				:ready="ready"
				:props="props"
				:tree-data="treeData" 
				:checkOnClickNode="true"
				:showRadio="showRadio"
				:showCheckbox="!showRadio"
				:checkStrictly="true"
				:defaultCheckedKeys="defaultCheckedKeys"
				:defaultExpandedKeys="defaultExpandedKeys"
				:isInjectParentInNode="true"
			>
			</ly-tree>
		</view>
		
		<!-- 确认按钮 -->
		<view class="btns">
			<u-button type="primary" shape="circle" :custom-style="subStyle" @click="submit" class="btn u-m-t-24">确认</u-button>
		</view>
	</view>
</template>

<script>
	import LyTree from '@/components/ly-tree/ly-tree.vue'
	export default {
		components: {
			LyTree
		},
		data() {
			return {
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				showRadio: true,
				treeData: [],
				type: '',
				ready: true,
				defaultCheckedKeys: [],
				defaultExpandedKeys: [],
				props: {
					label: 'title',
					disabled(data) {
						return data.status != 1
					}
				},
				name:'',
				searchValue: ''
			}
		},
		
		onLoad(param = {}) {
			// @ showRadio   是否为单选，默认单选，多选传0
			// @ type        若上层页面有多个人员选择，用以区分不同人员选择
			this.showRadio = param.showRadio == 0 ? false : true
			this.type = param.type || ''
			if (param.defaultCheckedKeys) {
				this.defaultCheckedKeys = param.defaultCheckedKeys.split(',')
			}
			if (param.defaultExpandedKeys) {
				this.defaultExpandedKeys = param.defaultExpandedKeys.split(',')
			}
			this.name = param.name
			this.fetchData(this.name);
		},
		methods: {
			fetchData(name) {
				uni.showLoading({ title: '数据加载中...', mask: true })
				this.ready = false
				let params = {}
				if (this.searchValue.trim()) params.title = this.searchValue.trim()
				this.$u.api.getStandardList(params).then(res => {
					this.treeData = this.handleTree(res.data, 'checkStandardId')
					this.ready = true
					uni.stopPullDownRefresh()
					uni.hideLoading()
				}).catch(() => {
					this.ready = true
					uni.hideLoading()
					uni.stopPullDownRefresh()
				})
			},
			submit() {
				let checkeds = this.$refs.tree.getCheckedNodes()
				let pages = getCurrentPages();             //获取所有页面栈实例列表
				let prevPage = pages[ pages.length - 2 ];  //上一页页面实例
				if (prevPage.$vm.setStandard) {
					prevPage.$vm.setStandard(checkeds, this.type)
				}
				uni.navigateBack();
			},
			handleTree(data, id, parentId, children) {
			  let config = {
			    id: id || 'id',
			    parentId: parentId || 'parentId',
			    childrenList: children || 'children'
			  }
			
			  var childrenListMap = {}
			  var nodeIds = {}
			  var tree = []
			
			  for (let d of data) {
			    let parentId = d[config.parentId]
			    if (childrenListMap[parentId] == null) {
			      childrenListMap[parentId] = []
			    }
			    nodeIds[d[config.id]] = d
			    childrenListMap[parentId].push(d)
			  }
			
			  for (let d of data) {
			    let parentId = d[config.parentId]
			    if (nodeIds[parentId] == null) {
			      tree.push(d)
			    }
			  }
			
			  for (let t of tree) {
			    adaptToChildrenList(t)
			  }
			
			  function adaptToChildrenList(o) {
			    if (childrenListMap[o[config.id]] !== null) {
			      o[config.childrenList] = childrenListMap[o[config.id]]
			    }
			    if (o[config.childrenList]) {
			      for (let c of o[config.childrenList]) {
			        adaptToChildrenList(c)
			      }
			    }
			  }
			  return tree
			}
		},
		onPullDownRefresh(){
			this.fetchData(this.name)
		}
	}
</script>

<style lang="scss">
	.top-search {
		position: fixed;
		width: 100%;
		height: 103rpx;
		background-color: #fff;
		padding: 0 30rpx;
		position: fixed;
		z-index: 10;
		top: 0;
		/* #ifdef H5 */
		top: 44px;
		/* #endif */
		&-left {
			height: 68rpx;
			background-color: #F5F5F5;
			border-radius: 68rpx;
			padding: 0 20rpx;
			margin-right: 20rpx;
		}
		&-right {
			&-btn {
				width: 136rpx;
				height: 68rpx;
				border-radius: 68rpx;
				background-color: #327BF0;
				color: #fff;
				font-size: 28rpx;
				transition: all 0.5s;
				.img {
					margin-right: 10rpx;
				}
				&:active {
					opacity: 0.3;
				}
			}
		}
	}
	.checkbox-box {
		width: 100%;
	}
	.checkbox-item {
		padding: 24rpx;
		width: 100%;
	}
	.u-checkbox__label, .u-radio__label {
		width: 100%;
	}
	.content {
		position: absolute;
		bottom: 124rpx;
		top: 103rpx;
		overflow-y: auto;
		width: 100%;
	}
	.btns {
		position: fixed;
		bottom: 0rpx;
		width: 100%;
		height: 124rpx;
		padding: 0 30rpx;
		z-index: 100;
	}
</style>

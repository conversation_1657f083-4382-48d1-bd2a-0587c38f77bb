<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="150" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="执法队员:" prop="userName" required>
					<u-input v-model="form.userName" type="popup" placeholder="执法队员" />
				</u-form-item>
				<u-form-item label="部门名称:" prop="deptName" required>
					<u-input v-model="form.deptName" type="popup" placeholder="部门名称" />
				</u-form-item>
			</view>

			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="违规人:" prop="lawbreakers">
					<u-input v-model="form.lawbreakers" type="text" placeholder="违规人" />
				</u-form-item>

				<!-- <u-form-item label="身份证:" prop="identityCard">
					<u-input v-model="form.identityCard" type="text" placeholder="身份证号码" />
				</u-form-item> -->
				<u-form-item label="联系电话:" prop="phone">
					<u-input v-model="form.phone" type="text" placeholder="联系电话" />
				</u-form-item>
				<u-form-item label="车牌号:" prop="carNo">
					<u-input v-model="form.carNo" type="text" placeholder="车牌号" />
				</u-form-item>
				<u-form-item label="发生时间:" prop="happenTime" required>
					<u-input v-model="form.happenTime" type="popup" placeholder="请选择发生时间" @click="showHappenTime = true"  />
					<u-icon name="calendar"  @click="showHappenTime = true"  size="40"></u-icon>
					<u-picker v-model="showHappenTime" mode="time" :default-time="form.happenTime" :params="params"  @confirm="confirmHappenTime"></u-picker>
				</u-form-item>
				<u-form-item label="地址:" prop="address" required>
					<u-input v-model="form.address" type="text" placeholder="请选择地址" />
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseAddress">选择地址</u-button>
				</u-form-item>
				<u-form-item label="案件类型:" prop="caseTypeName" required>
					<u-input v-model="form.caseTypeName" placeholder="请选择案件类型"  type="select" :select-open="showCaseTypeList" @click="showCaseTypeList = true" />
					<u-action-sheet v-model="showCaseTypeList" :list="caseTypeList" @click="caseTypeClick"></u-action-sheet>
				</u-form-item>
				<u-form-item label="处理类型:" prop="handleTypeName" required>
					<u-input v-model="form.handleTypeName" placeholder="请选择处理类型" type="select" :select-open="showHandleTypeList" @click="showHandleTypeList = true" />
					<u-action-sheet v-model="showHandleTypeList" :list="handleTypeList" @click="handleTypeClick"></u-action-sheet>
				</u-form-item>
				<u-form-item label="违规内容:" prop="content" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.content" type="textarea" placeholder="违规内容" />
				</u-form-item>
				<u-upload
					:file-list="fileList"
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
					:size-type="['compressed']"
					:before-remove="handleRemove"
					@on-uploaded="handleAllUpload"
					@on-error="handleError"
				></u-upload>
			</view>

			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<!-- 生成答题二维码按钮 -->
			<view class="exam_btn"  v-if="examBtnShow">
				<view class="exam_bg">
					<view class="exam_bear" @click="bearExam">点击生成试卷</view>
				</view>
			</view>
			<!-- 答题二维码 -->
			<view class="exam_content" v-if="examShow">
				<view class="exam_info">
					<view class="exam_to_btn" @click="$u.route({ url: 'pages/home/<USER>/rxzf/examAnswer/examAnswer', params: { id: form.examId } })">点击查看试卷详情</view>
					<u-image  :src="examQrCodeUrl" width="220rpx" height="220rpx"></u-image>
					<text class="exam_name">{{form.examName}}</text>
				</view>
			</view>

		</u-form>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex">
			<u-button class="u-flex-1 u-m-r-20" type="primary" shape="circle" :loading="aloading" :custom-style="subStyle" @click="handleSubmit(1)">暂存</u-button>
			<u-button class="u-flex-1" type="primary" shape="circle" :loading="aloading" :custom-style="subStyle" @click="handleOver">提交</u-button>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import gps from '@/common/gps.js'
	export default {
		data() {
			return {
				examBtnShow:false,
				examShow:false,
				// 试卷二维码
				examQrCodeUrl:'',
				caseTypeList:[],
				handleTypeList:[
					{ type: 1, text: '口头教育' },
					{ type: 2, text: '答题教育' },
					// { type: 3, text: '朋友圈检讨' }
				],
				showCaseTypeList:false,
				showHandleTypeList:false,
				aloading: false,
				fileList: [],
				// 存储班长/责任人
				role:'',
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				form: {address:"浙江省金华市婺城区城北街道"},
				happenData: {
					tableName: 'case_violation_record',
					status: 1
				},
				rules: {
					// userName: [{ required: true, message: '请输入执法队员', trigger: ['change', 'blur'] }],
					// deptName: [{ required: true, message: '请输入部门名称', trigger: ['change', 'blur'] }],
					// lawbreakers: [{ required: true, message: '请输入违规人', trigger: ['change', 'blur'] }],

					// identityCard: [
					// 	// { required: true, message: '请输入身份证号码', trigger: 'blur' },
					// 	{
					// 		validator: (rule, value, callback) => {
					// 			return this.$u.test.idCard(value);
					// 		},
					// 		message: '身份证号不正确'
					// 	}],
					// phone: [
					// 	// { required: true, message: '请输入违规人联系电话', trigger: 'blur' },
					// 	{
					// 		validator: (rule, value, callback) => {
					// 			return this.$u.test.mobile(value);
					// 		},
					// 		message: '手机号码不正确'
					// 	}
					// ],
					// carNo: [{ required: true, message: '请输入车牌号', trigger: ['change', 'blur'] }],
					carNo: [
						{
							validator: (rule, value, callback) => {
								// 车牌号正则表达式，支持新能源车牌
								const carNoReg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]$/;
								if (!value) return true;
								return carNoReg.test(value);
							},
							message: '车牌号格式不正确'
						}
					],
					happenTime: [{ required: true, message: '请选择发生时间', trigger: ['change', 'blur'] }],
					address: [{ required: true, message: '请输入地址', trigger: ['change', 'blur'] }],
					caseTypeName: [{ required: true, message: '请选择案件类型', trigger: ['change', 'blur'] }],
					handleTypeName: [{ required: true, message: '请选择处理类型', trigger: ['change', 'blur'] }],
					content: [{ required: true, message: '请输入违规内容', trigger: ['change', 'blur'] }],//
				},
				showHappenTime: false
			}
		},
		computed: {
			action() {
				// #ifdef H5  process.env["VUE_APP_BASE_URL"]
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			// 处理类型
			handleTypeClick(idx) {
				const { type, text } = this.handleTypeList[idx]
				this.form = { ...this.form, handleType:type, handleTypeName: text }
				if(this.form.handleType == 2) {
					if(this.form.examId) {
						this.form = { ...this.form, examId: this.form.examId,examName: this.form.examName}
						this.$u.api.getExamDetail({},this.form.examId).then(res=>{
							this.examQrCodeUrl = res.msg
						})
						this.examShow = true
						this.examBtnShow = false
						return
					}else{
						this.examBtnShow = true
						this.examShow = false
					}
				}else{
					this.examBtnShow = false
					this.examShow = false
				}
			},
			// 点击生成试卷
			bearExam(){
				this.$u.api.getExam().then(res=>{
					console.log(res)
					this.form = { ...this.form, examId: res.data.id,examName: res.data.name}
					this.examQrCodeUrl = res.data.qrCode
				})
				this.examBtnShow = false
				this.examShow = true
			},
			// 案件类型
			caseTypeClick(idx) {
				const { dictValue, dictLabel } = this.caseTypeList[idx]
				this.form = { ...this.form, caseType:dictValue, caseTypeName: dictLabel }
			},
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				if (!isFail) {
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					this.$implement()
				}
			},
			handleRemove(index, lists) {
				const fileInfo = lists[index]
				const fileId = fileInfo.url.split('?id=')[1]
				if (fileId) {
						return new Promise((resolve, reject) => {
							uni.showModal({
								title: '提示',
								content: '删除后将无法恢复，是否确认删除？',
								success: ({ confirm }) => {
									if (confirm) {
										this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
									} else {
										reject()
									}
								}
							})
						})
				} else {
					return true
				}
			},
			handleError() {
				// 文件上传失败，弹出提示是否重新上传
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					content: '图片上传失败，是否重新上传？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading('图片上传中')
							this.$refs.happenfile.reUpload()
						} else {
							this.$implement({ immediately: true })
						}
					}
				})
			},
			handleChooseAddress() {
				uni.chooseLocation({
					latitude: 29.110764,
					longitude: 119.635857,
					geocode: true,
					success: res => {
						const { address, longitude, latitude } = res
						const lnglat = gps.gcj_decrypt(parseFloat(latitude), parseFloat(longitude))
						this.form = { ...this.form, address, longitude: lnglat.lng, latitude: lnglat.lat }
					},
					fail: () => {
						uni.showToast({
							title: '地图打开失败',
							icon: 'none',
							position: 'bottom'
						})
					}
				})
			},
			confirmHappenTime(time){
				const { year, month, day, hour, minute, second} = time
				// this.form = { ...this.form, businessStartTime: `${year}-${month}-${day}` }
				this.form = { ...this.form, happenTime: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleSubmit(status) {
				if(this.form.status){
					if(this.vuex_id != this.form.userId) return this.mToase('您不是执法队员，没有操作权限！')
				}
				this.aloading = true
				this.$loading()
				if(this.form.handleType != 2) {
					this.form = {...this.form,examId:'',examName: ''}
				}else if(this.form.handleType == 2 && status == 9 && !this.form.examId){
					this.mToase('请点击生成试卷！')
					this.aloading = false
					return
				}
				const params = { ...this.form, status }
				let methodsFn = this.form.violationId != null ? this.$u.api.editViolation : this.$u.api.addViolation
				methodsFn(params).then(res => {
					const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
					this.happenData.businessId = this.form.violationId || res.data.violationId
					if (uploadFile) {
						this.$loading('图片上传中')
						this.$refs.happenfile.upload()
					} else {
						uni.hideLoading()
						uni.showToast({title: '操作成功'})
						this.$implement()
					}
				}).catch(() => {
					uni.hideLoading()
					this.aloading = false
				})
			},
			handleOver() {
				if(this.form.status){
					if(this.vuex_id != this.form.userId) return this.mToase('您不是执法队员，没有操作权限！')
				}
				this.$refs.uForm.validate(valid => {
					// 图片验证，没有图片不通过验证
					if (!this.$refs.happenfile.lists.length) {
						this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
						return
					}

					if (valid) {
						uni.showModal({
							title: '提示',
							content: '是否确认提交？',
							success: ({ confirm }) => {
								if (confirm) {
									this.handleSubmit(9)
								}
							}
						})
					}
				})

			}
		},
		async onLoad(params) {
			// 数据字典数据
			this.$u.api.dictList({dictType:"case_violation_type"}).then(res=>{
				this.caseTypeList = res.rows.map(item => {
					item.text = item.dictLabel
					item.type = item.dictValue
					return item
				})
			})

			if (params.id) {
				this.$loading()
				const [standardRes, fileRes] = await Promise.all([
					this.$u.api.getViolation({}, params.id),
					this.$u.api.getFileList({ tableName: 'case_violation_record', businessId: params.id })
				])
				/* 数据请求完毕 */
				uni.hideLoading()
				if (standardRes.code == 200 && fileRes.code == 200) {
					/* 表单数据 */
					this.form = standardRes.data
					// 展示处理类型
					const typeText = this.handleTypeList.find(item => item.type == this.form.handleType)
					this.form = { ...this.form, handleType: typeText ? typeText.type : '' ,handleTypeName:typeText ? typeText.text : ''}

					if(this.form.handleType == 2) {
						// 选择答题教育，并且已经生成试卷
						if(this.form.examId) {
							this.$u.api.getExamDetail({},this.form.examId).then(res=>{
								this.examQrCodeUrl = res.msg
							})
							this.examShow = true
							this.examBtnShow = false
						}else{
							// 选择答题教育，未生成试卷
							this.examShow = false
							this.examBtnShow = true
						}
					}
					else this.examShow = false
					/* 文件数据 */
					this.fileList = fileRes.rows.map(item => {
            return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
					})
				}


			} else {
				const timestamp = new Date().getTime()
				const happenTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = { type: '1', happenTime, userId:this.vuex_id, userName: this.vuex_nickName,deptName:this.vuex_deptName,deptId:this.vuex_deptId }
				// this.form = { type: '1', happenTime, userId:this.vuex_id, userName: this.vuex_nickName,deptName:this.vuex_deptName,deptId:this.vuex_deptId,address:'金华火车站',longitude:'119.635918',latitude:'29.110464' }
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.pos-r {
	height: 50rpx;
	line-height: 50rpx;
	font-size: 24rpx;
	background-color: #2979ff;
	border-radius: 10rpx;
	position: absolute;
	top: 30rpx;
	right: 0;
	color: #FFFFFF;
	padding: 0 15rpx;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}

// 生成试卷按钮
.exam_btn{
	background: #fff;
	margin: 0 15rpx;
	border-radius: 10rpx;
	padding: 20rpx;
	.exam_bg{
		display: flex;
		flex-direction: column;
		align-items: center;
		background: #F1F7FF;
		border-radius: 10rpx;
		padding: 20rpx;
		.exam_bear{
			display: flex;
			align-items: center;
			justify-content: center;
			color: #fff;
			font-size: 32rpx;
			font-weight: 500;
			width: 650rpx;
			height: 70rpx;
			border-radius: 10rpx;
			background: #4689F5;
		}
	}
}
// 试卷二维码
.exam_content{
	background: #fff;
	margin: 0 15rpx;
	border-radius: 10rpx;
	padding: 20rpx;
	.exam_info{
		display: flex;
		flex-direction: column;
		align-items: center;
		background: #F1F7FF;
		border-radius: 10rpx;
		padding: 30rpx;
		.exam_to_btn{
			display: flex;
			align-items: center;
			justify-content: center;
			color: #fff;
			font-size: 32rpx;
			font-weight: 500;
			width: 650rpx;
			height: 70rpx;
			border-radius: 10rpx;
			background: #4689F5;
			margin-bottom: 20rpx;
			// overflow: hidden;
		}
		.exam_name{
			font-size: 28rpx;
			margin-top: 15rpx;
			color: #808080;
		}
	}
}
</style>

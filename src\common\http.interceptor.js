// 手机号码加星号
function setPhone(phone) {
  if (typeof phone === 'string' && phone.length === 11) {
    const startNum = phone.substring(0, 3)
    const endNum = phone.substring(7)
    return `${startNum}****${endNum}`
  } else {
    return phone
  }
}

// 返回数据检测字段，如果由电话号码则转变成星号的
function changePhone(dataDetail) {
  const phones = [ 'phone', 'emergencyPhone', 'contactsTelephone', 'contactsTelephonetwo', 'phonenumber', 'complainPhone', 'tel']
  if (Array.isArray(dataDetail)) {
    dataDetail.map(item => {
      phones.forEach(key => {
        if (item[key]) {
          item[key] = setPhone(item[key])
        }
      })
    })
  } else if (Object.prototype.toString.call(dataDetail) === '[object Object]') {
    Object.keys(dataDetail).forEach(key => {
      if (phones.includes(key) && dataDetail[key]) {
        dataDetail[key] = setPhone(dataDetail[key])
      }
    })
  }
}

// 检测数据，如果电话号码包含星号就移出该字段，不做提交
function removePhone(dataDetail) {
  const phones = [ 'phone', 'emergencyPhone', 'contactsTelephone', 'contactsTelephonetwo', 'phonenumber', 'complainPhone', 'tel']
  Object.keys(dataDetail).forEach(key => {
    if (phones.includes(key) && dataDetail[key]) {
      const phoneNum = `${dataDetail[key]}`
      if (phoneNum.includes('*')) delete dataDetail[key]
    }
  })
}

// 这里的vm，就是我们在vue文件里面的this，所以我们能在这里获取vuex的变量，比如存放在里面的token变量
const install = (Vue, vm) => {
	// 此为自定义配置参数，具体参数见上方说明
	let config = {
		showLoading: false, // 是否显示请求中的loading
		originalData: true,
		header: {
			'content-type': 'application/json;charset=UTF-8'
		},
		timeout: 30000
		// ......
	}
	// #ifndef H5
	console.log(vm.vuex_ip)
	config.baseUrl = vm.vuex_ip;
	// #endif
	Vue.prototype.$u.http.setConfig(config);

	// 请求拦截，配置Token等参数
	Vue.prototype.$u.http.interceptor.request = (config) => {

		// 可以对某个url进行特别处理，此url参数为this.$u.get(url)中的url值
		if(!config.url.includes('/zqzfj/login') && config.url !== '/zqzfj/user/register') {
			config.header.Authorization = vm.$store.state.vuex_token || vm.Cookies.get('YGF-MOBILE-Token');
		}
		if (config.data && Object.prototype.toString.call(config.data) === '[object Object]') {
			removePhone(config.data)
		}
		config.sslVerify = false
		// 最后需要将config进行return
		return config;
		// 如果return一个false值，则会取消本次请求
		// if(config.url == '/user/rest') return false; // 取消某次请求
	}

	// 响应拦截，判断状态码是否通过
	Vue.prototype.$u.http.interceptor.response = (res) => {
		console.log(res)
		if(res.statusCode == 200) {
			// res为服务端返回值，可能有code，result等字段
			// 这里对res.result进行返回，将会在this.$u.post(url).then(res => {})的then回调中的res的到
			// 如果配置了originalData为true，请留意这里的返回值
			if(res.data && res.data.code == 401) {
				vm.$u.toast('登录失效，请重新登录');
				setTimeout(() => {
					// 此为uView的方法，详见路由相关文档
					vm.$u.vuex('vuex_token', "");
					vm.$u.route({type:'reLaunch', url: '/pages/login/login' })
				}, 1500)
				return false;
			} else if (typeof res.data === 'string' || res.data.code !== 200) {
				/* uni.showModal({
					title: '提示',
					content: res.data.msg || '请求失败',
					showCancel: false
				}); */
				uni.showToast({
					title: res.data.msg || '请求失败',
					mask: true,
					// position: 'bottom',
					icon: 'none',
				})
				return false;
			}
			/* 遍历后端返回数据，有手机号码就加星号处理 */
			if (res.data.rows || res.data.data) {
				const dataDetail = res.data.data || res.data.rows
				changePhone(dataDetail)
			}
			return res.data;
		} else {
			uni.showToast({
				title: res.msg || '服务器或网络异常',
				mask: true,
				// position: 'bottom',
				icon: 'none',
			})
			/* uni.showModal({
				title: '提示',
				content: res.msg || '数据请求失败',
				showCancel: false
			}); */
			// 如果返回false，则会调用Promise的reject回调，
			// 并将进入this.$u.post(url).then().catch(res=>{})的catch回调中，res为服务端的返回值
			return false;
		}
	}
}

export default {
	install
}

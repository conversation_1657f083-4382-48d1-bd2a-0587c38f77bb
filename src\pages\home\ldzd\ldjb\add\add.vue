<template>
  <view>
    <u-form
      ref="uForm"
      :model="form"
      class="container u-border-top"
      label-width="200"
      :label-style="labelStyle"
    >
      <view class="p-lr-30">
        <u-form-item label="交办人:" prop="userName">
          <u-input
            v-model="form.userName"
            type="text"
            disabled
            placeholder="交办人"
          />
        </u-form-item>
        <u-form-item label="执行人:" prop="workUserName" required>
          <u-input
            v-model="form.workUserName"
            type="popup"
            placeholder="请选择执行人"
          />
          <u-button
            v-slot="right"
            size="mini"
            type="primary"
            @click="handleChooseHandleUser('userIds')"
          >
            选择人员
          </u-button>
        </u-form-item>
        <u-form-item label="规定完成时间:" prop="limitTime" required>
          <u-input
            v-model="form.limitTime"
            type="popup"
            placeholder="请选择规定完成时间"
          />
          <u-button
            v-slot="right"
            size="mini"
            type="primary"
            @click="showLimitTime = true"
          >
            选择时间
          </u-button>
        </u-form-item>
        <u-form-item
          label="交办内容:"
          prop="content"
          label-position="top"
          required
        >
          <u-input
            v-model="form.content"
            type="textarea"
            maxlength="500"
            height="140"
            placeholder="请输入交办内容..."
          />
        </u-form-item>
        <u-form-item
          label="备注:"
          :border-bottom="false"
          label-position="top"
          prop="remark"
        >
          <u-input
            v-model="form.remark"
            type="textarea"
            maxlength="300"
            height="100"
            placeholder="请输入备注..."
          />
        </u-form-item>
      </view>
      <!-- 间隔 -->
      <u-gap height="20" bg-color="#F5F5F5"></u-gap>
      <view class="p-lr-30">
        <view class="upload-section">
          <u-upload
            ref="happenfile"
            name="files"
            max-count="6"
            width="157"
            height="157"
            :header="header"
            :auto-upload="false"
            :action="action"
            :form-data="happenData"
            :size-type="['compressed']"
            :file-list="happenFile"
            :before-remove="handleRemove"
            @on-uploaded="handleAllUpload"
            @on-error="handleError"
          ></u-upload>
          <text>附件</text>
        </view>
      </view>
    </u-form>

    <!-- 提交按钮 -->
    <view class="btn-box u-border-top u-flex u-row-between">
      <view class="u-flex-1 u-m-r-20">
        <u-button
          class="u-flex-1 u-m-r-20"
          type="primary"
          shape="circle"
          :custom-style="subStyle"
          @click="handleSubmit(1)"
        >
          暂存
        </u-button>
      </view>
      <view class="u-flex-1">
        <u-button
          type="primary"
          shape="circle"
          :custom-style="subStyle"
          @click="handleSubmit(2)"
        >
          提交
        </u-button>
      </view>
    </view>

    <!-- 时间选择器 -->
    <u-picker
      v-model="showLimitTime"
      mode="time"
      :params="params"
      :default-time="form.limitTime"
      @confirm="handleLimitTimeConfirm"
      @cancel="showLimitTime = false"
    ></u-picker>

    <!-- 提示 -->
    <u-top-tips ref="uTips"></u-top-tips>
  </view>
</template>

<script>
export default {
  data() {
    return {
      params: {
        year: true,
        month: true,
        day: true,
        hour: true,
        minute: true,
        second: true,
      },
      labelStyle: {
        color: '#808080',
        fontSize: '30rpx',
      },
      subStyle: {
        height: '86rpx',
        backgroundColor: '#327BF0',
      },
      draftStyle: {
        height: '86rpx',
        backgroundColor: '#909399',
      },
      showLimitTime: false,
      form: {
        userName: '',
        userId: '',
        workUserName: '',
        workUserId: '',
        limitTime: '',
        content: '',
        remark: '',
        status: 0,
      },
      rules: {
        workUserName: [
          { required: true, message: '请选择执行人', trigger: 'change' },
        ],
        limitTime: [
          { required: true, message: '请选择规定完成时间', trigger: 'change' },
        ],
        content: [
          { required: true, message: '请输入交办内容', trigger: 'blur' },
        ],
      },
      happenData: {
        tableName: 'oa_leader_task',
        status: 1,
      },
      happenFile: [],
    }
  },
  computed: {
    action() {
      // #ifdef H5
      return `/prod-api/system/file/upload`
      // #endif
      // #ifndef H5
      return `${this.vuex_ip}/prod-api/system/file/upload`
      // #endif
    },
    header() {
      return {
        Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token'),
      }
    },
  },
  methods: {
    // 选择执行人员
    handleChooseHandleUser(type) {
      const params = {}
      if (this.form[type]) {
        params.defaultCheckedKeys = this.form[type]
        params.defaultExpandedKeys = this.form[type]
      }
      params.showRadio = 1 // 单选模式
      params.type = type
      this.$u.route({ url: 'pages/common/selectUser/selectUser', params })
    },

    // 选择人员回调
    setUserData(checks, type) {
      // 选择好人员后的回调
      let checkData = {}
      if (checks.length == 1) {
        checkData = checks[0]
      } else {
        checks.map((v, i) => {
          if (i == 0) {
            checkData.label = v.label + ''
            checkData.id = v.id + ''
          } else {
            checkData.label += ',' + v.label + ''
            checkData.id += ',' + v.id + ''
          }
        })
      }
      if (type == 'userIds') {
        if (checkData) {
          this.form.workUserName = checkData.label
          this.form.workUserId = checkData.id
        }
      }
    },

    // 确认时间选择
    handleLimitTimeConfirm(time) {
      this.form.limitTime = time
      this.showLimitTime = false
    },

    // 表单验证
    validateFn(isValid) {
      return new Promise((resolve, reject) => {
        if (isValid) {
          this.$refs.uForm.validate((valid) => {
            if (valid) {
              resolve()
            } else {
              reject()
            }
          })
        } else {
          resolve()
        }
      })
    },

    // 提交表单
    handleSubmit(status) {
      this.validateFn(status === 2)
        .then(() => {
          const params = Object.assign({}, this.form, { status: status })
          // 根据是否有 id 判断调用新增还是编辑接口
          const apiMethod = this.form.id
            ? this.$u.api.editLeaderTaskList
            : this.$u.api.addLeaderTask
          // 开始上传
          this.$loading('数据上传中')
          apiMethod(params)
            .then((res) => {
              // 检查是否有未上传的图片
              const uploadFile =
                this.$refs.happenfile.lists &&
                this.$refs.happenfile.lists.some(
                  (item) => item.progress !== 100
                )
              this.happenData.businessId = this.form.id || res.data.id
              if (uploadFile) {
                this.$loading('图片上传中')
                this.$refs.happenfile.upload()
              } else {
                uni.showToast({ title: '操作成功' })
                uni.hideLoading()
                this.$implement()
              }
            })
            .catch(() => {
              uni.hideLoading()
            })
        })
        .catch(() => {})
    },

    // 文件上传完成
    handleAllUpload() {
      uni.showToast({ title: '操作成功' })
      uni.hideLoading()
      this.$implement()
    },

    // 文件上传错误
    handleError() {
      uni.hideLoading()
      this.$refs.uTips.show({
        title: '图片上传失败',
        type: 'error',
        duration: '2300',
      })
    },

    // 删除文件
    handleRemove(index, lists) {
      const fileInfo = lists[index]
      const fileId = fileInfo.url.split('?id=')[1]
      if (fileId) {
        return new Promise((resolve, reject) => {
          uni.showModal({
            title: '提示',
            content: '删除后将无法恢复，是否确认删除？',
            success: ({ confirm }) => {
              if (confirm) {
                this.$u.api
                  .deleteFileList({}, fileId)
                  .then(resolve)
                  .catch(reject)
              } else {
                reject()
              }
            },
          })
        })
      } else {
        return true
      }
    },
  },
  onLoad(params) {
    // 获取业务数据
    if (params.id) {
      this.$loading()
      this.$u.api
        .getLeaderTask({}, params.id)
        .then((res) => {
          uni.hideLoading()
          this.form = { ...res.data }
          // 如果有附件,需要加载附件列表
          if (res.data.id) {
            this.happenData.businessId = res.data.id
          }
        })
        .catch(() => {
          uni.hideLoading()
        })
    } else {
      // 初始化表单数据
      this.form.userName = this.vuex_nickName
      this.form.userId = this.vuex_id

      // 设置默认完成时间为明天
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      const year = tomorrow.getFullYear()
      const month = String(tomorrow.getMonth() + 1).padStart(2, '0')
      const day = String(tomorrow.getDate()).padStart(2, '0')
      const hour = String(tomorrow.getHours()).padStart(2, '0')
      const minute = String(tomorrow.getMinutes()).padStart(2, '0')
      const second = String(tomorrow.getSeconds()).padStart(2, '0')
      this.form.limitTime = `${year}-${month}-${day} ${hour}:${minute}:${second}`
    }
  },
  onReady() {
    this.$refs.uForm.setRules(this.rules)
  },
}
</script>

<style lang="scss">
.p-lr-30 {
  padding: 0 30rpx;
  background-color: #fff;
}

.container {
  padding-bottom: 145rpx;
}

.btn-box {
  width: 100%;
  padding: 14rpx 30rpx;
  position: fixed;
  bottom: 0;
  background-color: #ffffff;
  z-index: 10;
}

.upload-section {
  padding: 20rpx 0;

  text {
    display: block;
    margin-top: 10rpx;
    color: #666;
    font-size: 28rpx;
  }
}
</style>

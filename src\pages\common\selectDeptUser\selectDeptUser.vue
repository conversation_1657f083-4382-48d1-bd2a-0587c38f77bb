<template>
	<view>
		<view class="content">
			<ly-tree 
				ref="tree"
				node-key="id"
				:ready="ready"
				:tree-data="treeData" 
				:checkOnClickNode="true"
				:showCheckbox="true"
				:defaultCheckedKeys="defaultCheckedKeys"
				:defaultExpandedKeys="defaultExpandedKeys"
				:isInjectParentInNode="true"
			>
			</ly-tree>
		</view>
		
		<!-- 确认按钮 -->
		<view class="btns">
			<u-button type="primary" shape="circle" :custom-style="subStyle" @click="submit" class="btn u-m-t-24">确认</u-button>
		</view>
	</view>
</template>

<script>
	import LyTree from '@/components/ly-tree/ly-tree.vue'
	export default {
		components: {
			LyTree
		},
		data() {
			return {
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				showRadio: true,
				treeData: [],
				type: '',
				ready: true,
				defaultCheckedKeys: [],
				defaultExpandedKeys: [],
				name:''
			}
		},
		
		onLoad(param = {}) {
			// @ type        若上层页面有多个人员选择，用以区分不同人员选择
			this.type = param.type || ''
			if (param.defaultCheckedKeys) {
				this.defaultCheckedKeys = param.defaultCheckedKeys.split(',').map(key => `${key}u`)
			}
			if (param.defaultExpandedKeys) {
				this.defaultExpandedKeys = param.defaultExpandedKeys.split(',').map(key => `${key}u`)
			}
			this.name = param.name
			this.fetchData(this.name);
		},
		methods: {
			treeMap(item) {
				if (!item) return false
			  const haveChildren = Array.isArray(item.children) && item.children.length > 0;
			  let tree = {
					id: `${item.id}${item.type}`,
					label: item.label,
					parentId: item.parentId,
					type: item.type
			  }
			  if (haveChildren) tree.children = item.children.map(i => this.treeMap(i));
			  return tree;
			},
			fetchData(name) {
				uni.showLoading({ title: '数据加载中...', mask: true })
				this.ready = false
				this.$u.api.deptUsertreeselect({ typeList: '0,3' }).then(res => {
					this.treeData = res.data.map(item => this.treeMap(item))
					
					this.ready = true
					if (this.$u.test.isEmpty(this.defaultExpandedKeys)) {
						this.defaultExpandedKeys = [res.data[0].id]
					}
					uni.stopPullDownRefresh()
					uni.hideLoading()
				}).catch(() => {
					this.ready = true
					uni.hideLoading()
					uni.stopPullDownRefresh()
				})	
			},
			submit() {
				let checkeds = this.$refs.tree.getCheckedNodes(false, true) || []
				checkeds = checkeds.map(item => {
					item.id = parseInt(item.id)
					return item
				})
				checkeds = checkeds.filter(item => !(item.type == 'd' && (item.id == 100 || item.id == 114 || item.id == 115)))
				
				let pages = getCurrentPages();             //获取所有页面栈实例列表
				let prevPage = pages[ pages.length - 2 ];  //上一页页面实例
				if (prevPage.$vm.setDeptUserData) {
					prevPage.$vm.setDeptUserData(checkeds, this.type)
				}
				uni.navigateBack();
			},
			onPullDownRefresh(){
				this.fetchData(this.name)
			}
		}
	}
</script>

<style>
	.checkbox-box {
		width: 100%;
	}
	.checkbox-item {
		padding: 24rpx;
		width: 100%;
	}
	.u-checkbox__label, .u-radio__label {
		width: 100%;
	}
	.content {
		position: absolute;
		bottom: 124rpx;
		top: 0;
		overflow-y: auto;
		width: 100%;
	}
	.btns {
		position: fixed;
		bottom: 0rpx;
		width: 100%;
		height: 124rpx;
		padding: 0 30rpx;
		z-index: 100;
	}
</style>

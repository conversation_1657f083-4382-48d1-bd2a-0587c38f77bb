<template>
	<view>
		<top-supervise :caseId="form.transportId" caseType="transport" :status="form.status"/>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="当事人:" prop="party" required>
					<u-input v-model="form.party" type="text" placeholder="请输入当事人姓名"  disabled />
				</u-form-item>
				<!-- <u-form-item label="身份证:" prop="identityCard">
					<u-input v-model="form.identityCard" type="idcard" placeholder="请输入当事人身份证号" disabled  />
				</u-form-item> -->
				<u-form-item label="联系电话:" prop="phone">
					<u-input v-model="form.phone" placeholder="请输入当事人联系电话" disabled />
				</u-form-item>
				<u-form-item label="车辆车牌:" prop="carNo" required>
					<u-input v-model="form.carNo" type="text" placeholder="请输入车辆车牌" disabled />
				</u-form-item>
				<u-form-item label="车辆品牌:" prop="models">
					<u-input v-model="form.models" type="text" placeholder="请输入车辆品牌" disabled />
				</u-form-item>
				<u-form-item label="营运公司:" prop="operationCompany">
					<u-input v-model="form.operationCompany" type="text" disabled placeholder="请输入营运公司" />
				</u-form-item>
				<u-form-item label="从业资格证号:" prop="certificateNo">
					<u-input v-model="form.certificateNo" type="text"  placeholder="请输入从业资格证号" disabled />
				</u-form-item>
				<u-form-item label="道路运输证号:" :border-bottom="false" prop="transportCertificate">
					<u-input v-model="form.transportCertificate" type="text"  placeholder="请输入道路运输证号" disabled />
				</u-form-item>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<!-- <u-form-item label="标题:" prop="title" required>
					<u-input v-model="form.title" placeholder="请输入标题" />
				</u-form-item> -->
				<u-form-item label="上报人员:" prop="userName" required>
					<u-input v-model="form.userName" type="text" disabled placeholder="上报人员" />
				</u-form-item>
				<u-form-item label="辅助人员:" prop="userNames" >
					<u-input v-model="form.userNames" type="popup" placeholder="辅助人员"/>
				</u-form-item>
				<u-form-item label="上报时间:" prop="inspectionTime" required>
					<u-input v-model="form.inspectionTime" type="text" disabled placeholder="请选择上报时间" />
				</u-form-item>
				<u-form-item label="车辆类型:" prop="carTypeName" required>
					<u-input v-model="form.carTypeName" disabled placeholder="请选择车辆类型" />
				</u-form-item>
				<u-form-item label="违规类型:" prop="inspectionTypeName" required>
					<span v-if="!inspectionTypeList.length" style="font-size: 30rpx; color: #c0c4cc">请选择违规类型</span>
					<u-checkbox-group wrap>
						<u-checkbox
							v-model="item.checked"
							disabled
							v-for="(item, index) in inspectionTypeList" :key="index"
							:name="item.dictValue"
						>{{item.dictLabel}}</u-checkbox>
					</u-checkbox-group>
				</u-form-item>
				<!-- <u-form-item label="违规类型:" prop="inspectionTypeName" required>
					<u-input v-model="form.inspectionTypeName" type="select" :select-open="showInspectionType" placeholder="请选择违规类型" @click="handleOpenIsType" />
					<u-action-sheet v-model="showInspectionType" :list="inspectionTypeList" @click="handleInspectionTypeClick"></u-action-sheet>
				</u-form-item> -->
				<!-- <u-form-item v-if="isShowFTaxiType" label="网络预约出租汽车驾驶员证:" prop="onlineCarDriver" required>
					<u-radio-group v-model="form.onlineCarDriver">
						<u-radio name="1">有</u-radio>
						<u-radio name="0">无</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item v-if="isShowSTaxiType" label="网络预约出租汽车运输证类型:" prop="onlineCarTarnsport" required>
					<u-radio-group v-model="form.onlineCarTarnsport">
						<u-radio name="1">有</u-radio>
						<u-radio name="0">无</u-radio>
					</u-radio-group>
				</u-form-item> -->
				<u-form-item label="发生地址:" prop="address" required>
					<u-input v-model="form.address" type="text" disabled placeholder="请选择地址" />
				</u-form-item>
				<u-form-item label="经纬度:" :border-bottom="false" prop="lnglat" required>
					<u-input v-model="form.lnglat" type="popup" disabled  placeholder="请选择地址" />
				</u-form-item>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="违规内容:" prop="content" label-position="top" :border-bottom="false">
					<u-input v-model="form.content" type="textarea" maxlength="300" height="140" placeholder="请输入违规内容..."  disabled />
				</u-form-item>
				<u-upload
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
				  :size-type="['compressed']"
					:file-list="happenFile"
					:deletable="false"
					:customBtn="true"
				></u-upload>
			</view>
			<!-- 违规文件部分 -->
			<view v-for="(item,idx) in exFile" :key="idx">
				<u-gap height="20" bg-color="#F5F5F5"></u-gap>
				<view class="p-lr-30">
					<u-form-item :label="item.label" prop="content" label-position="top" :border-bottom="false">
						<text v-if="!item.fileList.length">无</text>
						<u-upload
							:ref="`file${item.data.status}`"
							name="files"
							width="157"
							height="157"
							:auto-upload="false"
							:action="action"
							:form-data="item.data"
						  :size-type="['compressed']"
							:file-list="item.fileList"
							:deletable="false"
							:customBtn="true"
						></u-upload>
					</u-form-item>
				</view>
			</view>
		</u-form>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import topSupervise from '@/components/top-supervise.vue'
	export default {
		components: {
			topSupervise
		},
		data() {
			return {
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				showCarType: false,
				transportCarType: [],
				showInspectionType: false,
				inspectionTypeList: [],
				showInspectionTime: false,
				isShowFTaxiType: false,
				isShowSTaxiType: false,
				form: {address:"浙江省金华市婺城区城北街道"},
				happenData: {
					tableName: 'case_transport',
					status: 1
				},
				happenFile: [],
				exFile: []
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			}
		},
		methods: {
			setFileType(type) {
				if (type == 4) {
					/* 网约车，修改上传文件类型 */
					this.exFile = [
						{ key: 2, label: '现场笔录', data: { tableName: 'case_transport', status: 2 }, fileList: [] },
						{ key: 3, label: '乘客询问笔录', data: { tableName: 'case_transport', status: 3 }, fileList: [] },
						{ key: 4, label: '驾驶员询问笔录', data: { tableName: 'case_transport', status: 4 }, fileList: [] },
						{ key: 5, label: '调查通知书', data: { tableName: 'case_transport', status: 5 }, fileList: [] },
						{ key: 8, label: '查封通知书', data: { tableName: 'case_transport', status: 8 }, fileList: [] },
						// { key: 7, label: '图片证据', data: { tableName: 'case_transport', status: 7 }, fileList: [] },
					]
				} else if (type == 5) {
					/* 黑车，修改上传文件类型 */
					this.exFile = [
						{ key: 2, label: '现场笔录', data: { tableName: 'case_transport', status: 2 }, fileList: [] },
						{ key: 3, label: '乘客询问笔录', data: { tableName: 'case_transport', status: 3 }, fileList: [] },
						{ key: 4, label: '驾驶员询问笔录', data: { tableName: 'case_transport', status: 4 }, fileList: [] },
						{ key: 5, label: '调查通知书', data: { tableName: 'case_transport', status: 5 }, fileList: [] },
						{ key: 9, label: '行政强制措施现场笔录', data: { tableName: 'case_transport', status: 9 }, fileList: [] },
						{ key: 10, label: '扣押决定书', data: { tableName: 'case_transport', status: 10 }, fileList: [] },
						// { key: 7, label: '图片证据', data: { tableName: 'case_transport', status: 7 }, fileList: [] },
					]
				} else {
					/* 其余为默认 */
					this.exFile = [
						{ key: 2, label: '现场笔录', data: { tableName: 'case_transport', status: 2 }, fileList: [] },
						{ key: 3, label: '乘客询问笔录', data: { tableName: 'case_transport', status: 3 }, fileList: [] },
						{ key: 4, label: '驾驶员询问笔录', data: { tableName: 'case_transport', status: 4 }, fileList: [] },
						{ key: 5, label: '调查通知书', data: { tableName: 'case_transport', status: 5 }, fileList: [] },
						// { key: 6, label: '先行登记保存证据通知书', data: { tableName: 'case_transport', status: 6 }, fileList: [] },
						// { key: 7, label: '图片证据', data: { tableName: 'case_transport', status: 7 }, fileList: [] },
					]
				}
			}
		},
		async onLoad(params) {
			// 获取车辆类型
			this.$loading('获取系统配置数据')
			await this.$u.api.getDicts({}, 'transport_car_type').then(res => {
				this.transportCarType = res.data.map(item => {
					item.text = item.dictLabel
					return item
				})
				uni.hideLoading()
			}).catch(() => {
				uni.hideLoading()
			})
			// 获取业务数据
			if (params.id) {
				this.$loading()
				Promise.all([
					this.$u.api.getTransport({}, params.id),
					this.$u.api.getFileList({ tableName: 'case_transport', businessId: params.id }),
					this.$u.api.getDicts({}, 'transport_car_type')
				]).then(resAry => {
					const formData = resAry[0].data
					const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
					this.form = { ...formData, lnglat }
					// 设置文件分类
					this.setFileType(this.form.carType)
					resAry[1].rows.map(item => {
						const obj = { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
						if (item.status == 1) {
							this.happenFile.push(obj)
						} else {
							this.exFile.forEach(file => {
								if (file.data.status == item.status) {
									file.fileList.push(obj)
								}
							})
						}
					})
					// 设置违规类型
					let remark = ''
					this.transportCarType = resAry[2].data.map(item => {
						item.text = item.dictLabel
						if (item.dictValue == resAry[0].data.carType) {
							remark = item.remark
						}
						return item
					})
					// 获取巡查类型
					if (remark) {
						this.$u.api.getDicts({}, remark).then(dictRes => {
							this.inspectionTypeList = dictRes.data.map(item => {
								item.checked = this.form.inspectionType.includes(item.dictValue)
								return item
							})
							uni.hideLoading()
						}).catch(() => {
							uni.hideLoading()
						})
					} else {
						uni.hideLoading()
					}
					// uni.hideLoading()
				}).catch(() => {
					uni.hideLoading()
				})
			} else {
				const timestamp = new Date().getTime()
				const inspectionTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = { inspectionTime, userName: this.vuex_username, userId: this.vuex_id }
			}
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

<template>
	<view>
		<!-- 搜索 -->
		<view class="top-search u-flex u-col-center u-border-top ">
			<view class="top-search-left u-flex u-flex-1">
				<u-image src="@/static/img/top-search.png" width="35rpx" height="35rpx"></u-image>
				<u-input v-model="searchValue" type="text" class="u-flex-1 u-m-l-20"></u-input>
			</view>
			<view class="top-search-right">
				<view class="top-search-right-btn u-flex u-row-center u-col-center" @click="handleSearch">
					<u-image src="@/static/img/filter.png" width="28rpx" height="28rpx" class="img"></u-image>
					<text>筛选</text>
				</view>
			</view>
		</view>
		
		<!-- 列表 -->
		<view class="container">
			<view class="list">
				<view class="list-item u-flex u-col-top u-row-between" v-for="(item, idx) in dataList" :key="idx" @click="handleOpen(item.id)">
					<u-image class="img" src="@/static/img/list-icon.png" width="60rpx" height="60rpx"></u-image>
					<view class="list-item-content u-flex u-flex-col u-flex-1 u-col-top">
						<text class="title u-line-1">{{ item.plateNo }}</text>
						<text class="text u-line-1">车辆品牌: {{ item.brand }}</text>
						<text class="text u-line-1">车辆车型: {{ item.taxiModel }}</text>
						<!-- <text class="text u-line-1">车牌号: {{ item.carNo }}</text> -->
						<text class="text u-line-1">车主姓名: {{ item.driver }}</text>
						<text class="text u-line-1">设备编号: {{ item.deviceIndexCode }}</text>
					</view>
				</view>
				<u-loadmore :status="status" class="u-m-t-20" />
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				pageNum: 1,
				pageSize: 10,
				searchValue: '',
				status: 'loadmore',
				dataList:[],
			}
		},
		methods: {
			fetchData() {
				const { pageNum, pageSize, searchValue } = this
				let params = { pageNum, pageSize }
				if (searchValue) params.searchValue = searchValue
				this.status = 'loading'
				this.$u.api.getTaxiList(params).then(res => {
					if (pageNum == 1) {
						this.dataList = res.rows
						uni.stopPullDownRefresh()
					} else {
						this.dataList = this.dataList.concat(res.rows)
					}
					this.status = res.rows.length < 10 ? 'nomore' : 'loadmore'
				}).catch(err => {
					this.status = 'loadmore'
					uni.stopPullDownRefresh()
				})
			},
			handleOpen(id) {
				this.$u.route({ url: 'pages/home/<USER>/taix/detail/detail', params: { id } })
			},
			handleSearch() {
				this.pageNum = 1
				this.fetchData()
			},
		},
		onLoad() {
			this.fetchData()
		},
		onPullDownRefresh() {
			this.pageNum = 1
			this.fetchData()
		},
		onReachBottom() {
			if (this.status == 'loadmore') {
				this.pageNum++
				this.fetchData()
			}
		}
	}
</script>

<style lang="scss">
	.container{
		padding-top: 123rpx;
	}
	.list {
		padding-bottom: 209rpx;
		.list-item {
			margin: 20rpx 30rpx 0;
			background-color: #FFFFFF;
			border-radius: 12rpx;
			box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1);
			padding: 20rpx 20rpx 30rpx;
			.img {
				flex-shrink: 0;
				margin-right: 20rpx;
			}
			&-content {
				width: 360rpx;
				color: #808080;
				line-height: 32rpx;
				font-size: 24rpx;
				.title {
					width: 100%;
					line-height: 60rpx;
					font-weight: 700;
					font-size: 34rpx;
					color: #333333;
				}
				.text {
					width: 100%;
				}
			}
			&-state {
				width: 180rpx;
				flex-shrink: 0;
				font-size: 28rpx;
				font-weight: 700;
				line-height: 60rpx;
				margin-left: 20rpx;
				.circle {
					width: 16rpx;
					height: 16rpx;
					background-color: #EC5656;
					border-radius: 50%;
					margin-right: 10rpx;
				}
			}
		}
	}
.top-search {
	width: 100%;
	height: 103rpx;
	background-color: #fff;
	padding: 0 30rpx;
	// box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1);
	position: fixed;
	top: 0;
	/* #ifdef H5 */
	top: 44px;
	/* #endif */
	z-index: 10;
	&-left {
		height: 68rpx;
		background-color: #F5F5F5;
		border-radius: 68rpx;
		padding: 0 20rpx;
		margin-right: 20rpx;
	}
	&-right {
		&-btn {
			width: 136rpx;
			height: 68rpx;
			border-radius: 68rpx;
			background-color: #327BF0;
			color: #fff;
			font-size: 28rpx;
			transition: all 0.5s;
			.img {
				margin-right: 10rpx;
			}
			&:active {
				opacity: 0.3;
			}
		}
	}
}
</style>

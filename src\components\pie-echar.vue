<template>
	<view :id="id" :ref = "id" :style="styles">
		
	</view>
</template>

<script>
	var myChart = null
export default {
	name:"pieEchar",
	props:{
		styles:{
			type: Object,
			// default: {},
		},
		id:{
			type: String,
			// default: {},
		},
	},
	data(){
		return{
			
		}
	},
	mounted() {
		
	},
	methods:{
		charts(){
			myChart.setOption( {
			    tooltip: {
			        trigger: 'item',
			    },
			    legend: {
			        bottom: '5%',
			        left: 'center',
			    },
			      graphic:{      //图形中间文字
			          type:"text",
			          left:"center",
			          top:"30%",
			          style:{
			            text:"拒付分析",
			            textAlign:"center",
			            fill:"#ffffff",
			            fontSize: 12
			          }
			        },
			    series: [
					
					{
					  type: 'pie',
					  radius: '50%',
					  center: ['50%', '50%'],
					  selectedMode: false,
					  hoverAnimation: false,
					  data: [{ value: 1, name: '' }],
					  itemStyle: {
					    color: '#7f000000'
					  },
					  label: {
					    show: false
					  },
					  labelLine: {
					    show: false
					  },
					  tooltip: {
					    show: false
					  },
					  animation: false,
					  cursor: 'auto',
					  emphasis: {
					    itemStyle: {
					      color: '#f7f7f7'
					    }
					  }
					}
			        ,{
			            name: '访问来源',
			            type: 'pie',
			            radius: ['40%', '60%'],
			            emphasis: {
			                itemStyle: {
			                    shadowBlur: 10,
			                    shadowOffsetX: 0,
			                    shadowColor: 'rgba(0, 0, 0, 0.5)'
			                },
			                label: {
			                    show: true,
			                    fontSize: '40',
			                    fontWeight: 'bold'
			                }
			            },
						// itemStyle: {
						// 	borderColor: '#192250',
						// 	borderWidth: 2
						// },
						backgroundColor: '#1b1b1b',//背景色
						color: ['#F9536F','#29E2FA', '#2475FE', '#FF992A', '#FFCD00','#F9536F',   '#bda29a','#6e7074', '#546570', '#c4ccd3'],
			            data: [
			                {value: 1048, name: '搜索引擎'},
			                {value: 735, name: '直接访问'},
			                {value: 580, name: '邮件营销'},
			                {value: 484, name: '联盟广告'},
			                {value: 300, name: '视频广告'}
			            ],
			        },
					
			    ],
				 media: [
				            {
				                query: {
				                    maxWidth: 500
				                },
				                option: {
				                    tooltip: {
				                        trigger: 'item',
				                    },			
				                    legend: {
				                        bottom: '5%',
				                        left: 'center',
										textStyle:{
										    fontSize: 12,//字体大小
											color: ['#F9536F','#29E2FA', '#2475FE', '#FF992A', '#FFCD00','#F9536F',   '#bda29a','#6e7074', '#546570', '#c4ccd3'],
										    // color: '#DFE4FF'//字体颜色
										 },
				                    },
									label:{
									 color: '#ffffff',//字体颜色
									},
				                    series: [
										{
										    radius: [0, '60%'],
										    center: ['50%', '30%']
										},
				                        {
				                            radius: [40, '50%'],
				                            center: ['50%', '30%']
				                        },
				                        
				                    ]
				                }
				            }
				        ]
				}
			);
		}
	}
}
</script>

<style>
</style>

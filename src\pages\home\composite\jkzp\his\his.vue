<template>
	<view>
		<top-supervise :caseId="form.captureId" caseType="capture" :status="form.status"/>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="150" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="发起人:" prop="userName">
					<u-input disabled v-model="form.userName" placeholder="发起人" />
				</u-form-item>
				<u-form-item label="发生时间:" prop="happenTime">
					<u-input disabled v-model="form.happenTime" placeholder="请选择发生时间" />
				</u-form-item>
				<u-form-item label="案件类型:" prop="caseTypeName">
					<u-input disabled v-model="form.caseTypeName" placeholder="请选择案件类型"  />
				</u-form-item>
				<u-form-item label="案件内容:" label-position="top">
					<u-input disabled v-model="form.content" type="textarea" :isShowNum="false" placeholder="请输入案件内容"  />
				</u-form-item>
				<u-form-item label="发生地址:" prop="address" label-position="top" :border-bottom="false">
					<u-input disabled v-model="form.address"  type="textarea" :isShowNum="false" placeholder="请选择地址" />
				</u-form-item>
			</view>
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="内容描述:" prop="content" label-position="top" :border-bottom="false" required>
					<u-input disabled v-model="form.content" type="textarea" maxlength="300" height="140" placeholder="请输入内容描述..."/>
				</u-form-item>
				<u-upload
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:auto-upload="false"
					:action="action"
					:file-list="happenFile"
					:deletable="false"
					:customBtn="true"
				></u-upload>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="当班组长:" prop="squadronUserName">
					<u-input v-model="form.squadronUserName" type="popup" placeholder="当班组长"/>
				</u-form-item>
				<view v-if="form.status >= 3">
					<u-form-item label="执行人员:" prop="userNames" :border-bottom="false" >
						<u-input disabled v-model="form.userNames" type="popup" placeholder="执行人员"/>
					</u-form-item>
				</view>
			</view>
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30" v-if="form.status >= 5">
				<u-form-item label="出警时间:" prop="outTime">
					<u-input v-model="form.outTime" type="popup" placeholder="出警时间" />
				</u-form-item>
				<u-form-item label="现场情况:" prop="doDescn" label-position="top" :border-bottom="false">
					<u-input disabled v-model="form.doDescn" type="textarea" maxlength="300" height="140" placeholder="请输入现场情况..."/>
				</u-form-item>
				<u-upload
					ref="descnFile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:auto-upload="false"
					:action="action"
					:size-type="['compressed']"
					:file-list="descnFile"
					:customBtn="true"
					:deletable="false"
				></u-upload>
			</view>
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30" v-if="form.status >= 6">
				<u-form-item label="到达时间:" prop="arriveTime" >
					<u-input v-model="form.arriveTime" type="popup" placeholder="到达时间" />
				</u-form-item>
				<u-form-item label="办结时间:" prop="fbTime" >
					<u-input v-model="form.fbTime" type="popup" placeholder="办结时间" />
				</u-form-item>
				<u-form-item label="处警结果:" prop="doResult" label-position="top" :border-bottom="false" >
					<u-input disabled v-model="form.doResult" type="textarea" maxlength="300" height="140" placeholder="请输入出警结果..."/>
				</u-form-item>
				<u-upload
					ref="resultFile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:auto-upload="false"
					:action="action"
					:size-type="['compressed']"
					:file-list="resultFile"
					:customBtn="true"
					:deletable="false"
				></u-upload>
			</view>
			<!-- 退回原因 -->
			<u-gap v-if="form.approveReason" height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item v-if="form.approveReason" label="退回原因:" prop="approveReason" label-position="top" :border-bottom="false">
					<u-input v-model="form.approveReason" type="textarea" disabled maxlength="300" height="140" placeholder="退回原因"/>
				</u-form-item>
			</view>
		</u-form>
	</view>
</template>

<script>
	import topSupervise from '@/components/top-supervise.vue'
	export default {
		components: {
			topSupervise
		},
		data() {
			return {
				form:{type:1,squadronName:'违停',address:"浙江省金华市婺城区城北街道"},
				labelStyle: {color: '#808080',fontSize: '30rpx'},
				subStyle: {height: '86rpx',backgroundColor: '#327BF0'},
				caseTypeList:[],
				happenFile: [],
				descnFile: [],
				resultFile: [],
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			}
		},
		onLoad(params) {
			this.$loading()
			Promise.all([
				this.$u.api.dictList({dictType:'case_capture_type'}),
				this.$u.api.getcapture({},params.id),
				this.$u.api.getFileList({ tableName: 'case_capture', businessId: params.id })
			]).then(res=>{
				uni.hideLoading()
				// 数据字典数据
				this.caseTypeList = res[0].rows
				this.caseTypeList.forEach(v=>{
					if(v.dictLabel) v.text = v.dictLabel
					if(v.dictSort) v.type = v.dictSort
				})
				// 表单数据
				let formData = res[1].data
				const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
				this.form = {...this.form,...formData,lnglat}
				// 图片数据
				res[2].rows.map(v=>{
					const url =  { url: `${this.vuex_ip}${v.filePath}?id=${v.fileId}` }
					if (v.status == 2) {
						this.happenFile.push(url)
					} else if (v.status == 6) {
						this.descnFile.push(url)
					} else if (v.status == 9) {
						this.resultFile.push(url)
					}
				})
			}).catch(() => {
				uni.hideLoading()
			})
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

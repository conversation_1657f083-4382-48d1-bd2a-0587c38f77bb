{
  "easycom": {
    "^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
  },
  "pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/home/<USER>/index",
      "style": {
        "app-plus": {
          "titleNView": {
            "titleText": "首页",
            "type": "transparent",
            "backgroundColor": "#5792f2"
          }
        },
        "navigationBarTextStyle": "white",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/login/login",
      "style": {
        "app-plus": {
          "titleNView": false
        },
        "navigationBarTextStyle": "white"
      }
    }, {
      "path": "pages/message/index/index",
      "style": {
        "app-plus": {
          "titleNView": false
        },
        "enablePullDownRefresh": true
      }
    }, {
      "path": "pages/my/index/index",
      "style": {
        "app-plus": {
          "titleNView": false
        },
        "enablePullDownRefresh": true
      }
    }, {
      "path": "pages/home/<USER>/swyt/list/list",
      "style": {
        "navigationBarTitleText": "四位一体",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/swyt/add/add",
      "style": {
        "navigationBarTitleText": "新增四位一体",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/common/selectUser/selectUser",
      "style": {
        "navigationBarTitleText": "选择",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/swyt/detail/detail",
      "style": {
        "navigationBarTitleText": "四位一体详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/swyt/his/his",
      "style": {
        "navigationBarTitleText": "四位一体详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/xcfx/list/list",
      "style": {
        "navigationBarTitleText": "巡查发现列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/xcfx/add/add",
      "style": {
        "navigationBarTitleText": "新增店铺巡查",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/xcfx/his/his",
      "style": {
        "navigationBarTitleText": "店铺巡查详情",
        "enablePullDownRefresh": false
      }

    },
    {
      "path": "pages/home/<USER>/xcfxrcxc/list/list",
      "style": {
        "navigationBarTitleText": "日常巡查列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/xcfxrcxc/add/add",
      "style": {
        "navigationBarTitleText": "新增日常巡查",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/xcfxrcxc/his/his",
      "style": {
        "navigationBarTitleText": "日常巡查详情",
        "enablePullDownRefresh": false
      }

    },
    {
      "path": "pages/home/<USER>/wtcp/list/list",
      "style": {
        "navigationBarTitleText": "违停抄牌列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/wtcp/add/add",
      "style": {
        "navigationBarTitleText": "新增违停抄牌",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/wtcp/his/his",
      "style": {
        "navigationBarTitleText": "违停抄牌详情",
        "enablePullDownRefresh": false
      }

    },
    {
      "path": "pages/home/<USER>/rcxc/list/list",
      "style": {
        "navigationBarTitleText": "日常巡查列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/rcxc/add/add",
      "style": {
        "navigationBarTitleText": "新增日常巡查",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/rcxc/detail/detail",
      "style": {
        "navigationBarTitleText": "日常巡查详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/rcxc/his/his",
      "style": {
        "navigationBarTitleText": "日常巡查详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/wgcz/list/list",
      "style": {
        "navigationBarTitleText": "违规处置",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/wgcz/add/add",
      "style": {
        "navigationBarTitleText": "新增违规处置",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/wgcz/detail/detail",
      "style": {
        "navigationBarTitleText": "违规处置详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/wgcz/his/his",
      "style": {
        "navigationBarTitleText": "违规处置详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/jycx/list/list",
      "style": {
        "navigationBarTitleText": "案件列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/jycx/add/add",
      "style": {
        "navigationBarTitleText": "新增简易案件",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/jycx/his/his",
      "style": {
        "navigationBarTitleText": "简易案件详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/jkzp/list/list",
      "style": {
        "navigationBarTitleText": "监控抓拍列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/jkzp/add/add",
      "style": {
        "navigationBarTitleText": "监控抓拍",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/hnxx/list/list",
      "style": {
        "navigationBarTitleText": "黄牛列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/hnxx/add/add",
      "style": {
        "navigationBarTitleText": "新增黄牛信息",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/hnxx/detail/detail",
      "style": {
        "navigationBarTitleText": "黄牛信息详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/hncz/add/add",
      "style": {
        "navigationBarTitleText": "新增黄牛处置",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/hncz/detail/detail",
      "style": {
        "navigationBarTitleText": "修改黄牛处置",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/hncz/his/his",
      "style": {
        "navigationBarTitleText": "黄牛处置详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/rwfb/list/list",
      "style": {
        "navigationBarTitleText": "任务列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/rwfb/add/add",
      "style": {
        "navigationBarTitleText": "新增任务",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/rwfb/detail/detail",
      "style": {
        "navigationBarTitleText": "任务详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/rwfb/his/his",
      "style": {
        "navigationBarTitleText": "任务详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/xcfx/detail/detail",
      "style": {
        "navigationBarTitleText": "修改店铺巡查",
        "enablePullDownRefresh": false
      }

    },
    {
      "path": "pages/home/<USER>/xcfxrcxc/detail/detail",
      "style": {
        "navigationBarTitleText": "修改日常巡查",
        "enablePullDownRefresh": false
      }

    },
    {
      "path": "pages/home/<USER>/wtcp/detail/detail",
      "style": {
        "navigationBarTitleText": "修改违停抄牌",
        "enablePullDownRefresh": false
      }

    },
    {
      "path": "pages/home/<USER>/jycx/detail/detail",
      "style": {
        "navigationBarTitleText": "修改简易处罚",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/hncz/list/list",
      "style": {
        "navigationBarTitleText": "黄牛处置列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/jkzp/detail/detail",
      "style": {
        "navigationBarTitleText": "监控抓拍详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/zfdc/detail/detail",
      "style": {
        "navigationBarTitleText": "任务详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/zfdc/list/list",
      "style": {
        "navigationBarTitleText": "执法列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/dzzp/list/list",
      "style": {
        "navigationBarTitleText": "电子抓拍列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/dzzp/his/his",
      "style": {
        "navigationBarTitleText": "案件详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/dpxx/list/list",
      "style": {
        "navigationBarTitleText": "店铺列表",
        "enablePullDownRefresh": true
      }

    }, {
      "path": "pages/home/<USER>/dpxx/add/add",
      "style": {
        "navigationBarTitleText": "新增店铺",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/dpxx/detail/detail",
      "style": {
        "navigationBarTitleText": "店铺详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/in/in",
      "style": {
        "navigationBarTitleText": "考勤打卡",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/out/out",
      "style": {
        "navigationBarTitleText": "考勤打卡",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/base/index/index",
      "style": {
        "app-plus": {
          "titleNView": {
            "titleText": "知识库",
            "type": "transparent",
            "backgroundColor": "#5792f2"
          }
        },
        "navigationBarTextStyle": "white",
        "enablePullDownRefresh": true
      }

    }, {
      "path": "pages/base/zsk/flfg/list/list",
      "style": {
        "navigationBarTitleText": "法律法规列表",
        "enablePullDownRefresh": true
      }

    }, {
      "path": "pages/base/zsk/flfg/detail/detail",
      "style": {
        "navigationBarTitleText": "法律法规详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/base/zsk/gzzd/list/list",
      "style": {
        "navigationBarTitleText": "规章制度列表",
        "enablePullDownRefresh": true
      }

    }, {
      "path": "pages/base/zsk/gzzd/detail/detail",
      "style": {
        "navigationBarTitleText": "规章制度详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/ldsjc/list/list",
      "style": {
        "navigationBarTitleText": "领导数据舱",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "uview-ui/components/u-avatar-cropper/u-avatar-cropper",
      "style": {
        "navigationBarTitleText": "头像裁剪",
        "navigationBarBackgroundColor": "#000000"
      }
    }, {
      "path": "pages/my/perCenter/perCenter",
      "style": {
        "navigationBarTitleText": "个人中心",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/my/changePsd/changePsd",
      "style": {
        "navigationBarTitleText": "修改密码",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/jkzp/his/his",
      "style": {
        "navigationBarTitleText": "监控抓拍",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/xkzl/list/list",
      "style": {
        "navigationBarTitleText": "许可总览",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/xkzl/his/his",
      "style": {
        "navigationBarTitleText": "许可总览详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/lsrw/list/list",
      "style": {
        "navigationBarTitleText": "店铺巡查列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/lsrw/add/add",
      "style": {
        "navigationBarTitleText": "新增店铺巡查",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/lsrw/detail/detail",
      "style": {
        "navigationBarTitleText": "店铺巡查详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/lsrw/his/his",
      "style": {
        "navigationBarTitleText": "店铺巡查详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/map/map",
      "style": {
        "navigationBarTitleText": "所在位置",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/rydw/rydw/rydw",
      "style": {
        "navigationBarTitleText": "人员定位",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/base/study/rcdt/list/list",
      "style": {
        "navigationBarTitleText": "日常答题",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/base/study/rcdt/detail/detail",
      "style": {
        "navigationBarTitleText": "答题",
        "enablePullDownRefresh": false,
        "app-plus": {
          "titleNView": {
            "buttons": [{
              "type": "menu"
            }]
          }
        }
      }

    }, {
      "path": "pages/base/study/rcdt/answer/answer",
      "style": {
        "navigationBarTitleText": "答题报告",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/base/study/rcdt/his/his",
      "style": {
        "navigationBarTitleText": "题目解析",
        "enablePullDownRefresh": false,
        "app-plus": {
          "titleNView": {
            "buttons": [{
              "type": "menu"
            }]
          }
        }

      }

    }, {
      "path": "pages/home/<USER>/xcfx/getIns/getIns",
      "style": {
        "navigationBarTitleText": "任务详情",
        "enablePullDownRefresh": false
      }

    },

    {
      "path": "pages/base/study/cjwd/qaList/qaList",
      "style": {
        "navigationBarTitleText": "常见问答",
        "enablePullDownRefresh": true
      }

    }, {
      "path": "pages/base/dxal/dxal",
      "style": {
        "navigationBarTitleText": "典型案例详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/my/myWords/myWords",
      "style": {
        "navigationBarTitleText": "惯用语设置",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/taix/list/list",
      "style": {
        "navigationBarTitleText": "出租车列表",
        "enablePullDownRefresh": true
      }

    }, {
      "path": "pages/home/<USER>/taix/detail/detail",
      "style": {
        "navigationBarTitleText": "出租车详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/ajfb/ajfb/ajfb",
      "style": {
        "navigationBarTitleText": "案卷分布",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/jkm/jkm",
      "style": {
        "navigationBarTitleText": "缴款码",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/ldjb/list/list",
      "style": {
        "navigationBarTitleText": "案件交办列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/ldjb/add/add",
      "style": {
        "navigationBarTitleText": "新增领导交办",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/ldjb/detail/detail",
      "style": {
        "navigationBarTitleText": "领导交办详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/ajcx/list/list",
      "style": {
        "navigationBarTitleText": "案卷查询列表",
        "enablePullDownRefresh": true
      }

    }, {
      "path": "pages/my/myWorkReport/myWorkReport",
      "style": {
        "navigationBarTitleText": "工作统计",
        "enablePullDownRefresh": false,
        "pageOrientation": "landscape"
      }

    }, {
      "path": "pages/home/<USER>/lddb/list/list",
      "style": {
        "navigationBarTitleText": "督办案件",
        "enablePullDownRefresh": true
      }

    }, {
      "path": "pages/base/study/czjl/list/list",
      "style": {
        "app-plus": {
          "titleNView": {
            "backgroundColor": "rgba(0,0,0,0)",
            "type": "float"
          }
        },
        "navigationBarTextStyle": "white"
      }

    }, {
      "path": "pages/my/notice/list/list",
      "style": {
        "navigationBarTitleText": "通知公告",
        "enablePullDownRefresh": true
      }

    }, {
      "path": "pages/my/notice/detail/detail",
      "style": {
        "navigationBarTitleText": "详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/outMap/outMap",
      "style": {
        "navigationBarTitleText": "巡查",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/common/selectDeptUser/selectDeptUser",
      "style": {
        "navigationBarTitleText": "选择",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/dckh/list/list",
      "style": {
        "navigationBarTitleText": "督查考核列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/dbsp/list/list",
      "style": {
        "navigationBarTitleText": "代班审批列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/dckh/add/add",
      "style": {
        "navigationBarTitleText": "新增监督考核",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/common/selectStandard/selectStandard",
      "style": {
        "navigationBarTitleText": "选择考核标准",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/dckh/detail/detail",
      "style": {
        "navigationBarTitleText": "修改监督考核",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/dckh/his/his",
      "style": {
        "navigationBarTitleText": "申述详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/dbsp/detail/detail",
      "style": {
        "navigationBarTitleText": "代班审批",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/dbsp/his/his",
      "style": {
        "navigationBarTitleText": "代班审批详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/dckh/RepresentationsList/index",
      "style": {
        "navigationBarTitleText": "申诉列表",
        "enablePullDownRefresh": false
      }

    },{
      "path": "pages/home/<USER>/dckh/appeal/appeal",
      "style": {
        "navigationBarTitleText": "申诉详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/my/workLog/list/list",
      "style": {
        "navigationBarTitleText": "工作日志",
        "enablePullDownRefresh": true
      }

    }, {
      "path": "pages/my/workLog/detail/detail",
      "style": {
        "navigationBarTitleText": "工作日志详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/znzp/add/add",
      "style": {
        "navigationBarTitleText": "智能抓拍详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/znzp/detail/detail",
      "style": {
        "navigationBarTitleText": "智能抓拍详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/znzp/his/his",
      "style": {
        "navigationBarTitleText": "智能抓拍详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/znzp/list/list",
      "style": {
        "navigationBarTitleText": "智能抓拍列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/wtcz/list/list",
      "style": {
        "navigationBarTitleText": "问题处置列表",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/gzdt/list/list",
      "style": {
        "navigationBarTitleText": "工作动态列表",
        "enablePullDownRefresh": true
      }

    }, {
      "path": "pages/home/<USER>/gzdt/add/add",
      "style": {
        "navigationBarTitleText": "新增动态",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/gzdt/detail/detail",
      "style": {
        "navigationBarTitleText": "动态详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/rxzf/list/list",
      "style": {
        "navigationBarTitleText": "柔性执法",
        "enablePullDownRefresh": true
      }

    }

  , {
      "path": "pages/home/<USER>/rxzf/add/add",
      "style": {
        "navigationBarTitleText": "新增教育劝导",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/rxzf/detail/detail",
      "style": {
        "navigationBarTitleText": "柔性执法详情页",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/rxzf/examAnswer/examAnswer",
      "style": {
        "navigationBarTitleText": "答题详情",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/home/<USER>/rxzf/examHis/examHis",
      "style": {
        "navigationBarTitleText": "题目解析",
        "enablePullDownRefresh": false,
        "app-plus": {
          "titleNView": {
            "buttons": [{
              "type": "menu"
            }]
          }
        }
      }

    }


  , {
      "path": "pages/home/<USER>/zbdwb/list/list",
      "style": {
        "navigationBarTitleText": "值班点位表",
        "enablePullDownRefresh": false,
        "app-plus": {
          "bounce":"none"
        }
      }
    }
  ,{
      "path" : "pages/home/<USER>/zbdwb/detail/detail",
      "style" :
      {
        "navigationBarTitleText": "值班点位表",
        "enablePullDownRefresh": false
      }

    }
  ,{
      "path" : "pages/home/<USER>/dzzp/add/add",
      "style" :
      {
        "navigationBarTitleText": "电子抓拍",
        "enablePullDownRefresh": false
      }

    }
  ,{
      "path" : "pages/home/<USER>/leave/list/list",
      "style" :
      {
        "navigationBarTitleText": "请假申请",
        "enablePullDownRefresh": true
      }

    }
  ,{
      "path" : "pages/home/<USER>/dbsq/list/list",
      "style" :
      {
        "navigationBarTitleText": "代班申请",
        "enablePullDownRefresh": true
      }

    }
  ,{
      "path" : "pages/home/<USER>/rxdwt/list/list",
      "style" :
      {
        "navigationBarTitleText": "人行道违停",
        "enablePullDownRefresh": true
      }

    }
  ,{
      "path" : "pages/home/<USER>/rxdwt/add/add",
      "style" :
      {
        "navigationBarTitleText": "新增人行道违停",
        "enablePullDownRefresh": false
      }

    }
  ,{
      "path" : "pages/home/<USER>/rxdwt/detail/detail",
      "style" :
      {
        "navigationBarTitleText": "人行道违停详情",
        "enablePullDownRefresh": false
      }

    }
  ,{
      "path" : "pages/home/<USER>/leave/add/add",
      "style" :
      {
        "navigationBarTitleText": "新增请假",
        "enablePullDownRefresh": false
      }

    }
  ,{
      "path" : "pages/home/<USER>/leave/detail/detail",
      "style" :
      {
        "navigationBarTitleText": "请假申请详情",
        "enablePullDownRefresh": false
      }

    }
  ,{
      "path" : "pages/home/<USER>/dbsq/add/add",
      "style" :
      {
        "navigationBarTitleText": "新增代班",
        "enablePullDownRefresh": false
      }

    }
  ,{
      "path" : "pages/home/<USER>/dbsq/detail/detail",
      "style" :
      {
        "navigationBarTitleText": "代班申请详情",
        "enablePullDownRefresh": false
      }

    }
  ,{
      "path" : "pages/home/<USER>/approve/list/list",
      "style" :
      {
        "navigationBarTitleText": "请假审批",
        "enablePullDownRefresh": true
      }

    }
  ,{
      "path" : "pages/home/<USER>/approve/detail/detail",
      "style" :
      {
        "navigationBarTitleText": "请假审批详情",
        "enablePullDownRefresh": false
      }

    }

  ,{
      "path" : "pages/home/<USER>/ybaj/add/add",
      "style" :
      {
        "navigationBarTitleText": "新增一般案件",
        "enablePullDownRefresh": false
      }

    }
  ,{
      "path" : "pages/home/<USER>/ybaj/detail/detail",
      "style" :
      {
        "navigationBarTitleText": "修改一般案件",
        "enablePullDownRefresh": false
      }

    }
  ,{
      "path" : "pages/home/<USER>/ybaj/his/his",
      "style" :
      {
        "navigationBarTitleText": "一般案件详情",
        "enablePullDownRefresh": false
      }

    }
  ,{
      "path" : "pages/home/<USER>/ybaj/list/list",
      "style" :
      {
        "navigationBarTitleText": "一般案件列表",
        "enablePullDownRefresh": false
      }

    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "联合执法管理应用",
    "navigationBarBackgroundColor": "#FFFFFF",
    "backgroundColor": "#F5F5F5",
    "usingComponents": {
      "ly-tree-node": "/components/ly-tree/ly-tree-node"
    }
  },
  "tabBar": {
    "color": "#96A3B0",
    "selectedColor": "#327BF0",
    "backgroundColor": "#FFFFFF",
    "borderStyle": "#EBEBEB",
    "fontSize": "12px",
    "iconWidth": "26px",
    "list": [{
      "pagePath": "pages/home/<USER>/index",
      "iconPath": "static/img/tabbar/home.png",
      "selectedIconPath": "static/img/tabbar/home_c.png",
      "text": "首页"
    },
      {
        "pagePath": "pages/base/index/index",
        "iconPath": "static/img/tabbar/base.png",
        "selectedIconPath": "static/img/tabbar/base_c.png",
        "text": "知识库"
      },
      {
        "pagePath": "pages/message/index/index",
        "iconPath": "static/img/tabbar/message.png",
        "selectedIconPath": "static/img/tabbar/message_c.png",
        "text": "消息"
      },
      {
        "pagePath": "pages/my/index/index",
        "iconPath": "static/img/tabbar/my.png",
        "selectedIconPath": "static/img/tabbar/my_c.png",
        "text": "我的"
      }
    ]
  }
}

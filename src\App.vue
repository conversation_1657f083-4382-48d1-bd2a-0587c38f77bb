<script>
import { AESEncrypt } from '@/common/aesutil.js'

export default {
  onLaunch: function() {
    this.addListener()
    console.log('App Launch start')
    // #ifdef APP-PLUS
    plus.screen.lockOrientation('portrait-primary')
    // #endif
    console.log('开始初始化...')
    // #ifdef APP-PLUS
    this.appPush()
    // #endif
    console.log('推送初始化完成')
    // this.autoLogin()
    // #ifdef APP-PLUS
    this.appUpdate()
    // #endif
    console.log('App Launch complete')
  },
  watch: {
    '$route.path': {
      handler(toPath, fromPath) {
        console.log('当前页面路由地址：' + toPath)
        console.log('上一个路由地址：' + fromPath)
        this.watchRouter()
      },
    },
  },
  onShow: function() {
    console.log('App Show')
  },
  onHide: function() {
    console.log('App Hide')
  },
  methods: {
    watchRouter() {
      setTimeout(() => {
        console.log('路由跳转')
        let canExitType = ''
        // if (this.$route.path == '/' || this.$route.path == '/login') {
        // canExitType = 'canExit'
        //   console.log('canExit')
        // } else {
        canExitType = 'canNotExit'
        //   console.log('canNotExit')
        // }
        window.parent.postMessage(
          {
            cmd: 'route_change',
            currentPage: this.$route.path,
            canExitType: canExitType,
          },
          '*'
        )
      }, 600) // 延迟去获得跳转后的页面路由
    },
    addListener() {
      window.addEventListener('message', (event) => {
        if (event.data.cmd == 'navi_back') {
          console.log('frontstation-route_path', this.$route.path)

          if (this.$route.path == '/') {
            window.location.href = '/ygfMobileJc'
          } else {
            history.back()
          }
        }
      })
    },
    appPush() {
      // #ifdef APP-VUE
      plus.push.setAutoNotification(false)
      plus.push.addEventListener('receive', (e) => {
        console.log(e)
        try {
          const data = JSON.parse(e.content)
          if (data.type == 'mark') {
            uni.$emit('changeMark')
          } else if (data.type == 'message') {
            uni.$emit('setMsg', data.data)
          } else if (data.type == 'notice') {
            uni.$emit('noticeMsg', data.data)
            plus.push.createMessage(data.data.messageContent)
          }
        } catch (e) {
          // TODO handle the exception
        }
      })
      // #endif
    },
    autoLogin() {
      if (this.vuex_username && this.vuex_password) {
        const params = {
          username: this.vuex_username,
          password: this.vuex_password,
        }
        // #ifndef H5
        const clientInfo = plus.push.getClientInfo()
        const systemInfo = uni.getSystemInfoSync()
        params.clientId = clientInfo.clientid
        params.phoneModel = systemInfo.model
        // #endif
        const paramsKey = AESEncrypt(JSON.stringify(params), 'faprwTsQM2S6k3p5')
        this.$u.api
          .login({ requestData: paramsKey })
          .then((res) => {
            this.$u.vuex('vuex_token', res.token)
            uni.reLaunch({
              url: '/pages/home/<USER>/index',
              success() {
                // #ifndef H5
                plus.navigator.closeSplashscreen()
                // #endif
              },
            })
          })
          .catch(() => {
            // #ifndef H5
            plus.navigator.closeSplashscreen()
            // #endif
          })
      } else {
        // #ifndef H5
        plus.navigator.closeSplashscreen()
        // #endif
      }
    },
    wgtUpdate(url) {
      console.log(url)
      uni.downloadFile({
        url: url,
        success: (downloadResult) => {
          console.log(downloadResult)
          if (downloadResult.statusCode === 200) {
            plus.runtime.install(
              downloadResult.tempFilePath,
              {
                force: false,
              },
              function() {
                uni.showModal({
                  title: '提示',
                  content: '更新成功',
                  showCancel: false,
                  success: () => {
                    plus.runtime.restart()
                  },
                })
              },
              function(e) {
                console.log(e)
                uni.showModal({
                  title: '提示',
                  showCancel: false,
                  content: '更新包安装失败，请重试',
                })
              }
            )
          } else {
            uni.showModal({
              title: '提示',
              showCancel: false,
              content: '更新包下载失败，请重试',
            })
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            showCancel: false,
            content: '更新包下载失败，请重试',
          })
        },
      })
    },
    getAppVersion() {
      return new Promise((resolve, reject) => {
        let url = '/zqzfj/business/app/version/1'
        // #ifndef H5
        url = `${this.vuex_ip}/zqzfj/business/app/version/1`
        // #endif
        uni.request({
          url,
          method: 'get',
          timeout: 10000,
          dataType: 'json',
          success: (res) => {
            resolve(res.data)
          },
          fail: () => {
            uni.hideLoading()
            this.mToase('服务器或网络异常，请联系管理员')
            reject(null)
          },
        })
      })
    },
    appUpdate() {
      plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
        const version = widgetInfo.version
        console.log('---------------------------------')
        console.log(version)
        this.$u.vuex('vuex_version', version)
        this.getAppVersion().then((appInfo) => {
          const seriesVersion = appInfo.data.version
          console.log(seriesVersion)
          if (!seriesVersion || version === seriesVersion) return
          if (version[0] < seriesVersion[0]) {
            // 整包大版本更新
            uni.showModal({
              // 提醒用户更新
              title: '更新提示',
              content: '有大版本需要更新，请更新',
              showCancel: false,
              success: (res) => {
                if (res.confirm && appInfo.data.packageUpdateUrl) {
                  this.wgtUpdate(appInfo.data.packageUpdateUrl)
                  // plus.runtime.openURL(appInfo.data.packageUpdateUrl);
                }
              },
            })
          } else if (version < seriesVersion) {
            uni.showModal({
              // 提醒用户更新
              title: '更新提示',
              content: '有版本需要更新，请更新!',
              showCancel: false,
              success: (res) => {
                if (res.confirm && appInfo.data.partUpdateUrl) {
                  this.wgtUpdate(appInfo.data.partUpdateUrl)
                }
              },
            })
          }
        })
      })
    },
    isHasPush() {
      // #ifdef APP-PLUS
      // 通知情况
      if (!NotificationManagerCompat) return
      var main = plus.android.runtimeMainActivity()
      var pkName = main.getPackageName()
      var uid = main.getApplicationInfo().plusGetAttribute('uid')
      var NotificationManagerCompat = plus.android.importClass(
        'android.support.v4.app.NotificationManagerCompat'
      )
      var areNotificationsEnabled =
        NotificationManagerCompat.from(main).areNotificationsEnabled()
      // 未开通‘允许通知’权限，则弹窗提醒开通，并点击确认后，跳转到系统设置页面进行设置
      if (!areNotificationsEnabled) {
        uni.showModal({
          title: '提示',
          content: '您还没有开启通知权限，无法接受到消息通知，是否前往设置？',
          success: ({ confirm }) => {
            if (confirm) {
              var Intent = plus.android.importClass('android.content.Intent')
              var Build = plus.android.importClass('android.os.Build')
              // android 8.0引导
              if (Build.VERSION.SDK_INT >= 26) {
                var intent = new Intent(
                  'android.settings.APP_NOTIFICATION_SETTINGS'
                )
                intent.putExtra('android.provider.extra.APP_PACKAGE', pkName)
              } else if (Build.VERSION.SDK_INT >= 21) {
                // android 5.0-7.0
                var intent = new Intent(
                  'android.settings.APP_NOTIFICATION_SETTINGS'
                )
                intent.putExtra('app_package', pkName)
                intent.putExtra('app_uid', uid)
              } else {
                // (<21)其他--跳转到该应用管理的详情页
                intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                var uri = Uri.fromParts(
                  'package',
                  mainActivity.getPackageName(),
                  null
                )
                intent.setData(uri)
              }
              // 跳转到该应用的系统通知设置页
              main.startActivity(intent)
            }
          },
        })
      }
      // #endif
    },
  },
}
</script>

<style lang="scss">
@import 'components/ly-tree/ly-iconfont.css';
/*每个页面公共css */
/* #ifndef APP-NVUE */
@import 'uview-ui/index.scss';

.status_bar {
  height: var(--status-bar-height);
  width: 100%;
}
page {
  background-color: #f5f5f5;
}
/* #endif */
</style>

<template>
	<view class="page">
		<!-- 搜索 -->
		<view class="top-search u-flex u-col-center u-border-top u-border-bottom">
			<view class="top-search-left u-flex u-flex-1">
				<u-image src="@/static/img/top-search.png" width="35rpx" height="35rpx"></u-image>
				<u-input v-model="searchValue" type="text" class="u-flex-1 u-m-l-20"></u-input>
			</view>
			<view class="top-search-right">
				<view class="top-search-right-btn u-flex u-row-center u-col-center" @click="handleSearch">
					<u-image src="@/static/img/filter.png" width="28rpx" height="28rpx" class="img"></u-image>
					<text>筛选</text>
				</view>
			</view>
		</view>
		<!-- 列表 -->
		<view class="container-list">
			<view class="list" v-if="dataList.length">
				<view class="list-item u-flex u-col-top u-row-between" v-for="(item, idx) in dataList" :key="idx" @click="handleOpen(item)">
					<u-image class="img" src="@/static/img/list-icon.png" width="60rpx" height="60rpx"></u-image>
					<view class="list-item-content u-flex u-flex-col u-flex-1 u-col-top">
						<text class="title u-line-1">{{ item.content }}</text>
						<text class="text u-line-1">案卷类型: {{ names[item.type] }}</text>
						<text class="text u-line-1">相关人员: {{ item.userName }}</text>
						<text class="text u-line-1">创建时间: {{ item.time }}</text>
					</view>
					<view class="list-item-state u-flex u-col-center u-row-right">
						<view class="circle" :style="{ backgroundColor: item.status == 9 ? '#bdc3bf' : '#FAB71C' }"></view>
						<text>{{ item.status == 9 ? '已办结' : '处理中' }}</text>
					</view>
				</view>
				<u-loadmore v-if="noPermission" :status="status" class="u-m-t-20" />
			</view>
			<view class="noData u-text-center u-m-t-30" style="color: #888;" v-if="!dataList.length">
				<text style="display: inline-block;">可在此功能中根据关键字查询</text>
				<text style="display: inline-block;">监控抓拍、四位一体、巡查发现、简易处罚、店铺巡查、黄牛处置、日常巡查等</text>
				<text style="display: inline-block;">案卷信息并查看</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				pageNum: 1,
				pageSize: 10,
				searchValue: '',
				noPermission: false,
				dataList: [],
				status: 'loading',
				names: {
					four: '四位一体',
					capture: '监控抓拍',
					inspection: '巡查发现',
					punish: '简易案件',
					tout: '黄牛处置',
					trafficCapture: '电子抓拍',
					transport: '违规处置'
				}
			}
		},
		methods: {
			fetchData() {
				const { pageNum, pageSize, searchValue } = this
				let params = { pageNum, pageSize }
				if (searchValue) params.searchValue = searchValue
				this.status = 'loading'
				this.$u.api.getUrgelistAll(params).then(res => {
					if (pageNum == 1) {
						this.dataList = res.rows
						uni.stopPullDownRefresh()
					} else {
						this.dataList = this.dataList.concat(res.rows)
					}
					this.status = res.rows.length < pageSize ? 'nomore' : 'loadmore'
				}).catch(err => {
					this.status = 'loadmore'
					uni.stopPullDownRefresh()
				})
			},
			handleSearch() {
				this.pageNum = 1
				this.fetchData()
			},
			handleOpen(row) {
				const typeList = {
					four: 'pages/home/<USER>/swyt/his/his',
					capture: 'pages/home/<USER>/jkzp/his/his',
					inspection: 'pages/home/<USER>/xcfx/his/his',
					punish: 'pages/home/<USER>/jycx/his/his',
					tout: 'pages/home/<USER>/hncz/his/his',
					trafficCapture: 'pages/home/<USER>/dzzp/his/his',
					transport: 'pages/home/<USER>/wgcz/his/his'
				}
				this.$u.route({ url: typeList[row.type], params: { id: row.id } })
			}
		},
		onPullDownRefresh() {
			this.pageNum = 1
			this.fetchData()
		},
		onReachBottom() {
			if (this.status == 'loadmore') {
				this.pageNum++
				this.fetchData()
			}
		}
	}
</script>

<style lang="scss">
page, .page {
	height: 100%;
}
.top-search {
	width: 100%;
	height: 103rpx;
	background-color: #fff;
	padding: 0 30rpx;
	// box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1);
	position: fixed;
	top: 0;
	left: 0;
	z-index: 10;
	/* #ifdef H5 */
	top: 44px;
	/* #endif */
	&-left {
		height: 68rpx;
		background-color: #F5F5F5;
		border-radius: 68rpx;
		padding: 0 20rpx;
		margin-right: 20rpx;
	}
	&-right {
		&-btn {
			width: 136rpx;
			height: 68rpx;
			border-radius: 68rpx;
			background-color: #327BF0;
			color: #fff;
			font-size: 28rpx;
			transition: all 0.5s;
			.img {
				margin-right: 10rpx;
			}
			&:active {
				opacity: 0.3;
			}
		}
	}
}

.container-list {
	min-height: 100%;
	background-color: #f3f3f4;
	padding: 123rpx 0rpx 30rpx;
	.list {
		padding-bottom: 209rpx;
		.list-item {
			margin: 20rpx 30rpx 0;
			background-color: #FFFFFF;
			border-radius: 12rpx;
			box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1);
			padding: 20rpx 20rpx 30rpx;
			.img {
				flex-shrink: 0;
				margin-right: 20rpx;
			}
			&-content {
				width: 360rpx;
				color: #808080;
				line-height: 32rpx;
				font-size: 24rpx;
				.title {
					width: 100%;
					line-height: 60rpx;
					font-weight: 700;
					font-size: 34rpx;
					color: #333333;
				}
				.text {
					width: 100%;
				}
			}
			&-state {
				width: 180rpx;
				flex-shrink: 0;
				font-size: 28rpx;
				font-weight: 700;
				line-height: 60rpx;
				margin-left: 20rpx;
				.circle {
					width: 16rpx;
					height: 16rpx;
					background-color: #EC5656;
					border-radius: 50%;
					margin-right: 10rpx;
				}
			}
		}
	}
}
</style>

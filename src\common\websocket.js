import store from "@/store"
let ip = store.state.vuex_ip.slice(7)
if (ip.includes(':')) {
	ip = ip.split(':')[0]
}
const url = `ws://${ip}:8088/ws`
console.log(url)
let socketTask = null
let setIntervalWesocketPush = null
let message = null
let isManual = false

// 创建链接
export const connectSocket = (msg) => {
	if (msg) {
		message = msg
		isManual = false
	}
	socketTask && socketTask.close()
	if (!socketTask) {
		socketTask = uni.connectSocket({
			url: url, //仅为示例，并非真实接口地址。
			complete(res) {
				// console.log(socketTask)
				socketTask.onOpen(onOpen)
				socketTask.onMessage(onMessage)
				socketTask.onError(onError)
				socketTask.onClose(onClose)
			}
		});
	} else {
		console.log('webscoket已连接')
	}
}

export const closeWebSocket = () => {
	isManual = true
	socketTask.close({ code: 1000 })
}

/** 打开WS之后发送心跳 */
const onOpen = () => {
	console.log('open')
	sendPing()
}

/** 连接失败重连 */
const onError = res => {
	socketTask.close()
	clearInterval(setIntervalWesocketPush)
	// console.log('连接失败重连中',socketTask.readyState, res)
	if (socketTask.readyState !== 3 && !isManual) {
		socketTask = null
		/* 避免与服务器断开后短时间无限重连 */
		setTimeout(() => {
			connectSocket()
		}, 1000)
	} else {
		socketTask = null
	}
}

/** WS数据接收统一处理 */
const onMessage = res => {
	const data = res.data ? JSON.parse(res.data) : {};
	console.log('msg---',data)
	let msgCounts = store.state.vuex_msg_count
	/* 统计新数字 */
	if (data.type == 1) {
		// 对应数值减一
		if (!msgCounts[data.name]) return
		else {
			msgCounts[data.name]--
			msgCounts.all--
		}
	} else if (data.type == 0) {
		// 对应数值加一
		msgCounts[data.name]++
		msgCounts.all++
	}
	/* 设置tarbar数字角标 */
	/* if (msgCounts.all <= 0) {
		uni.removeTabBarBadge({ index: 0 })
	} else {
		uni.setTabBarBadge({ index: 0, text: `${msgCounts.all}` })
	} */
	store.commit('$uStore', { name: 'vuex_msg_count', value: msgCounts })
}

/**
 * 发送数据但连接未建立时进行处理等待重发
 * @param {any} message 需要发送的数据
 */
const connecting = message => {
  setTimeout(() => {
    if (socketTask.readyState === 0) {
      connecting(message)
    } else {
      socketTask.send({ data: JSON.stringify(message) })
    }
  }, 1000)
}

/**
 * 发送数据
 * @param {any} message 需要发送的数据
 */
const sendWSPush = message => {
  if (socketTask !== null && socketTask.readyState === 3 && !isManual) {
    socketTask.close()
    connectSocket()
  } else if (socketTask.readyState === 1) {
    socketTask.send({ data: JSON.stringify(message) })
  } else if (socketTask.readyState === 0) {
    connecting(message)
  }
}

/** 断开重连 */
const onClose = (code) => {
  clearInterval(setIntervalWesocketPush)
	console.log('code', code)
  console.log('websocket已断开....正在尝试重连',socketTask.readyState)
  if (socketTask.readyState !== 2 && !isManual) {
    socketTask = null
		/* 避免与服务器断开后短时间无限重连 */
    setTimeout(() => {
			connectSocket()
		}, 1000)
  } else {
		socketTask = null
	}
}

// 发送心跳
const sendPing = () => {
	clearInterval(setIntervalWesocketPush)
	sendWSPush(message)
	setIntervalWesocketPush = setInterval(() => {
		sendWSPush(message)
	}, 5000)
}
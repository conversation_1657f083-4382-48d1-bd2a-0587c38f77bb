<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="150" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="督查人:" prop="userName" required>
					<u-input v-model="form.userName" type="popup" placeholder="督查人" />
				</u-form-item>
				<u-form-item label="事件描述:" prop="content" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.content" disabled type="textarea" placeholder="请输入事件描述" />
				</u-form-item>

				<view class="u-rela">
					<u-form-item label="考核条款:" prop="checkStandardName" label-position="top" required>
						<u-input v-model="form.checkStandardName" disabled type="textarea" placeholder="请选择考核条款" />
					</u-form-item>
					<!-- <view class="pos-r" @click="handleChooseStandar">选择项目</view> -->
				</view>
				<u-form-item label="考核标准:" prop="checkStandardContent">
					<u-input v-model="form.checkStandardContent" type="popup" placeholder="请选择考核标准" />
				</u-form-item>
				<u-form-item label="积分类型:" prop="type" required>
					<u-radio-group v-model="form.type" disabled>
						<u-radio name="1">扣分</u-radio>
						<u-radio name="0">加分</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="积分值:" prop="checkStandardScore" required>
					<u-input v-model="form.checkStandardScore" disabled type="number" placeholder="请输入积分值" />
				</u-form-item>
			</view>

			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="考核时间:" prop="happenTime" required>
					<u-input v-model="form.happenTime" type="popup" placeholder="请选择考核时间" @click="showHappenTime = true"  />
					<!-- <u-icon name="calendar"  @click="showHappenTime = true"  size="40"></u-icon>
					<u-picker v-model="showHappenTime" mode="time" :default-time="form.happenTime" :params="params"  @confirm="confirmHappenTime"></u-picker> -->
				</u-form-item>
				<u-form-item label="地址:" prop="address" required>
					<u-input v-model="form.address" type="popup" placeholder="请选择地址" />
					<!-- <u-button size="mini" type="primary" v-slot="right" @click="handleChooseAddress">选择地址</u-button> -->
				</u-form-item>
				<u-upload
					max-count="4"
					width="157"
					height="157"
					:auto-upload="false"
				  :size-type="['compressed']"
					:file-list="fileList"
					:deletable="false"
					:customBtn="true"
					name="files"
				></u-upload>
			</view>

			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<!-- form.status  1：新创建，2：推送班长，3：待反馈，9：已办结 -->
				<u-form-item label="班长:" prop="groupUserName" v-if="form.groupUserName">
					<u-input v-model="form.groupUserName" ref="monitor" type="popup" placeholder="班长" />
					<!-- <u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser({showRadio: 1, type: 'monitor'})">选择人员</u-button> -->
				</u-form-item>
				<u-form-item label="责任人:" prop="checkUserNames">
					<u-input v-model="form.checkUserNames" ref="responsible" type="popup" placeholder="责任人" />
					<!-- <u-button size="mini" type="primary"  v-if="form.status != 3" v-slot="right" @click="handleChooseHandleUser({showRadio: 0, type: 'responsible'})">选择人员</u-button> -->
				</u-form-item>
			</view>

			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">

				<u-form-item label="反馈内容:" prop="feedback" label-position="top" required v-if="form.feedback">
					<u-input v-model="form.feedback"  type="textarea" disabled placeholder="请输入反馈内容" />
				</u-form-item>
				<u-upload
					max-count="4"
					width="157"
					height="157"
					:auto-upload="false"
				  :size-type="['compressed']"
					:file-list="fileList1"
					:deletable="false"
					:customBtn="true"
					name="files"
				></u-upload>
			</view>

			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30" v-if="reasonShow || form.reason">
				<u-form-item label="申诉原因:" prop="reason" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.reason" type="textarea"  placeholder="请输入申诉原因" />
				</u-form-item>
			</view>
		</u-form>
		<!-- 提交按钮 -->
    <view class="btn-box u-border-top u-flex">
      <u-button v-if="appealStatus == 0" class="u-flex-1 u-m-r-20" type="primary" shape="circle" :loading="aloading" :custom-style="subStyle" @click="handleConfirmAppeal()">确认</u-button>
      <u-button v-if="appealStatus == 0" class="u-flex-1" type="primary" shape="circle" :loading="aloading" :custom-style="subStyle" @click="handleAppeal(1)">申诉</u-button>
    </view>
    <!-- 提示 -->
    <u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import {replaceNullWithEmptyString} from "../../../../../common/util";

  export default {
		data() {
			return {
        reasonShow:false,
        appealStatus: '',
				aloading: false,
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				form: {
          address:"浙江省金华市婺城区城北街道"
        },
				showHappenTime: false,
				fileList: [],
				fileList1: [],
				rules: {
					reason: [{ required: true, message: '请填写申诉原因', trigger: ['change', 'blur'] }],
				},
			}
		},
		methods: {
			handleAppeal(status){
        this.reasonShow = true
        console.log(this.form);
        if(!this.form.checkUserIds.split(',').includes(this.vuex_id.toString())) return this.mToase('您不是责任人，没有操作权限！')
				uni.showModal({
					title: '提示',
					content: '是否确认申诉？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$refs.uForm.validate(valid => {
								if (valid){
									this.aloading = true
									this.$loading()
									const params = { ...this.form, status, appealUserId:this.vuex_id, appealUserName: this.vuex_nickName  }
									this.$u.api.addAppeal(params).then(() => {
										uni.hideLoading()
										uni.showToast({title: '操作成功'})
										this.$implement()
									}).catch(() => {
										uni.hideLoading()
										this.aloading = false
									})
								}
							})
						}
					}
				})
			},
      handleConfirmAppeal(){
        this.reasonShow = false
        if(!this.form.checkUserIds.split(',').includes(this.vuex_id.toString())) return this.mToase('您不是责任人，没有操作权限！')
        uni.showModal({
          title: '提示',
          content: '是否确认？',
          success: ({ confirm }) => {
            if (confirm) {
              this.$refs.uForm.validate(valid => {
                if (valid){
                  this.aloading = true
                  this.$loading()
                  const params = { ...this.form, appealUserId:this.vuex_id, appealUserName: this.vuex_nickName  }
                  this.$u.api.confirmAppeal(params).then(() => {
                    uni.hideLoading()
                    uni.showToast({title: '操作成功'})
                    this.$implement()
                  }).catch(() => {
                    uni.hideLoading()
                    this.aloading = false
                  })
                }
              })
            }
          }
        })
      }
		},
		async onLoad(params) {
      if (params.appealStatus) this.appealStatus = params.appealStatus
			if (params.id) {
				this.$loading()
				const [standardRes, fileRes] = await Promise.all([
					this.$u.api.getStandard({}, params.id),
					this.$u.api.getFileList({ tableName: 'supervision_check_record', businessId: params.id })
				])

				/* 数据请求完毕 */
				uni.hideLoading()
				if (standardRes.code == 200 && fileRes.code == 200) {
					/* 表单数据 */
					this.form = replaceNullWithEmptyString(standardRes.data)
					console.log(fileRes)
					/* 文件数据 */
					 fileRes.rows.forEach(item => {
						// #ifdef H5
						const obj = { url: `${item.filePath}?id=${item.fileId}` }
						// #endif
						// #ifndef H5
						const obj = { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
						// #endif
						if(item.status == 1){
							this.fileList.push(obj)
						} else if (item.status == 3) {
							this.fileList1.push(obj)
						}
					})
				}
			} else {
				const timestamp = new Date().getTime()
				const happenTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = { type: '1', happenTime }
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.pos-r {
	height: 50rpx;
	line-height: 50rpx;
	font-size: 24rpx;
	background-color: #2979ff;
	border-radius: 10rpx;
	position: absolute;
	top: 30rpx;
	right: 0;
	color: #FFFFFF;
	padding: 0 15rpx;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

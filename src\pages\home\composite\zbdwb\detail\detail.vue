<template>
	<view class="zbdwb-detail">
		<view class="content u-border-top" @touchstart="touchStart" @touchend="isScroll = true">
			<u-line color="#e4e7ed" class="top-line" :style="{ top: `${ cH }rpx` }" />
			<!-- 上部分显示地址 -->
			<scroll-view
				class="address-box u-border-bottom"
				:scroll-left="cellScrollLeft"
				:scroll-x="isScroll"
				:style="{ height: `${ cH }rpx`, paddingLeft: `${ hW }rpx` }"
			>
				<view :style="{ width: `${configData.address.length * cW}rpx`, overflow: 'hidden' }">
					<view
						class="address u-border-right"
						v-for="(item,index) in configData.address"
						:key="index"
						:style="{ width: `${ cW }rpx`, height: `${ cH }rpx`, lineHeight: `${ cH }rpx`, fontSize: `${ fS }rpx` }"
						@click="handleAddress($event, index)"
					>
						<text>{{ item.name }}</text>
					</view>
				</view>
			</scroll-view>
			<!-- 左边部分显示时刻 -->
			<scroll-view
				class="hour-box u-border-right"
				:scroll-top="cellScrollTop"
				:scroll-y="isScroll"
				:style="{ width: `${ hW }rpx`, paddingTop: `${ cH }rpx` }"
			>
				<view 
					class="hour u-border-bottom"
					v-for="(item,index) in configData.hour"
					:key="index"
					:style="{ width: `${ hW }rpx`, height: `${ cH }rpx`, lineHeight: `${ cH }rpx`, fontSize: `${ fS }rpx` }"
				>
					<text>{{ item.name }}</text>
				</view>
			</scroll-view>
			<view :style="{ width: '100%', height: '100%', boxSizing: 'border-box', paddingTop: `${ cH }rpx`, paddingLeft: `${ hW }rpx` }">
				<scroll-view
					id="cellContent"
					class="content-inner"
					:scroll-left="cellScrollLeft"
					:scroll-top="cellScrollTop"
					:scroll-x="true"
					:scroll-y="true"
					@scroll="handleScroll"
				>
					<view class="content-box" :style="{ width: `${configData.address.length * cW}rpx` }">
						<view
							class="content-inner-item u-border-right u-border-top u-line-1"
							v-for="(item,index) in configData.cell"
							:key="index"
							:style="{ width: `${ cW }rpx`, height: `${ cH }rpx`, lineHeight: `${ cH }rpx`, fontSize: `${ fS }rpx` }"
							@click="handleUser($event, index)"
						>{{ item.userName }}</view>
					</view>
				</scroll-view>
			</view>
		</view>
		<!-- 缩放 -->
		<view class="footer">
			<view v-if="copyData.length" class="copy-list u-p-t-12 u-p-l-12 u-border-top">
				<u-tag v-for="(item,index) in copyData" :key="index" :text="item.name" class="u-m-r-12 u-m-b-12" closeable @close="removeCopy(index)"/>
			</view>
			<view class="u-border-top u-p-t-30 u-p-b-30" style="background-color:#fff;overflow: hidden;">
				<u-slider v-model="sliderValue" min="1" class="u-m-l-40 u-m-r-40" @end="sliderEnd"></u-slider>
			</view>
			<view class="btn-box u-border-top u-flex u-row-between">
				<view class="u-flex-1">
					<u-button type="primary" shape="circle" :custom-style="subStyle" @click="handleSubmit">提 交</u-button>
				</view>
			</view>
		</view>
		<!-- 弹窗显示列表 -->
		<view v-if="showMenuList" :style="{ top: menuTop, left: menuLeft, bottom: menuBottom, right: menuRight }" class="menu-list">
			<view class="item u-border-bottom" @click="showMenuList = false">
				<text>取消</text>
			</view>
			<view v-if="menuType == 'address'" class="item u-border-bottom" @click="prevAddress">
				<text>往前新增</text>
			</view>
			<view v-if="menuType == 'address'" class="item u-border-bottom" @click="appendAddress">
				<text>往后新增</text>
			</view>
			<view v-if="menuType == 'user' && hasValue" class="item u-border-bottom" @click="copyConfirm">
				<text>复制</text>
			</view>
			<view v-if="menuType == 'user' && copyData.length > 0" class="item u-border-bottom" @click="pasteConfirm">
				<text>粘贴</text>
			</view>
			<view v-if="hasValue" class="item u-border-bottom" @click="deleteConfirm">
				<text>删除</text>
			</view>
			<view
				v-for="(item,index) in menuList"
				:key="index"
				:class="menuList.length - 1 === index ? '' : 'u-border-bottom'"
				class="item"
				@click="itemConfirm(item)"
			>
				<text>{{ item.name }}</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				configData: {
					address: [],
					hour: [],
					cell: []
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				cellScrollTop: 0,
				cellScrollLeft: 0,
				cellH: 100,
				cellW: 200,
				hourW: 120,
				scale: 1.5,
				sliderValue: 50,
				fontSize: 28,
				isScroll: true,
				addressList: [],
				showMenuList: false,
				menuTop: null,
				menuLeft: null,
				menuBottom: null,
				menuRight: null,
				menuList: [],
				userList: [],
				selectIdx: 0, // 当前点击单元格的序号
				menuType: null,
				copyData: [],
				hasValue: false,
				detailObj: {}
			}
		},
		watch: {
			sliderValue() {
				this.scale = this.sliderValue * 2 * 0.01
			}
		},
		computed: {
			cW() {
				return this.cellW * this.scale
			},
			cH() {
				return this.cellH * this.scale
			},
			hW() {
				return this.hourW * this.scale
			},
			fS() {
				return this.fontSize * this.scale
			}
		},
		methods: {
			sliderEnd() {
				this.$u.vuex('vuex_slider', this.sliderValue)
			},
			touchStart(e) {
				const query = uni.createSelectorQuery().in(this);
				query.select('#cellContent').boundingClientRect(data => {
					const { clientX, clientY } = e.touches[0]
					const { left, top } = data
					this.isScroll = !!(clientX > left && clientY > top)
				}).exec();
			},
			handleScroll(e) {
				this.cellScrollTop = e.detail.scrollTop
				this.cellScrollLeft = e.detail.scrollLeft
			},
			handleSubmit() {
				const submitFn = this.configData.hourHeaderId ? this.$u.api.editHourHeader : this.$u.api.addHourHeader
				let params = { abscissa: JSON.stringify(this.configData.address), ordinate: JSON.stringify(this.configData.hour) }
				if (this.configData.hourHeaderId) params.hourHeaderId = this.configData.hourHeaderId
				if (!this.configData.distributeTime) {
					const timestamp = new Date().getTime()
					const distributeTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
					params.distributeTime = distributeTime
				}
				params.details = this.configData.cell.filter(item => item.userId)
				if (!params.details.length) {
					uni.showModal({
						title: '提示',
						content: '请添加人员',
						showCancel: true
					})
					return
				}
				this.$loading()
				submitFn(params).then(res => {
					uni.hideLoading()
					uni.showToast({title: '操作成功'})
					this.$implement()
				}).catch(() => {
					// uni.hideLoading()
				})
			},
			prevAddress() {
				// 往前添加一个地址栏
				this.confirmAddress({ name: '选择地址' }, false)
			},
			appendAddress() {
				// 往后添加一个地址栏
				this.selectIdx++
				this.confirmAddress({ name: '选择地址' }, false)
			},
			copyConfirm() {
				const cellData = this.configData.cell[this.selectIdx]
				this.copyData.push({ name: cellData.userName, key: cellData.userId })
				this.showMenuList = false
			},
			pasteConfirm() {
				this.copyData.forEach((item, idx) => {
					let cellData = this.configData.cell[this.selectIdx + idx]
					if (cellData) {
						cellData = { ...cellData, userName: item.name, userId: item.key }
						this.configData.cell.splice(this.selectIdx + idx, 1, cellData)
					}
				})
				this.showMenuList = false
			},
			removeCopy(idx) {
				this.copyData.splice(idx, 1)
			},
			deleteConfirm() {
				if (this.menuType == 'address') {
					// 删除地址
					this.confirmAddress(null,true)
				} else {
					const cellData = {...this.configData.cell[this.selectIdx], userId: null, userName: null }
					this.configData.cell.splice(this.selectIdx, 1, cellData)
					this.showMenuList = false
				}
			},
			setMenuPos(e) {
				if (e.detail.x + 130 >= this.sysInfo.screenWidth) {
					this.menuLeft = 'auto'
					this.menuRight = '0px'
				} else {
					this.menuLeft = `${e.detail.x}px`
					this.menuRight = 'auto'
				}
				if (e.detail.y + 400 >= this.sysInfo.screenHeight) {
					this.menuTop = 'auto'
					this.menuBottom = '0px'
				} else {
					this.menuTop = `${e.detail.y}px`
					// #ifdef H5
					this.menuTop = `${e.detail.y + 44}px`
					// #endif
					this.menuBottom = 'auto'
				}
			},
			// 选择地址
			handleAddress(e, idx) {
				if (this.configData.hourHeaderId) {
					this.showMenuList = false
					return
				}
				const addressData = this.configData.address[idx]
				this.hasValue =  !!(addressData.key)
				
				this.setMenuPos(e)
				this.selectIdx = idx
				this.menuType = 'address'
				this.menuList = this.addressList.map(item => {
					return {
						name: item.dictLabel,
						key: item.dictValue
					}
				})
				this.showMenuList = true
			},
			// 用户选择
			handleUser(e,idx) {
				const cellData = this.configData.cell[idx]
				this.hasValue =  !!(cellData.userId)
				
				this.setMenuPos(e)
				this.selectIdx = idx
				this.menuType = 'user'
				this.menuList = this.userList.map(item => {
					return {
						name: item.nickName,
						key: item.userId
					}
				})
				this.showMenuList = true
			},
			confirmAddress(item, isEdit) {
				// 点击最后一个单元格，新增地址,点击原本存在的地址，替换地址内容
				// const isEdit = this.selectIdx != this.configData.address.length - 1
				let addressList = [...this.configData.address]
				if (item) {
					addressList.splice(this.selectIdx, Number(isEdit), item)
				} else {
					addressList.splice(this.selectIdx, Number(isEdit))
				}
				this.configData.address = addressList
				// 重新生成数据
				let cellList = []
				this.configData.hour.forEach((hour, yIdx) => {
					this.configData.address.forEach((address,xIdx) => {
						cellList.push({
							xIdx,
							yIdx,
							title: `${xIdx},${yIdx}`,
							type: address.key,
							address: address.name,
							hour: hour.name
						})
					})
				})
				this.configData.cell = [...cellList]
				this.showMenuList = false
			},
			confirmUser(item) {
				const cellData = { ...this.configData.cell[this.selectIdx], userName: item.name, userId: item.key }
				this.configData.cell.splice(this.selectIdx, 1, cellData)
				this.showMenuList = false
			},
			// 确认选择
			itemConfirm(item) {
				if (this.menuType == 'address') {
					this.confirmAddress(item, true)
				} else {
					this.confirmUser(item)
				}
			},
		},
		async onLoad(params) {
			if (this.vuex_slider) this.sliderValue = this.vuex_slider
			// 获取屏幕高度，用以后续计算使用
			this.sysInfo = uni.getSystemInfoSync()
			// 获取地址数据字典
			this.$loading('加载配置信息')
			const res = await this.$u.api.dictList({dictType:"oa_scheduling_address"}).catch(() => {})
			if (res && res.rows) this.addressList = res.rows
			// 获取当前部门人员列表
			const userRes = await this.$u.api.getUserList({ deptId: this.vuex_deptId }).catch(() => {})
			if (userRes && userRes.rows) this.userList =  userRes.rows
			uni.hideLoading()
			if (params.id) {
				this.$loading()
				this.$u.api.getHourHeader({}, params.id).then(res => {
					uni.hideLoading()
					if (res.data) {
						res.data.details.forEach(item => {
							this.detailObj[item.title] = item
						})
						this.configData.hourHeaderId = res.data.hourHeaderId
						this.configData.address = JSON.parse(res.data.abscissa)
						this.configData.hour = JSON.parse(res.data.ordinate)
						this.configData.distributeTime = res.data.distributeTime
						this.configData.hour.forEach((hour, yIdx) => {
							this.configData.address.forEach((address,xIdx) => {
								let itemData = this.detailObj[`${xIdx},${yIdx}`] || {}
								this.configData.cell.push({
									xIdx,
									yIdx,
									type: address.key,
									address: address.name,
									hour: hour.name,
									title: `${xIdx},${yIdx}`,
									userId: itemData.userId,
									userName: itemData.userName
								})
							})
						})
					}
				}).catch(() => {
					uni.hideLoading()
				})
			} else {
				this.configData.hour = new Array(24).fill('').map((v, idx) => {
					return {name: idx}
				})
				this.configData.address = [{ name: '选择地址' }]
				this.configData.hour.forEach((hour, yIdx) => {
					this.configData.address.forEach((address,xIdx) => {
						this.configData.cell.push({
							xIdx,
							yIdx,
							title: `${xIdx},${yIdx}`,
							type: null,
							address: address.name,
							hour: hour.name
						})
					})
				})
			}
		}
	}
</script>

<style lang="scss">
	.zbdwb-detail {
		height: 100vh;
		/* #ifdef H5 */
		height: calc(100vh - 44px);
		/* #endif */
		display: flex;
		flex-direction: column;
	}
	.menu-list {
		width: 130px;
		max-height: 400px;
		position: fixed;
		top: 0;
		left: 0;
		background-color: rgba($color: #000000, $alpha: 0.6);
		z-index: 1000;
		color: #fff;
		padding: 5px;
		border-radius: 5px;
		text-align: center;
		overflow: auto;
		.item {
			height: 32px;
			line-height: 32px;
			transition: color 0.3s;
			font-size: 16px;
			&:active {
				color: #ccc;
			}
		}
	}
	.btn-box {
		width: 100%;
		padding: 14rpx 30rpx;
		// position: fixed;
		// bottom: 0;
		background-color: #FFFFFF;
		// z-index: 10;
	}
	.footer {
		flex-shrink: 0;
		.copy-list {
			background-color: #fff;
		}
	}
	.content {
		width: 100%;
		height: 0;
		flex: 1;
		// height: calc(100% - 200rpx);
		background-color: #fff;
		position: relative;
		overflow: hidden;
		.top-line {
			position: absolute;
			left: 0;
			z-index: 20;
		}
		.address-box {
			width: 100%;
			position: absolute;
			top: 1rpx;
			left: 0;
			background-color: #fff;
			z-index: 10;
			box-sizing: border-box;
			.address {
				float: left;
				text-align: center;
			}
		}
		.hour-box {
			height: 100%;
			position: absolute;
			top: 1rpx;
			left: 0;
			background-color: #fff;
			z-index: 10;
			box-sizing: border-box;
			.hour {
				text-align: center;
			}
		}
		.content-inner {
			width: 100%;
			height: 100%;
			overflow: auto;
			box-sizing: border-box;
			.content-box {
				overflow: hidden;
				.content-inner-item {
					text-align: center;
					float: left;
					border-color: #333;
				}
			}
		}
	}
</style>


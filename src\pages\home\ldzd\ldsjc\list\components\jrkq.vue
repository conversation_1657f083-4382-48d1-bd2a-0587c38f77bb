<template>
	<view class="u-p-t-30 u-p-b-30 pagess">
		<view class="u-m-t-30 u-m-b-18">
			<view class="tab">
				<u-tabs-swiper ref="uTabs" :active-item-style="barStyle" inactive-color="#ffffff"
					bg-color="rgba(0,0,0,0)" font-size="24rpx" :show-bar="false" name="deptName" :list="jrkqData" :current="current"
					@change="tabsChange" :is-scroll="false" swiperWidth="100%"></u-tabs-swiper>
			</view>
			<view class="container" :style="{height: height,background: background}">
				<u-empty v-if="!jrkqData.length" text="暂无数据" mode="list"></u-empty>
				<swiper v-else class="swiper" :style="{height:height}" :current="swiperCurrent" @transition="transition"
					@animationfinish="animationfinish">
					<!-- 我的 -->

					<swiper-item class="swiper-item" :style="styles" v-for="(item, index) in jrkqData"
						:key="index">
						<view class="jrkq">
							<view class="jrkq-tubiao">
								<u-circle-progress active-color="#FFAB00" border-width="24" bg-color='' :width="160"
									:percent="item.ratio">
									<view class="u-progress-content">
										<view class="u-progress-dot"></view>
										<text class='u-progress-info u-font-32'>{{item.ratio}}%</text>
									</view>
								</u-circle-progress>
								<view class="u-text-center u-m-t-10">出勤率</view>
							</view>
							<view class="jrkq-list">
								<view class="jrkq-list-item u-m-b-10">
									<view class="u-font-36" style="color: #F9536F;">{{item.total}}</view>
									<view>总人数</view>
								</view>
								<view class="jrkq-list-item">
									<view class="u-font-36" style="color: #2475FE;">{{item.late}}</view>
									<view>迟到人数</view>
								</view>
							</view>
							<view class="jrkq-list">
								<view class="jrkq-list-item u-m-b-10">
									<view class="u-font-36" style="color: #29E2FA;">{{item.notOnDuty}}</view>
									<view>未到岗人数</view>
								</view>
								<view class="jrkq-list-item">
									<view class="u-font-36" style="color: #FF992A;">{{item.leave}}</view>
									<view>请假人数</view>
								</view>
							</view>
						</view>
					</swiper-item>
				</swiper>
			</view>
		</view>
	</view>
</template>

<script>
	import PageContent from '@/components/page-content.vue'
	export default {
		data() {
			return {
				current: 0,
				swiperCurrent: 0,
				list: [],
				styles: {
					width: "100%",
					height: '547rpx'
				},
				sIsFirst: true,
				pIsFirst: true,
				barStyle: {
					color: "#fff",
					background: 'linear-gradient(180deg, #29E2FA 0%, #4286F8 100%)',
				}
			}
		},
		props: {
			jrkqData:{
				type:Array
			},
			height: {
				type: String,
				default: '607rpx'
			},
			background: {
				type: String,
				default: ''
			}

		},
		watch:{
			// jrkqData(newVol){
			// 	newVol.forEach((v,i)=>{
			// 		// list.push({name:v.deptName})
			// 		this.$set(this.list, i, {name:v.deptName})
			// 	})
			// 	console.log(this.list);
			// }
		},
		components: {
			PageContent
		},
		methods: {
			fetchData() {

			},
			tabsChange(index) {
				this.swiperCurrent = index;
			},
			transition(e) {
				let dx = e.detail.dx;
				this.$refs.uTabs.setDx(dx);
			},
			animationfinish(e) {
				let current = e.detail.current;
				this.$refs.uTabs.setFinishCurrent(current);
				this.swiperCurrent = current;
				this.current = current;

			},

		},
		// onReady() {
		// 	myChart = this.$echarts.init(document.getElementById('ea1'));
		// 	this.charts()
		// }
	}
</script>

<style lang="scss" scoped>
	.pagess {
		background-color: #1b345e;
		color: #fff;
	}

	.s-title {
		border-left: 8rpx #4689F5 solid;
		font-weight: bold;
	}

	::v-deep .u-tabs-scorll-flex {
		justify-content: center !important;

	}

	::v-deep .u-tabs-scorll-flex .u-tabs-item {
		flex: none !important;
		color: #fff;
		line-height: 60rpx;
		background: rgba(66, 134, 248, 0.3);
		// width: 140rpx;
		height: 60rpx;
		border-radius: 12rpx;
		margin-left: 20rpx;
	}

	.container {
		width: 100%;
		color: #fff;
		// padding: 30rpx;
		font-size: 24rpx;
		border-radius: 14px;
		margin: 18rpx 0 20rpx;

		.swiper {
			height: 100%;

			.swiper-item {
				width: 100%;
			}
		}
	}

	.ssjq-list {
		width: 100%;
		height: 189rpx;
		background: rgba(255, 255, 255, 0.08);
		color: #fff;
		padding: 30rpx;
		font-size: 24rpx;
		border-radius: 14px;
		margin-bottom: 20rpx;

		&-right {
			align-items: flex-end;
		}

		&-flex {
			display: flex;
			height: 100%;
			justify-content: space-between;
			flex-direction: column;

			&-state {
				width: 180rpx;
				flex-shrink: 0;
				font-size: 28rpx;
				font-weight: 700;
				line-height: 60rpx;
				margin-left: 20rpx;

				.circle {
					width: 16rpx;
					height: 16rpx;
					background-color: #EC5656;
					border-radius: 50%;
					margin-right: 10rpx;
				}
			}
		}

	}

	.jrkq {
		width: 100%;
		height: 290rpx;
		display: flex;
		justify-content: space-between;

		&-tubiao {
			background: rgba(66, 134, 248, 0.3);
			border-radius: 12rpx;
			width: 220rpx;
			box-sizing: border-box;
			padding: 30rpx;
		}

		&-list {
			font-size: 24rpx;
			height: 100%;
			justify-content: space-between;
			display: flex;
			width: 220rpx;
			flex-direction: column;
			margin-left: 10rpx;

			&-item {
				height: 140rpx;
				border-radius: 12rpx;
				background: rgba(66, 134, 248, 0.3);
				padding: 20rpx 30rpx;
				display: flex;
				justify-content: space-between;
				flex-direction: column;
			}
		}
	}
	::v-deep .u-tabs-scorll-flex{
		display: inline-block;
	}
</style>

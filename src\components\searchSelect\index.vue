<template>
  <view :style="SelectStyle">
    <u-input v-model="value" type="select" :select-open="showList" placeholder="请选择类型" @click="showList = true" />
    <u-action-sheet v-model="showList" :list="list" @click="handleTypeClick"></u-action-sheet>
  </view>
</template>

<script>
export default {
  name: "index",
  data() {
    return {
      showList: false
    }
  },
  props: {
    value: {
      type: String,
      default: ""
    },
    SelectStyle: {
      type: String,
      default: ""
    },
    list: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    handleTypeClick(idx) {
      const { type, text } = this.list[idx]
      this.$emit("input", text)
      this.$emit("change", {type, text})
    },
  },
  watch: {}
}
</script>

<style scoped>

</style>

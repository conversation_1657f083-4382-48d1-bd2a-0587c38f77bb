<template>
	<view>
		<!-- 主体 -->
		<view class="tab u-border-top">
			<u-tabs-swiper ref="uTabs" :list="list" :current="current" @change="tabsChange" :is-scroll="false" swiperWidth="750"></u-tabs-swiper>
		</view>
		<view class="container">
			<swiper class="swiper" :current="swiperCurrent" @transition="transition" @animationfinish="animationfinish">
				<!-- 我的 -->
				<swiper-item class="swiper-item" v-for="(scrollIItem, index) in listParams" :key="index">
					<!-- 搜索 -->
					<view class="top-search u-flex u-col-center">
						<view class="top-search-left u-flex u-flex-1">
							<u-image src="@/static/img/top-search.png" width="35rpx" height="35rpx"></u-image>
							<u-input v-model="scrollIItem.searchValue" type="text" class="u-flex-1 u-m-l-20"></u-input>
						</view>
						<view class="top-search-right">
							<view class="top-search-right-btn u-flex u-row-center u-col-center" @click="filtrate">
								<u-image src="@/static/img/filter.png" width="28rpx" height="28rpx" class="img"></u-image>
								<text>筛选</text>
							</view>
						</view>
					</view>
					<!-- 内容 -->
					<page-content refresher infiniting height="calc(100vh - 183rpx)" @refresh="refresh" @infinite="infiniteScroll" class="swiper-item-inner">
						<view class="list">
							<view class="list-item u-flex u-col-top u-row-between" v-for="(item, idx) in scrollIItem.listData" :key="idx" @click="handleOpen(item)">
								<u-image  class="img" src="@/static/img/list-icon.png" width="60rpx" height="60rpx"></u-image>
								<view class="list-item-content u-flex u-flex-col u-flex-1 u-col-top">
									<text class="title u-line-1">{{ item.inspectionContent}}</text>
									<!-- <view>
										<text class="text u-line-1">类型: </text>
										<text v-if="item.isTemporary == 1" style="color: #e4a904;">店铺巡查</text>
										<text v-else style="color: #d86349;">巡查发现</text>
									</view> -->
									<text class="text u-line-1">是否正常：{{ item.isProblem == 1 ? '发现问题' : '正式巡查' }}</text>
									<text class="text u-line-1" v-if="item.policeType">警情类别: {{ typeListName(item.policeType) }}</text>
									<text class="text u-line-1" v-if="item.businessName">店铺名称: {{ item.businessName }}</text>
									<text class="text u-line-1" v-if="item.userName">巡查人: {{ item.userName }}</text>
									<text class="text u-line-1" v-if="item.phone">当事人电话: {{ item.phone }}</text>
									<text class="text u-line-1" v-if="item.businessNo">营业执照: {{ item.businessNo }}</text>
									<text class="text u-line-1" v-if="item.address">详细地址: {{ item.address }}</text>
									<text class="text u-line-1" v-if="item.happenDate">发生时间: {{ item.happenDate }}</text>
								</view>
								<view class="list-item-state u-flex u-col-center u-row-right">
									<view class="circle" :style="{ backgroundColor: circleColor[item.status] }"></view>
									<text>{{ item.status | statusName }}</text>
								</view>	
							</view>
							<u-loadmore v-if="scrollIItem.noPermission" :status="scrollIItem.status" class="u-m-t-20" />
							<u-empty mode="permission" v-if="!scrollIItem.noPermission" class="u-m-t-80"></u-empty>
						</view>
					</page-content>
				</swiper-item> 
			</swiper>
		</view>
		<!-- 操作按钮 -->
		<view class="bottom-btn u-flex u-row-center u-col-center" @click="$u.route('pages/home/<USER>/wtcp/add/add' )">
			<u-image src="@/static/img/btn-add-icon.png" width="33rpx" height="33rpx"></u-image>
			<text class="text">新增巡查</text>
		</view>
	</view>
</template>

<script>
	import PageContent from '@/components/page-content.vue'
	export default {
		data() {
			return {
				list: [
					{ name: '我的' },
					// { name: '待处理' },
					{ name: '处理中' },
					{ name: '已办结' }
				],
				current: 0,
				swiperCurrent: 0,
				listParams: [
					{searchValue: '',pageNum: 1,pageSize: 10,type:2, noPermission: true,listData: [],status: 'loadmore'},
					{searchValue: '',pageNum: 1,pageSize: 10,type:2, noPermission: true,listData: [],status: 'loadmore'},
					{searchValue: '',pageNum: 1,pageSize: 10,type:2, noPermission: true,listData: [],status: 'loadmore'},
					// {searchValue: '',pageNum: 1,pageSize: 10,type:2, noPermission: true,listData: [],status: 'loadmore'},
				],
				sIsFirst: true,
				pIsFirst: true,
				yIsFirst: true,
				circleColor: {
					// 0: '#EC5656',
					1: '#FAB71C',
					2: '#25c548',
					9: '#bdc3bf'
				},
				typeList: []
			}
		},
		components: {
			PageContent
		},
		filters: {
			statusName(status) {
				const statusObj = { 1 : '待处理', 2 : '处理中', 9 : '已办结' }
				if (statusObj) return statusObj[status]
			},
		},
		methods: {
			typeListName(type) {
				if (type) {
					return this.typeList[type].dictLabel || ''
				} else {
					return ''
				}
			},
			filtrate(){
				this.listParams[this.current].pageNum = 1
				this.listParams[this.current].status= 'loadmore',
				this.fetchData()
			},
			rePageData() {
				this.listParams[this.current].pageNum = 1
				this.sIsFirst = true
				this.pIsFirst = true
				this.yIsFirst = true
				this.fetchData()
			},
			refresh({ complete }) {
				this.listParams[this.current].pageNum = 1
				this.fetchData(complete)
			},
			infiniteScroll({ setStatus }) {
				if (this.current == 0 && this.listParams[this.current].status == 'nomore') return
				if (this.current== 1 && this.listParams[this.current].status == 'nomore') return
				if (this.current== 2 && this.listParams[this.current].status == 'nomore') return
				if (this.current== 3 && this.listParams[this.current].status == 'nomore') return
				this.listParams[this.current].pageNum++
				this.fetchData()
			},
			fetchData(complete) {
				const { pageNum, pageSize, searchValue, type } = this.listParams[this.current]
				let params = { pageNum, pageSize, type }
				if (searchValue) params.searchValue = searchValue
				this.current == 2?params.status = 9 : this.current == 0? "" : params.status = 2
				let api = this.current == 0 ? this.$u.api.myInspectionList(params) : this.$u.api.inspectionList(params)
				this.listParams[this.current].status = 'loading'
				api.then(res => {
					if (pageNum === 1) {
						this.listParams[this.current].listData = res.rows
					} else {
						this.listParams[this.current].listData = this.listParams[this.current].listData.concat(res.rows)
					}
					this.listParams[this.current].status = res.rows.length < 10 ? 'nomore' : 'loadmore'
					if (complete) complete()
				}).catch((err) => {
					this.listParams[this.current].status = 'loadmore'
					if (complete) complete()
					if (err.data.code === 403) this.listParams[this.current].noPermission = false
				})
			},
			
			handleOpen(item){
				if (item.status == 9) {
					this.$u.route({ url: 'pages/home/<USER>/wtcp/his/his', params: { id: item.inspectionId } })
				} else {
					this.$u.route({ url: 'pages/home/<USER>/wtcp/detail/detail', params: { id: item.inspectionId } })
				}
			},
			tabsChange(index) {
				this.swiperCurrent = index;
			},
			transition(e) {
				let dx = e.detail.dx;
				this.$refs.uTabs.setDx(dx);
			},
			animationfinish(e) {
				let current = e.detail.current;
				this.$refs.uTabs.setFinishCurrent(current);
				this.swiperCurrent = current;
				this.current = current;
				if (this.sIsFirst && current == 1) {
					// 第一次切换到第二页加载数据
					this.fetchData()
					this.sIsFirst = false
				}else if(this.pIsFirst && current == 2){
					// 第一次切换到第二页加载数据
					this.fetchData()
					this.pIsFirst = false
				}else if(this.yIsFirst && current == 3){
					// 第一次切换到第二页加载数据
					this.fetchData()
					this.yIsFirst = false
				}
			},
		},
		async onLoad() {
			await this.$u.api.dictList({dictType:"case_alert_type"}).then(res => {
				this.typeList = res.rows
			})
			this.fetchData()
		}
	}
</script>

<style lang="scss">
.top-search {
	width: 100%;
	height: 103rpx;
	background-color: #fff;
	padding: 0 30rpx;
	box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1);
	position: relative;
	z-index: 10;
	&-left {
		height: 68rpx;
		background-color: #F5F5F5;
		border-radius: 68rpx;
		padding: 0 20rpx;
		margin-right: 20rpx;
	}
	&-right {
		&-btn {
			width: 136rpx;
			height: 68rpx;
			border-radius: 68rpx;
			background-color: #327BF0;
			color: #fff;
			font-size: 28rpx;
			transition: all 0.5s;
			.img {
				margin-right: 10rpx;
			}
			&:active {
				opacity: 0.3;
			}
		}
	}
}

.tab {
	
}

// 主体
.container {
	height: calc(100vh - 80rpx);
	background-color: #f5f5f5;
	// min-height: 100vh;
	.swiper {
		height: 100%;
		.swiper-item-inner {
			width: 100%;
			height: calc(100vh - 183rpx);
		}
	}
	.list {
		padding-bottom: 209rpx;
		.list-item {
			margin: 20rpx 30rpx 0;
			background-color: #FFFFFF;
			border-radius: 12rpx;
			box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1);
			padding: 20rpx 20rpx 30rpx;
			.img {
				flex-shrink: 0;
				margin-right: 20rpx;
			}
			&-content {
				width: 360rpx;
				color: #808080;
				line-height: 32rpx;
				font-size: 24rpx;
				.title {
					width: 100%;
					line-height: 60rpx;
					font-weight: 700;
					font-size: 34rpx;
					color: #333333;
				}
				.text {
					width: 100%;
				}
			}
			&-state {
				width: 180rpx;
				flex-shrink: 0;
				font-size: 28rpx;
				font-weight: 700;
				line-height: 60rpx;
				margin-left: 20rpx;
				.circle {
					width: 16rpx;
					height: 16rpx;
					background-color: #EC5656;
					border-radius: 50%;
					margin-right: 10rpx;
				}
			}
		}
	}
}

// 按钮
.bottom-btn {
	width: 220rpx;
	height: 88rpx;
	position: fixed;
	bottom: 40rpx;
	left: 50%;
	margin-left: -110rpx;
	background-color: #327BF0;
	color: #FFFFFF;
	border-radius: 88rpx;
	box-shadow: 0px 0px 14px 0px rgba(82, 97, 121, 0.3);
	transition: all 0.5s;
	z-index: 10;
	&:active {
		background-color: #73a6f7;
	}
	.text {
		font-size: 28rpx;
		margin-left: 15rpx;
	}
}
</style>

<template>
	<view>
		<view class="banner">
			<!-- 分数 -->
			<view class="total-score">
				<text class="score">{{ swiperCurrent == 0 ? totalScore : totalTime }}</text>
				<text>{{ swiperCurrent == 0 ? '当前积分(分)' : '学习总时长(分钟)' }}</text>
			</view>
			<!-- tab按钮 -->
			<view class="tab">
				<view class="tab-item" :class="{ active: swiperCurrent == 0 }" @click="swiperCurrent = 0">
					<u-image src="@/static/img/jl_jf_icon.png" width="64" height="64"></u-image>
					<text class="u-m-l-20">积分明细</text>
				</view>
				<view class="tab-item" :class="{ active: swiperCurrent == 1 }" @click="swiperCurrent = 1">
					<u-image src="@/static/img/js_xx_icon.png" width="64" height="64"></u-image>
					<text class="u-m-l-20">学习时长</text>
				</view>
			</view>
		</view>
		
		<!-- 列表 -->
		<view class="container">
			<swiper class="swiper" :current="swiperCurrent" @animationfinish="animationfinish">
				<!-- 我的 -->
				<swiper-item class="swiper-item">
					<!-- 内容 -->
					<page-content refresher infiniting height="calc(100vh - 480rpx)" @refresh="refresh" @infinite="infiniteScroll" class="swiper-item-inner">
						<view class="list">
							<view class="list-item" v-for="(item, idx) in listParams[0].dataList" :key="idx">
								<!-- 积分 -->
								<view class="u-flex u-row-between u-border-bottom u-p-b-30">
									<view class="u-flex u-flex-col u-col-top">
										<text class="title u-line-1">{{ item.reason }}</text>
										<text class="text u-line-1">{{ item.createTime }}</text>
									</view>
									<text class="u-font-b">{{ item.type == 1 ? '+' : '-' }}{{ item.score }}分</text>
								</view>
							</view>
							<u-loadmore :status="listParams[0].status" class="u-m-t-20" />
						</view>
					</page-content>
				</swiper-item>
				<!-- 学习时长 -->
				<swiper-item class="swiper-item">
					<!-- 内容 -->
					<page-content refresher infiniting height="calc(100vh - 480rpx)" @refresh="refresh" @infinite="infiniteScroll" class="swiper-item-inner">
						<view class="list">
							<view class="extraTime" v-if="extraTime > 0">
								<text>本月超额学习</text>
								<text class="org">{{ extraTime }}小时</text>
							</view>
							<view class="list-item" v-for="(item, idx) in listParams[1].dataList" :key="idx">
								<!-- 时长 -->
								<view class="u-flex u-row-between u-border-bottom u-p-b-30">
									<view class="u-flex u-flex-col u-col-top">
										<text class="title u-line-1">{{ item.learnContent }}</text>
										<text class="text u-line-1">{{ item.startTime }}</text>
									</view>
									<text class="title">{{ (item.duration / 60).toFixed(2) }}分钟</text>
								</view>
							</view>
							<u-loadmore :status="listParams[1].status" class="u-m-t-20" />
						</view>
					</page-content>
				</swiper-item>
			</swiper>
		</view>
	</view>
</template>

<script>
	import PageContent from '@/components/page-content.vue'
	
	export default {
		components: {
			PageContent
		},
		data() {
			return {
				totalScore: 0,
				totalTime: 0,
				extraTime: 0,
				swiperCurrent: 0,
				listParams: [
					{
						pageNum: 1,
						pageSize: 10,
						dataList: [],
						status: 'loading',
						isFirst: false
					},
					{
						pageNum: 1,
						pageSize: 10,
						dataList: [],
						status: 'loading',
						isFirst: true
					}
				]
			}
		},
		methods: {
			fetchAllData() {
				this.$u.api.getRecordCount({ userId: this.vuex_id }).then(res => {
					const result = res.rows[0]
					if (result) {
						this.totalScore = result.totalScore
						this.totalTime = result.totalTime
						this.extraTime = result.extraTime
					}
				})
			},
			fetchData(complete) {
				let { pageNum, pageSize, status, dataList } = this.listParams[this.swiperCurrent]
				let params = { pageNum, pageSize, userId: this.vuex_id }
				this.listParams[this.swiperCurrent].status = 'loading'
				const listFn = this.swiperCurrent == 0 ? this.$u.api.getScoreRecord : this.$u.api.getLearnRecord
				listFn(params).then(res => {
					if (pageNum === 1) {
						dataList = res.rows
					} else {
						dataList = dataList.concat(res.rows)
					}
					this.listParams[this.swiperCurrent].status = res.rows.length < 10 ? 'nomore' : 'loadmore'
					if (complete) complete()
					this.listParams.splice(this.swiperCurrent, 1, { pageNum, pageSize, status: this.listParams[this.swiperCurrent].status, dataList, isFirst: false })
				}).catch((err) => {
					this.listParams[this.swiperCurrent].status = 'loadmore'
					if (complete) complete()
					if (err.data.code === 403) noPermission = false
					this.listParams.splice(this.swiperCurrent, 1, { pageNum, pageSize, status: 'loadmore', dataList, isFirst: false })
				})
			},
			animationfinish(e) {
				this.swiperCurrent = e.detail.current
				if (this.listParams[e.detail.current].isFirst) {
					// 第一次切换到第二页加载数据
					this.fetchData()
				}
			},
			refresh({ complete }) {
				this.listParams[this.swiperCurrent].pageNum = 1
				this.fetchData(complete)
			},
			infiniteScroll({ setStatus }) {
				if (this.listParams[this.swiperCurrent].status === 'nomore') return
				this.listParams[this.swiperCurrent].pageNum++
				this.fetchData()
			}
		},
		onLoad() {
			this.fetchAllData()
			this.fetchData()
		}
	}
</script>

<style lang="scss">
.banner {
	width: 750rpx;
	height: calc(440rpx + var(--status-bar-height));
	background: url(../../../../../static/img/jl_bg.png) no-repeat top center / cover;
	overflow: hidden;
	position: relative;
	.total-score {
		margin-top: calc(120rpx + var(--status-bar-height));
		font-size: 28rpx;
		color: #FFFFFF;
		text-align: center;
		.score {
			font-size: 100rpx;
			font-weight: 700;
			display: block;
		}
	}
	.tab {
		position: absolute;
		bottom: 0;
		left: 30rpx;
		right: 30rpx;
		height: 110rpx;
		background-color: #F6F6F8;
		border-radius: 16rpx 16rpx 0 0;
		display: flex;
		overflow: hidden;
		.tab-item {
			flex: 1;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 28rpx;
			&.active {
				background-color: #fff;
			}
		}
	}
}
.container {
	background-color: #FFFFFF;
	margin: 0 30rpx;
	height: calc(100vh - 440rpx - var(--status-bar-height));
	.swiper {
		height: 100%;
	}
	.list-item {
		padding: 30rpx 30rpx 0;
		.title {
			font-size: 30rpx;
			font-weight: 700;
			margin-bottom: 10rpx;
		}
		.text {
			font-size: 24rpx;
			color: #808080;
		}
	}
	.extraTime {
		margin: 30rpx 30rpx 0;
		background: #FFEAEB;
		color: #333;
		font-size: 30rpx;
		font-weight: 700;
		padding: 20rpx;
		border-radius: 16rpx;
		display: flex;
		justify-content: space-between;
		.org {
			color: #FA6400;
		}
	}
}
</style>

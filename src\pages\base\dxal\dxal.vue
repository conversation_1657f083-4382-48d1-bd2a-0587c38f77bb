<template>
	<view class="u-skeleton">
		<learn-time ref="learnTime" learnContent="典型案例"></learn-time>
		<view class="container-top">
			<view class="title">
				<text class="u-line-2 u-skeleton-rect">{{ detailData.title }}</text>
			</view>
			<view class="b-title jg-icon">
				<text class="u-skeleton-rect">类型：</text>
				<text class="u-skeleton-rect">{{ detailData.typeName }}</text>
			</view>
			<view class="b-title sz-icon">
				<text class="u-skeleton-rect">实施时间：</text>
				<text class="u-skeleton-rect">{{ detailData.updateTime ? detailData.updateTime : detailData.createTime }}</text>
			</view>
			<view class="b-title wj-icon">
				<text class="u-skeleton-rect">相关文件：</text>
				<view class="u-skeleton-rect file" v-for="(item, index) in mainFileList" :key="index" @click="downLoadFile(item)">
					<view class="file_name">{{ item.name }}</view>
					<view>下载</view>
				</view>
			</view>
		</view>
		<u-gap bg-color="#F5F5F5" style="height: 10px;"></u-gap>
		<!-- 主体 -->
		<view class="container">
			<scroll-view class="u-skeleton-rect" :scroll-y="true" :show-scrollbar="true" style="height: calc(100vh - 195px);">
				<u-parse :html="detailData.content" :show-with-animation="true"></u-parse>
			</scroll-view>
		</view>
		<u-skeleton :loading="loading" :animation="true" bgColor="#FFF"></u-skeleton>

			<view class="task-loading" v-if="dowmloadToast">
				<text>下载进度{{progress}}%</text>
			</view>
	</view>
</template>

<script>
	import LearnTime from '@/components/learn-time.vue'

	export default {
		components: {
			LearnTime
		},
		data() {
			return {
				detailId: null,
				detailData: {
					title: '占位数据',
					issueDept: '占位数据',
					issueTime: '2020-01-01',
					implementationTime: '2020-01-01',
					content: '占位数据'
				},
				loading: true,
				mainFileList:[],
				progress:0,
				dowmloadToast: false
			}
		},
		methods: {
			// 下载文件
			downLoadFile(item){
				const tempFilePath = this.vuex_file_list[item.fileId]
				if (tempFilePath) {
					uni.openDocument({
					  filePath: tempFilePath,
					  showMenu: true,
					  success: function (res) {
					    console.log('打开文档成功');
					  },
						fail: function (res) {
						  console.log('打开文档失败',res);
						}
					});
				} else {
					const downloadTask = uni.downloadFile({
					  url: item.url,
					  success: (res) => {
					    var filePath = res.tempFilePath;
							this.$u.vuex('vuex_file_list', { ...this.vuex_file_list, [item.fileId]: filePath })
					    uni.openDocument({
					      filePath: filePath,
					      showMenu: true,
					      success: function (res) {
					        console.log('打开文档成功');
					      },
								fail: function (res) {
								  console.log('打开文档失败',res);
								}
					    });
					  }
					})

					downloadTask.onProgressUpdate((res) => {
						console.log(res)
						this.dowmloadToast = true
						this.progress = res.progress

						// 满足测试条件，取消下载任务。
						if (res.progress >= 100) {
								// downloadTask.abort()
								this.dowmloadToast = false
								this.progress = 0
						}
					})
				}

			},
			fetchData() {
				this.$loading()
				Promise.all([
					this.$u.api.getClassicCaseDetail({}, this.detailId),
					this.$u.api.getFileList({ tableName: 'kb_classic_case', businessId: this.detailId}),
				]).then(res => {
					console.log(res)
					uni.hideLoading()
					this.loading = false
					this.detailData = res[0].data
					this.mainFileList = res[1].rows.map(item => {
          return { name: item.displayName, url: `${this.vuex_ip}${item.filePath}`, ...item }
        })
				}).catch(() => {
					uni.hideLoading()
					this.loading = false
				})
			}
		},
		onLoad(params) {
			this.detailId = params.id
			this.fetchData()
		},
		onBackPress(e) {
			if (e.from == 'backbutton' && !this.$refs.learnTime.isEndStudy) {
				uni.showModal({
					title: '提示',
					content: '还未结束学习，是否保存学习记录？',
					confirmText: '直接退出',
					cancelText: '保存记录',
					success: ({ confirm }) => {
						if (confirm) {
							uni.navigateBack()
						} else {
							this.$refs.learnTime.addLearnRecord()
						}
					}
				})
				return true
			}
		}
	}
</script>

<style lang="scss">
.container-top {
	background-color: #fff;
	padding-top: 15px;
	padding-bottom: 15px;
	margin-top: 40px;
	.title {
		font-size: 18px;
		font-weight: 700;
		margin-bottom: 10px;
		padding: 0 15px;
	}
	.b-title {
		font-size: 12px;
		color: #808080;
		margin-bottom: 6px;
		padding: 0 15px 0 35px;
		&.jg-icon {
			background: url(../../../static/img/type-icon.png) no-repeat 15px center / contain;
		}
		&.sz-icon {
			background: url(../../../static/img/sz.png) no-repeat 15px center / contain;
		}
		&.wj-icon {
			background: url(../../../static/img/file.png) no-repeat 15px top / contain;
			background-size: 16px 16px;
		}
		.file{
			display: flex;
			justify-content: space-between;
			color: blue;
			text-decoration: underline;
			margin-top: 5px;
			.file_name{
				width: 90%;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}
}
.container {
	background-color: #FFFFFF;
	padding: 0 30rpx 15px;
	overflow-y: auto;
}
.task-loading {
	position: fixed;
	width: 30%;
	height: 100px;
	z-index: 99999;
	top: 50%;
	left: 50%;
	transform: translate(-50%,-50%);
	background: rgba(0,0,0,0.5);
	border-radius: 10px;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #fff;
}
</style>

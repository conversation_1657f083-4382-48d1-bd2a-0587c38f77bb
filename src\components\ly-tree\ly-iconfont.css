/* ly-tree iconfont 字体文件 */
/* #ifndef APP-NVUE */
@font-face {
	font-family: "ly-iconfont";
	font-weight: normal;
	font-style: normal;
	font-display: swap;
	src: url('/static/fonts/ly-iconfont.woff2') format('woff2');
}
/* #endif */

.ly-iconfont {
	font-family: "ly-iconfont" !important;
	font-size: 30rpx;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.ly-icon-caret-right:before {
	content: "\e8ee";
}

.ly-icon-loading:before {
	content: "\e657";
}


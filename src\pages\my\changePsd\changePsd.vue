<template>
	<view>
		<view>
			<u-form class="chage-psd-form u-border-top" :model="form" ref="uForm" label-width="220" label-position="top" :error-type="errorType">
				<u-form-item label="账号" prop="account">
					<u-input v-model="form.account" disabled/>
				</u-form-item>
				<u-form-item label="原密码" prop="oldPassword">
					<u-input type="password" v-model="form.oldPassword" />
				</u-form-item>
				<u-form-item label="新密码" prop="newPassword">
					<u-input type="password" v-model="form.newPassword"/>
				</u-form-item>
				<u-form-item label="密码确认" prop="newpassword">
					<u-input type="password" v-model="form.newpassword" />
				</u-form-item>
			</u-form>
			<view class="sub-btn">
				<u-button type="primary" shape="circle" @click="submit()" class="btn">提交</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				form: {},
				errorType:['message','border-bottom','toast'],
				rules: {
					oldPassword: [
						{
							required: true, 
							message: '请输入原密码',
							trigger: ['change','blur'],
						}
					],
					newPassword: [
						{
							required: true, 
							message: '请输入新密码',
							trigger: ['change','blur'],
						},{
							// 密码强度校验
							validator: (rule, value) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// this.$u.test.mobile()就是返回true或者false的
								const reg = /(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z]).{8,16}/
								return reg.test(value)
							},
							message: '请输入包含数字、大小写字母并且长度为8到16位的密码',
							// 触发器可以同时用blur和change
							trigger: ['change','blur'],
						}
					],
					newpassword: [
						{
							required: true, 
							message: '请再次输入新密码',
							trigger: ['change','blur'],
						},{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// this.$u.test.mobile()就是返回true或者false的
								return this.form.newPassword === value
							},
							message: '两次密码输入不一致',
							// 触发器可以同时用blur和change
							trigger: ['change','blur'],
						}
					]
				}
			}
		},
		methods: {
			submit() {
				this.$refs.uForm.validate(valid => {
					if (valid) {
						const { oldPassword, newPassword } = this.form
						this.$loading()
						this.$u.api.updatePwd({},`?oldPassword=${oldPassword}&newPassword=${newPassword}`).then(() => {
							uni.showToast({ title: '修改密码成功' })
							uni.hideLoading()
							setTimeout(() => {
								uni.navigateBack()
							},1500)
						}).catch(() => {
							uni.hideLoading()
						})
					}
				})
			}
		},
		onLoad() {
			this.form.account = this.vuex_username;
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
	.chage-psd-form {
		background-color: #fff;
		padding: 30rpx 30rpx 0;
	}
	.sub-btn {
		width: 100%;
		position: absolute;
		bottom: 0;
		padding: 15rpx 30rpx;
		background-color: #fff;
	}
</style>

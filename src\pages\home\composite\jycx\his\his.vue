<template>
	<view>
		<top-supervise :caseId="form.punishmentId" caseType="punish" :status="form.status"/>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="250" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="处罚对象:" prop="userType">
					<u-radio-group v-model="form.userType" disabled>
						<u-radio name="1">公民</u-radio>
						<u-radio name="2">法人、其他组织</u-radio>
					</u-radio-group>
				</u-form-item>
			</view>
			<view class="p-lr-30" v-if="form.userType == 2">
				<u-form-item label="是否个体工商户:" prop="isSelfEmployed" >
					<u-radio-group v-model="form.isSelfEmployed" disabled>
						<u-radio name="1">是</u-radio>
						<u-radio name="0">否</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="单位名称:" prop="companyName">
					<u-input v-model="form.companyName" placeholder="请输入单位名称" disabled />
				</u-form-item>
				<u-form-item label="单位地址:" prop="companyAddress">
					<u-input v-model="form.companyAddress" type="popup" placeholder="请选择单位地址" disabled />
				</u-form-item>
				<u-form-item label="统一社会信用代码:" prop="companyCode">
					<u-input v-model="form.companyCode"  placeholder="请输入单位统一社会信用代码" disabled />
				</u-form-item>
			</view>
			<view class="p-lr-30">
				<u-form-item label="当事人姓名:" prop="party">
					<u-input v-model="form.party" placeholder="请输入当事人姓名" disabled />
				</u-form-item>
				<u-form-item label="联系电话:" prop="phone">
					<u-input v-model="form.phone" placeholder="请输入当事人联系电话" disabled />
				</u-form-item>
				<u-form-item label="当事人性别:" prop="gender">
					<u-radio-group v-model="form.gender" disabled>
						<u-radio name="1">男</u-radio>
						<u-radio name="2">女</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="年龄:" prop="age" >
					<u-input v-model="form.age" type="number" placeholder="请输入当事人年龄" disabled />
				</u-form-item>
				<!-- <u-form-item label="身份证:" prop="identityCard" :border-bottom="false">
					<u-input v-model="form.identityCard" placeholder="请输入当事人身份证" disabled />
				</u-form-item> -->
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="案发时间:" prop="caseTime">
					<u-input v-model="form.caseTime" type="popup" placeholder="请选择案发时间" disabled />
					<u-icon name="calendar" size="40"></u-icon>
				</u-form-item>
				<u-form-item label="案由:" prop="summaryName">
					<u-input v-model="form.summaryName" type="popup" placeholder="请选择案由" disabled />
				</u-form-item>
				<u-form-item label="案发地址:" prop="address">
					<u-input v-model="form.address" type="popup" placeholder="请选择地址" disabled />
				</u-form-item>
				<u-form-item label="立案简介:" prop="caseContent" label-position="top" :border-bottom="false">
					<u-input v-model="form.caseContent" type="textarea" maxlength="300" height="140" disabled placeholder="请输入立案简介..."/>
				</u-form-item>
				<u-upload
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
					:size-type="['compressed']"
					:file-list="happenFile"
					:customBtn="true"
					:deletable="false"
				></u-upload>
			</view>

			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="处罚类型:" prop="punishType">
					<u-radio-group v-model="form.punishType" disabled>
						<u-radio name="0">警告</u-radio>
						<u-radio name="1">罚款</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item v-if="form.punishType == 1" label="处罚金额:" prop="punishMoney">
					￥<u-input v-model="form.punishMoney" type="number" placeholder="请输入处罚金额" disabled />
				</u-form-item>
				<u-form-item label="主办人:" prop="userName">
					<u-input v-model="form.userName" type="popup" placeholder="请选择协办人"/>
				</u-form-item>
				<u-form-item label="协办人:" prop="userIds" :border-bottom="false" >
					<u-input v-model="form.userNames" type="popup" placeholder="请选择协办人" disabled/>
				</u-form-item>
				<u-form-item label="处罚结果:" prop="remark" label-position="top" :border-bottom="false">
					<u-input v-model="form.remark" disabled type="textarea" maxlength="300" height="140" placeholder="请输入处罚结果..."/>
				</u-form-item>
				<u-upload
					ref="resfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
					:size-type="['compressed']"
					:file-list="resFile"
					:customBtn="true"
					:deletable="false"
				></u-upload>
			</view>
		</u-form>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import gps from '@/common/gps.js'
	import topSupervise from '@/components/top-supervise.vue'
	export default {
		components: {
			topSupervise
		},
		data() {
			return {
				form:{userType:1,isSelfEmployed:1,gender:1,type:0,punishType:0,onlineCarTarnsport:0,onlineCarDriver:0,address:""},
				labelStyle: {color: '#808080',fontSize: '30rpx'},
				showCaseDate:false,
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				subStyle: {height: '86rpx',backgroundColor: '#327BF0'},
				rules: {
					party: [{ required: true, message: '请输入当事人', trigger: 'blur' }],
					companyName: [{ required: true, message: '请填写单位名称', trigger: 'blur' }],
					companyAddress: [{ required: true, message: '请填写单位地址', trigger: 'change' }],
					companyCode: [{ required: true, message: '请填写单位统一社会信用代码', trigger: 'blur' }],
					// identityCard: [{ required: true, message: '请输入身份证', trigger: 'blur' },
					// 	{
					// 		validator: (rule, value, callback) => {
					// 			return this.$u.test.idCard(value);
					// 		},
					// 		message: '身份证号不正确',
					// 		trigger: ['change','blur'],
					// 	}],
					caseTime: [{ required: true, message: '请选择案发时间', trigger: 'change' }],
					summaryId: [{ required: true, message: '请选择案由', trigger: 'change' }],
					phone: [
						{ required: true, message: '请输入当事人联系电话', trigger: 'blur' },
						// {validator: (rule, value, callback) => {return this.$u.test.mobile(value);},message: '手机号码不正确',trigger: ['change','blur'],},
					],
					address: [{ required: true, message: '请选择地址', trigger: 'change' }],
					punishMoney: [{ required: true,type:'number',message: '请输入金额', trigger: 'blur' },{validator: (rule, value, callback) => {return this.$u.test.number(value);},message: '金额输入不正确',trigger: ['change','blur'],}],
					caseContent:[{ required: true, message: '请输入店铺名称', trigger: 'change' }],
				},
				happenData: {
					tableName: 'case_punishment',
					status: 2
				},
				happenFile:[],
				resFile: []
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			handCaseTime(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, caseTime: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleChooseHandleUser(type) {
				let params = {}
				if (this.form[type]) {
					params.defaultCheckedKeys = this.form[type]
					params.defaultExpandedKeys = this.form[type]
				}
				if(type == 'summaryId'){
					params.name = 'anyou'
				}
				params.type = type
				this.$u.route({ url: 'pages/common/selectUser/selectUser', params })
			},
			setUserData(checks,type) {
				// 选择好人员后的回调
				const checkData = checks[0]
				if(type == 'userIds'){
					if (checkData) this.form = { ...this.form, userNames: checkData.label, userIds: checkData.id }
				}else if(type == 'summaryId'){
					if (checkData) this.form = { ...this.form, summaryName: checkData.label, summaryId: checkData.id }
				}else {
					if (checkData) this.form = { ...this.form, userName: checkData.label, userId: checkData.id }
				}
			},
			handleChooseLocation() {
				uni.chooseLocation({
					latitude: 29.110764,
					longitude: 119.635857,
					success: res => {
						const { address, longitude, latitude } = res
						const lnglat = gps.gcj_decrypt(parseFloat(latitude), parseFloat(longitude))
						this.form = { ...this.form, address: "浙江省金华市婺城区城北街道" + address, longitude: lnglat.lng, latitude: lnglat.lat, lnglat: `${lnglat.lng}, ${lnglat.lat}` }
					},
					fail: () => {
						uni.showToast({
							title: '地图打开失败',
							icon: 'none',
							position: 'bottom'
						})
					}
				})
			},

				handleAllUpload(lists) {
					// 所有文件上传成功，返回上一级页面
					const isFail = lists.some(item => item.progress !== 100)
					if (!isFail) {
						uni.showToast({title: '操作成功'})
						uni.hideLoading()
						this.$implement()
					}
				},
				handleError() {
					// 文件上传失败，弹出提示是否重新上传
					uni.hideLoading()
					uni.showModal({
						title: '提示',
						content: '图片上传失败，是否重新上传？',
						success: ({ confirm }) => {
							if (confirm) {
								this.$loading('图片上传中')
								this.$refs.happenfile.reUpload()
							} else {
								this.$implement({ immediately: true })
							}
						}
					})
				},




		},
		onLoad(params) {
			this.$loading()
			if(params.id){
				Promise.all([
					this.$u.api.getPersuasion({}, params.id),
					this.$u.api.getFileList({ tableName: 'case_punishment', businessId: params.id }),
				]).then(resAry => {
					const formData = resAry[0].data
					const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
					this.form = { ...formData, lnglat }
					console.log(resAry[0]);
					resAry[1].rows.map(item => {
						if (item.status == 2) {
							this.happenFile.push({ url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` })
						} else if (item.status == 9) {
							this.resFile.push({ url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` })
						}
					})
					uni.hideLoading()
				}).catch((err) => {
					console.log(err);
					uni.hideLoading()
					uni.showToast({title: '加载失败', icon: 'error'})
				})
			}else{
				const timestamp = new Date().getTime()
				const caseTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = {...this.form,caseTime, userName: this.vuex_username, userId: this.vuex_id }
				uni.hideLoading()
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
.w-form ::v-deep .u-form-item--left{
	flex:.5 0 230rpx !important;
}
</style>

<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="店铺名称:" prop="shopName" required>
					<u-input v-model="form.shopName" placeholder="请输入店铺名称" />
				</u-form-item>
				<u-form-item label="店铺类型:" prop="businessTypeName" required>
					<u-input v-model="form.businessTypeName" type="select" :select-open="showShopList" placeholder="请选择店铺类型" @click="showShopList = true" />
					<u-action-sheet v-model="showShopList" :list="shopList" @click="handleTypeClick"></u-action-sheet>
				</u-form-item>
				<u-form-item label="营业执照类型:" prop="certificateType" >
					<u-input v-model="form.certificateType" placeholder="请输入营业执照类型" />
				</u-form-item>
				<u-form-item label="营业执照号码:" prop="businessNo" required>
					<u-input v-model="form.businessNo" placeholder="请输入营业执照号码" />
				</u-form-item>
				<u-form-item label="执照开始时间:" prop="businessStartTime" required>
					<u-input v-model="form.businessStartTime" type="popup" placeholder="请选择执照开始时间" @click="showStartTime = true"  />
					<u-icon name="calendar"  @click="showStartTime = true"  size="40"></u-icon>
					<u-picker v-model="showStartTime" mode="time" :params="params"  @confirm="handStartTime"></u-picker>
				</u-form-item>
				<!-- <u-form-item label="执照结束时间:" prop="businessEndTime" required>
					<u-input v-model="form.businessEndTime" type="popup" placeholder="请选择执照结束时间" @click="showEndTime = true"  />
					<u-icon name="calendar"  @click="showEndTime = true"  size="40"></u-icon>
					<u-picker v-model="showEndTime" mode="time" :params="params"  @confirm="handEndTime"></u-picker>
				</u-form-item> -->
				<u-form-item label="周期:" prop="typeName" required>
					<u-input v-model="form.typeName" type="select" :select-open="showTypeList" placeholder="请选择周期" @click="showTypeList = true" />
					<u-action-sheet v-model="showTypeList" :list="typeList" @click="handleCycleTypeClick"></u-action-sheet>
				</u-form-item>
				<u-form-item label="经营范围:" prop="businessScope">
					<u-input v-model="form.businessScope" placeholder="请输入经营范围" />
				</u-form-item>
				<u-form-item label="二维码串号:" prop="bindCode" required>
					<u-input v-model="form.bindCode" type="text" placeholder="二维码串号" />
					<u-icon name="scan"  @click="handleScan" v-slot="right" color="#2979ff" size="36"></u-icon>
				</u-form-item>
				<view class="u-rela">
					<u-form-item label="详细地址:" prop="address" label-position="top" required>
						<u-input v-model="form.address" type="textarea" placeholder="请选择详细地址" />
					</u-form-item>
					<view class="pos-r" @click="handleChooseLocation">选择地址</view>
					<!-- <u-button class="pos-r" size="mini" type="primary" ></u-button> -->
				</view>
			</view>
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="法人:" prop="corporation" required>
					<u-input v-model="form.corporation" placeholder="请输入法人" />
				</u-form-item>
				<u-form-item label="联系人:" prop="contactsName" required>
					<u-input v-model="form.contactsName" placeholder="请输入联系人" />
				</u-form-item>
				<u-form-item label="联系电话:" prop="contactsTelephone" required>
					<u-input v-model="form.contactsTelephone" placeholder="请输入联系电话" />
				</u-form-item>
				<u-form-item label="联系人手机2:" prop="contactsTelephonetwo">
					<u-input v-model="form.contactsTelephonetwo" placeholder="请输入联系人手机2" />
				</u-form-item>
				<!-- <u-form-item label="身份证号:" prop="contactsIdcard" required>
					<u-input v-model="form.contactsIdcard" placeholder="请输入联系人身份证号" />
				</u-form-item> -->

				<view class="u-rela">
					<u-form-item label="住址(地址):" prop="businessAddress" label-position="top" :border-bottom="false " required>
						<u-input v-model="form.businessAddress" type="textarea" placeholder="请选择住址(地址)" />
						<!-- <u-button size="mini" type="primary" v-slot="right" @click="handleChooseLocation('zz')">选择地址</u-button> -->
					</u-form-item>
					<view class="pos-r" @click="handleChooseLocation('zz')">选择地址</view>
				</view>
				<!-- <u-form-item label="省级:" prop="province">
					<u-input v-model="form.province" placeholder="请输入省级" />
				</u-form-item>
				<u-form-item label="地级:" prop="city" >
					<u-input v-model="form.city" placeholder="请输入地级" />
				</u-form-item>
				<u-form-item label="县级:" prop="area">
					<u-input v-model="form.area" placeholder="请输入县级" />
				</u-form-item>
				<u-form-item label="乡级:" prop="street" >
					<u-input v-model="form.street" placeholder="请输入乡级" />
				</u-form-item>
				<u-form-item label="村级:" prop="village" >
					<u-input v-model="form.village" placeholder="请输入村级" />
				</u-form-item> -->
				<!-- <u-form-item label="详细地址:" prop="address" required>
					<u-input v-model="form.address" type="popup" placeholder="请选择详细地址" />
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseLocation">选择地址</u-button>
				</u-form-item>
				<u-form-item label="管辖中队:" prop="jurisdictionName" required>
					<u-input v-model="form.jurisdictionName" type="select" :select-open="showJurisdiction" placeholder="请选择案件类型" @click="showJurisdiction = true" />
					<u-action-sheet v-model="showJurisdiction" :list="shopList" @click="handJurisdictionClick"></u-action-sheet>
				</u-form-item>
				<u-form-item label="中队小组:" prop="teamName" required>
					<u-input v-model="form.teamName" type="select" :select-open="showTeam" placeholder="请选择案件类型" @click="showTeam = true" />
					<u-action-sheet v-model="showTeam" :list="shopList" @click="handshowTeamClick"></u-action-sheet>
				</u-form-item> -->
				<!-- <u-form-item label="二维码串号:" prop="bindCode" required>
					<u-input v-model="form.bindCode" placeholder="请输入绑定的二维码串号" />
				</u-form-item>
				<u-form-item label="企业类型:" prop="enterpriseType" >
					<u-input v-model="form.enterpriseType" placeholder="请输入企业类型" />
				</u-form-item>
				<u-form-item label="登记机关:" prop="registrar">
					<u-input v-model="form.registrar" placeholder="请输入登记机关" />
				</u-form-item>
				<u-form-item label="管辖单位:" prop="jurisdictionUnit">
					<u-input v-model="form.jurisdictionUnit" placeholder="请输入管辖单位" />
				</u-form-item>
				<u-form-item label="注册资金:" prop="registeredCapital">
					<u-input v-model="form.registeredCapital" placeholder="请输入注册资金" />
				</u-form-item> -->
			</view>
			<view class="p-lr-30 u-flex u-row-between u-text-center u-p-b-30">
				<view>
					<u-upload
						ref="file0"
						name="files"
						max-count="1"
						width="210"
						height="210"
						:header="header"
						:auto-upload="false"
						:action="action"
						:form-data="happenData"
						:size-type="['compressed']"
						:file-list="happenFile"
						:before-remove="handleRemove"
						@on-uploaded="handleAllUpload"
						@on-error="handleError"
					></u-upload>
					<text>营业执照</text>
				</view>
				<view>
					<u-upload
						ref="file1"
						name="files"
						max-count="1"
						width="210"
						height="210"
						:header="header"
						:auto-upload="false"
						:action="action"
						:form-data="happenData"
						:size-type="['compressed']"
						:file-list="happenFile"
						:before-remove="handleRemove"
						@on-uploaded="handleAllUpload"
						@on-error="handleError"
					></u-upload>
					<text>身份证正面</text>
				</view>
				<view>
					<u-upload
						ref="file2"
						name="files"
						max-count="1"
						width="210"
						height="210"
						:header="header"
						:auto-upload="false"
						:action="action"
						:form-data="happenData"
						:size-type="['compressed']"
						:file-list="happenFile"
						:before-remove="handleRemove"
						@on-uploaded="handleAllUpload"
						@on-error="handleError"
					></u-upload>
					<text>身份证反面</text>
				</view>
			</view>
			<view class="p-lr-30 u-p-b-30 u-flex u-row-between u-text-center">
				<view>
					<u-upload
						ref="file3"
						name="files"
						max-count="1"
						width="210"
						height="210"
						:header="header"
						:auto-upload="false"
						:action="action"
						:form-data="happenData"
						:size-type="['compressed']"
						:file-list="happenFile"
						:before-remove="handleRemove"
						@on-uploaded="handleAllUpload"
						@on-error="handleError"
					></u-upload>
					<text>门店图片</text>
				</view>
				<view>
					<u-upload
						ref="file4"
						name="files"
						max-count="1"
						width="210"
						height="210"
						:header="header"
						:auto-upload="false"
						:action="action"
						:form-data="happenData"
						:size-type="['compressed']"
						:file-list="happenFile"
						:before-remove="handleRemove"
						@on-uploaded="handleAllUpload"
						@on-error="handleError"
					></u-upload>
					<text>附件</text>
				</view>
				<view>
					<u-upload
						ref="file5"
						name="files"
						max-count="1"
						width="210"
						height="210"
						:header="header"
						:auto-upload="false"
						:action="action"
						:form-data="happenData"
						:size-type="['compressed']"
						:file-list="happenFile"
						:before-remove="handleRemove"
						@on-uploaded="handleAllUpload"
						@on-error="handleError"
					></u-upload>
					<text>附件</text>
				</view>
			</view>
		</u-form>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex u-row-between">
			<view class="u-flex-1">
				<u-button type="primary" shape="circle" :custom-style="subStyle" @click="handleSubmit(2)">保存</u-button>
			</view>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import gps from '@/common/gps.js'

	export default {
		data() {
			return {
				form:{address:""},
				labelStyle: {color: '#808080',fontSize: '30rpx'},
				subStyle: {height: '86rpx',backgroundColor: '#327BF0'},
				params: { year: true, month: true, day: true, hour: false, minute: false, second: false },
				showShopList:false,
				showJurisdiction:false,
				showTeam:false,
				showStartTime:false,
				showEndTime:false,
				shopList:[],
				happenData: {
					tableName: 'case_shop',
				},
				happenFile:[],
				rules: {
					shopName: [{ required: true, message: '请输入店铺名称', trigger: 'blur' }],
					shopKey: [{ required: true, message: '请输入编码', trigger: 'blur' }],
					registrationNumber: [{ required: true, message: '请输入注册号', trigger: 'blur' }],
					businessNo: [{ required: true, message: '请输入营业执照号码', trigger: 'blur' }],
					businessStartTime: [{ required: true, message: '请输入营业执照开始时间', trigger: 'change' }],
					businessEndTime: [{ required: true, message: '请输入营业执照结束时间', trigger: 'change' }],
					corporation: [{ required: true, message: '请输入法人', trigger: 'blur' }],
					address: [{ required: true, message: '请输入详细地址', trigger: 'change' }],
					jurisdictionName: [{ required: true, message: '请输入管辖中队', trigger: 'change' }],
					teamName: [{ required: true, message: '请输入管辖中队小组', trigger: 'change' }],
					jurisdiction: [{ required: true, message: '请输入管辖中队名称', trigger: 'blur' }],
					contactsName: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
					// contactsTelephone: [{ required: true, message: '请输入联系电话', trigger: 'blur' },
					// {
					// 	validator: (rule, value, callback) => {
					// 		return this.$u.test.mobile(value);
					// 	},
					// 	message: '手机号码不正确',
					// 	trigger: ['change','blur'],
					// }],
					businessAddress: [{ required: true, message: '请输入住址(地址)', trigger: 'change' }],
					businessTypeName: [{ required: true, message: '请输入店铺类型', trigger: 'change' }],
					bindCode: [{ required: true, message: '请扫码获取二维码串号', trigger: 'change' }],
					// contactsIdcard: [{ required: true, message: '请输入身份证', trigger: 'blur' },
					// 	{
					// 		validator: (rule, value, callback) => {
					// 			return this.$u.test.idCard(value);
					// 		},
					// 		message: '身份证号不正确',
					// 		trigger: ['change','blur'],
					// 	}
					// ],
				},
				showTypeList: false,
				typeList: []
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			handleScan() {
				uni.scanCode({
					success: (res) => {
						if (res.result) this.form = { ...this.form, bindCode: res.result }
						else {
							uni.showToast({
								title: '未识别到有效二维码，请重试',
								icon: 'err',
								position: 'bottom'
							})
						}
					},
					fail: () => {
						uni.showToast({
							title: '扫码失败，请重试',
							icon: 'err',
							position: 'bottom'
						})
					}
				})
			},
			handleCycleTypeClick(idx) {
				const { type, text } = this.typeList[idx]
				this.form = { ...this.form, type, typeName: text }
			},
			handleSubmit() {
				const params = { ...this.form}
				this.$refs.uForm.validate(valid => {
					if (valid) {
						// 图片验证，没有图片不通过验证
						if (!this.$refs.file0.lists.length) {
							this.$refs.uTips.show({ title: '请上传营业执照图片', type: 'error', duration: '2300' })
							return
						}
						if (!this.$refs.file3.lists.length) {
							this.$refs.uTips.show({ title: '请上传门店图片', type: 'error', duration: '2300' })
							return
						}
						// 开始上传
						this.$loading('数据上传中')
						this.$u.api.shopAdd(params).then(res => {
							// 遍历列表，查询是否有未上传的图片
							// const uploadFile = this.$refs.file0.lists.some(item => item.progress !== 100)
							this.happenData.businessId = res.data.shopId
							this.happenData.status = 0
							// if (uploadFile) {
								this.$loading('营业执照图片上传中')
								this.$refs.file0.upload()
							// } else {
								// uni.showToast({title: '操作成功'})
								// uni.hideLoading()
								// this.$implement()
							// }
						}).catch(() => {
							uni.hideLoading()
						})
					}
				});
			},
			handStartTime(time){
				const { year, month, day, hour, minute, second} = time
				this.form = { ...this.form, businessStartTime: `${year}-${month}-${day}` }
			},
			handEndTime(time){
				const { year, month, day, hour, minute, second} = time
				this.form = { ...this.form, businessEndTime: `${year}-${month}-${day}` }
			},
			handleTypeClick(idx) {
				const { dictValue, dictLabel } = this.shopList[idx]
				this.form = { ...this.form, businessType:dictValue, businessTypeName: dictLabel }
			},
			handJurisdictionClick(idx) {
				const { dictValue, dictLabel } = this.shopList[idx]
				this.form = { ...this.form, jurisdictionId:dictValue, jurisdictionName: dictLabel }
			},
			handshowTeamClick(idx) {
				const { dictValue, dictLabel } = this.shopList[idx]
				this.form = { ...this.form, teamId:dictValue, teamName: dictLabel }
			},
			handleRemove(index, lists) {
				const fileInfo = lists[index]
				const fileId = fileInfo.url.split('?id=')[1]
				if (fileId) {
						return new Promise((resolve, reject) => {
							uni.showModal({
								title: '提示',
								content: '删除后将无法恢复，是否确认删除？',
								success: ({ confirm }) => {
									if (confirm) {
										this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
									} else {
										reject()
									}
								}
							})
						})
				} else {
					return true
				}
			},
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				if (!isFail) {
					if (this.happenData.status < 5) {
						const names = { 1: '身份证正面', 2: '身份证反面', 3: '门店图片', 4: '附件1' }
						this.happenData.status++
						this.$loading(`${names[this.happenData.status]}图片上传中`)
						this.$refs[`file${this.happenData.status}`].upload()
					} else {
						uni.showToast({title: '操作成功'})
						uni.hideLoading()
						this.$implement()
					}
				}
			},
			handleError() {
				// 文件上传失败，弹出提示是否重新上传
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					content: '图片上传失败，是否重新上传？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading('图片上传中')
							this.$refs.happenfile.reUpload()
						} else {
							this.$implement({ immediately: true })
						}
					}
				})
			},
			handleChooseLocation(type) {
				uni.chooseLocation({
					latitude: 29.110764,
					longitude: 119.635857,
					geocode: true,
					success: res => {
						const { address, longitude, latitude } = res
						const lnglat = gps.gcj_decrypt(parseFloat(latitude), parseFloat(longitude))
						if(type == 'zz') return this.form = { ...this.form, businessAddress:address }
						this.form = { ...this.form, address: "浙江省金华市婺城区城北街道" + address, longitude: lnglat.lng, latitude: lnglat.lat }
					},
					fail: () => {
						uni.showToast({
							title: '地图打开失败',
							icon: 'none',
							position: 'bottom'
						})
					}
				})
			},
		},
		onLoad() {
			Promise.all([
				this.$u.api.dictList({dictType:"shop_type"}),
				this.$u.api.dictList({dictType:"shop_cycle"})
			]).then(res=>{
				const [shopList, typeList] = res
				this.shopList = shopList.rows.map(item => {
					item.text = item.dictLabel
					item.type = item.dictValue
					return item
				})
				this.typeList = typeList.rows.map(item => {
					item.text = item.dictLabel
					item.type = item.dictValue
					return item
				})
			})
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style>
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.pos-r {
	height: 50rpx;
	line-height: 50rpx;
	font-size: 24rpx;
	background-color: #2979ff;
	border-radius: 10rpx;
	position: absolute;
	top: 30rpx;
	right: 0;
	color: #FFFFFF;
	padding: 0 15rpx;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
.w-form ::v-deep .u-form-item--left{
	flex:.5 0 230rpx !important;
}
</style>

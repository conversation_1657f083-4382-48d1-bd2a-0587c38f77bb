<template>
	<view class="containe">
		<qiun-data-charts type="line" :chartData="chartData" background="none" :opts="eopts" />
	</view>
</template>

<script>
	export default {
		props:{
			ajqsData:{
				type:Object
			}
		},
		watch:{
			ajqsData(newVal){

				if(!newVal) return
				this.chartData.series = [{
								"name": "行政执法",
								"data": newVal.inspectionData?newVal.inspectionData:[]
							},
							{
								"name": "运管执法",
								"data": newVal.transportData?newVal.transportData:[]
							},
							{
								"name": "电子抓拍",
								"data": newVal.caseCaptureData?newVal.caseCaptureData:[]
							}]
		}

		},

		data() {
			return {
				eopts: {
					"dataLabel": false,
					"color": [
						"#FD8863",
						"#FFC745",
						"#4FA7FF"
					],
					"xAxis": {
						axisLineColor: '#ffffff',
						fontColor: '#ffffff',
					},
					"yAxis": {
						axisLineColor: '#ffffff',
						data: [{
							fontColor: '#ffffff',
						}]
					},
					"legend": {
						"position": "top",
						"float": "right",
						fontColor: '#ffffff',
					}
				},
				chartData: {
					"categories": [
						"1月",
						"2月",
						"3月",
						"4月",
						"5月",
						"6月",
						"7月",
						"8月",
						"9月",
						"10月",
						"11月",
						"12月",
					],
					"series": []
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.containe {
		width: 100%;
		height: 607rpx;
		background: rgba(255, 255, 255, 0.08);
		color: #fff;
		// padding: 30rpx;
		font-size: 24rpx;
		border-radius: 14px;
		margin: 18rpx 0 20rpx;
	}
</style>

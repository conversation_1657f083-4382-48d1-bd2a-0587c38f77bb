<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="填写人" prop="userName" required>
					<u-input v-model="form.userName" type="text"  placeholder="填写人"  />
				</u-form-item>
				<u-form-item label="填写日期:" prop="logTime" required>
					<u-input v-model="form.logTime" type="popup" placeholder="请选择填写日期" @click="showlogTime = true"  />
					<u-icon name="calendar"  @click="showlogTime = true"  size="40"></u-icon>
					<u-picker v-model="showlogTime" mode="time" :params="params" :default-time="form.logTime" @confirm="handlogTime"></u-picker>
				</u-form-item>
				<u-form-item label="工作内容:" label-position="top" prop="content" :border-bottom="false" required>
					<u-input v-model="form.content" type="textarea" placeholder="请输入工作内容" />
				</u-form-item>
			</view>
		</u-form>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex u-row-between">
			<view class="u-flex-1">
				<u-button  type="primary" shape="circle" :custom-style="subStyle" @click="handleEdit">提 交</u-button>
			</view>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				showlogTime: false,
				form:{},
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				rules: {
					userName: [{ required: true, message: '请输入填写人', trigger: 'blur' }],
					logTime: [{ required: true, message: '请选择填写日期', trigger: 'change' }],
					content: [{ required: true, message: '请输入内容', trigger: 'blur' }]
				}
			}
		},
		methods: {
			handleEdit(state) {
				const params = { ...this.form }
				this.$refs.uForm.validate(async valid => {
					if (valid) {
						// 开始上传
						this.$loading('数据上传中')
						const submitFn = params.id ? this.$u.api.editWorkLog : this.$u.api.addWorkLog
						// 提交数据
						const res = await submitFn(params).catch(() => {})
						// 请求结束
						uni.hideLoading()
						if (res && res.code == 200) {
							uni.showToast({title: '操作成功'})
							uni.hideLoading()
							this.$implement()
						}
					}
				});
			},
			handlogTime(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, registerTime: `${year}-${month}-${day}` }
			}
		},
		async onLoad(params) {
			if(params.id){
				this.$loading()
				const res = await this.$u.api.getWorkLogDetail({}, params.id).catch(() => {})
				// 请求结束
				uni.hideLoading()
				if (res && res.code == 200) this.form = res.data
			}else{
				const timestamp = new Date().getTime()
				const logTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = { ...this.form, logTime, userId: this.vuex_id, userName: this.vuex_nickName }
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

<template>
	<view>
		<top-supervise :caseId="form.fourId" caseType="four" :status="form.status" />
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<!-- <u-form-item label="标题:" prop="title">
					<u-input v-model="form.title" placeholder="请输入标题" disabled/>
				</u-form-item> -->
				<u-form-item label="类型:" prop="typeText">
					<u-input v-model="form.typeText" type="popup" :select-open="showList" placeholder="请选择类型"/>
				</u-form-item>
				<u-form-item label="发起人员:" prop="userName">
					<u-input v-model="form.userName" type="text" disabled placeholder="请选择发起人员"  />
				</u-form-item>
				<u-form-item label="联系电话:" prop="phone">
					<u-input v-model="form.phone" disabled placeholder="请输入发起人员联系电话" />
				</u-form-item>
				<u-form-item label="发生时间:" prop="happenDate">
					<u-input v-model="form.happenDate" type="popup" placeholder="请选择发生时间" />
				</u-form-item>
				<u-form-item label="发生地址:" prop="address">
					<u-input v-model="form.address" type="popup" placeholder="请选择地址" />
				</u-form-item>
				<u-form-item label="经纬度:" prop="lnglat" :border-bottom="false">
					<u-input v-model="form.lnglat" type="popup" disabled  placeholder="请选择地址"/>
				</u-form-item>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="内容描述:" prop="content" label-position="top" :border-bottom="false">
					<u-input v-model="form.content" type="textarea" maxlength="300" height="140" disabled placeholder="请输入内容描述..."/>
				</u-form-item>
				<u-upload
					ref="happenfile"
					max-count="4"
					width="157"
					height="157"
					:auto-upload="false"
					:action="action"
				  :size-type="['compressed']"
					:file-list="happenFile"
					:deletable="false"
					:customBtn="true"
					name="files"
				></u-upload>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="处理人员:" prop="handleUserName">
					<u-input v-model="form.handleUserName" type="popup" placeholder="请选择处理人员" />
				</u-form-item>
				<u-form-item label="处理完成时间:" prop="handleDate" required>
					<u-input v-model="form.handleDate" type="popup" placeholder="请输入处理结果" @click="showHandleDate = true"/>
					<u-picker v-model="showHandleDate" mode="time" :params="params" :default-time="form.handleDate" @confirm="handleHpDateCon"></u-picker>
				</u-form-item>
				<u-form-item label="处理结果:" prop="handleContent" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.handleContent" type="textarea" maxlength="300" height="140" placeholder="请输入处理结果..."/>
				</u-form-item>
				<u-upload
					ref="handlefile"
					max-count="4"
					width="157"
					height="157"
					name="files"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="handleData"
				  :size-type="['compressed']"
					:file-list="handleFile"
					:before-remove="handleRemove"
					@on-uploaded="handleAllUpload"
					@on-error="handleError"
				></u-upload>
			</view>
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30" v-if="form.rejectContent">
				<u-form-item label="驳回原因:" label-position="top" :border-bottom="false">
					<u-input v-model="form.rejectContent" type="textarea" maxlength="300" height="140" disabled/>
				</u-form-item>
			</view>
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
		</u-form>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top">
			<u-button type="primary" shape="circle" :custom-style="subStyle" @click="handleSubmit">处理完成</u-button>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import topSupervise from '@/components/top-supervise.vue'

	export default {
		components: {
			topSupervise
		},
		data() {
			return {
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				showList: false,
				typeList: [
					{ type: 1, text: '环卫保洁' },
					{ type: 2, text: '园林' },
					{ type: 3, text: '绿化' },
					{ type: 4, text: '市政' }
				],
				showHandleDate: false,
				form: {address:"浙江省金华市婺城区城北街道"},
				rules: {
					handleDate: [{ required: true, message: '请选择处理完成时间', trigger: 'change' }],
					handleContent: [{ required: true, message: '请输入处理结果', trigger: 'change' }]
				},
				handleData: {
					tableName: 'case_four_in_one',
					status: 2
				},
				happenFile: [],
				handleFile: []
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			handleHpDateCon(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, handleDate: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleRemove(index, lists) {
				const fileInfo = lists[index]
				const fileId = fileInfo.url.split('?id=')[1]
				if (fileId) {
						return new Promise((resolve, reject) => {
							uni.showModal({
								title: '提示',
								content: '删除后将无法恢复，是否确认删除？',
								success: ({ confirm }) => {
									if (confirm) {
										this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
									} else {
										reject()
									}
								}
							})
						})
				} else {
					return true
				}
			},
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				if (!isFail) {
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					this.$implement()
				}
			},
			handleError() {
				// 文件上传失败，弹出提示是否重新上传
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					content: '图片上传失败，是否重新上传？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading('图片上传中')
							this.$refs.handlefile.reUpload()
						} else {
							this.$implement({ immediately: true })
						}
					}
				})
			},
			handleSubmit() {
				const params = { ...this.form, status: 2 }
				this.$refs.uForm.validate(valid => {
					if (valid) {
						// 图片验证，没有图片不通过验证
						if (!this.$refs.handlefile.lists.length) {
							this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
							return
						}
						// 开始上传
						this.$loading('数据上传中')
						this.$u.api.caseEdit(params).then(res => {
							// 遍历列表，查询是否有未上传的图片
							const uploadFile = this.$refs.handlefile.lists.some(item => item.progress !== 100)
							this.handleData.businessId = this.form.fourId
							if (uploadFile) {
								this.$loading('图片上传中')
								this.$refs.handlefile.upload()
							} else {
								uni.showToast({ title: '操作成功', mask: true })
								uni.hideLoading()
								this.$implement()
							}
						}).catch(() => {
							uni.hideLoading()
						})
					}
				});
			}
		},
		onLoad(params) {
			if (params.id) {
				this.$loading()
				Promise.all([
					this.$u.api.getCaseDetail({}, params.id),
					this.$u.api.getFileList({ tableName: 'case_four_in_one', businessId: params.id })
				]).then(resAry => {
					const formData = resAry[0].data
					const typeText = this.typeList.find(item => item.type == formData.type)
					const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
					const timestamp = new Date().getTime()
					const handleDate = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
					if (!formData.handleDate) formData.handleDate = handleDate
					this.form = { ...formData, typeText: typeText ? typeText.text : '', lnglat }
					resAry[1].rows.forEach(item => {
						const url =  { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
						if (item.status == 1) {
							this.happenFile.push(url)
						} else {
							this.handleFile.push(url)
						}
					})
					uni.hideLoading()
				}).catch(() => {
					uni.hideLoading()
				})
			} else {
				const timestamp = new Date().getTime()
				const happenDate = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = { happenDate, userName: this.vuex_username, userId: this.vuex_id }
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 115rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

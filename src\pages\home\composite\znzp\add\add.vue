<template>
	<view>
		<top-supervise :caseId="form.captureId" caseType="capture" :status="form.status"/>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="150" :label-style="labelStyle">
			<view class="p-lr-30">
				<!-- <u-form-item label="发起人:" prop="userName">
					<u-input disabled v-model="form.userName" placeholder="发起人" />
				</u-form-item> -->
				<u-form-item label="发生时间:" prop="happenTime">
					<u-input disabled v-model="form.happenTime" placeholder="请选择发生时间" />
				</u-form-item>
				<u-form-item label="案件类型:" prop="caseTypeName">
					<u-input disabled v-model="form.caseTypeName" placeholder="请选择案件类型"  />
				</u-form-item>
				<u-form-item label="案件内容:" label-position="top">
					<u-input disabled v-model="form.content" type="textarea" :isShowNum="false" placeholder="请输入案件内容"  />
				</u-form-item>
				<u-form-item label="发生地址:" prop="address" label-position="top" :border-bottom="false">
					<u-input disabled v-model="form.address"  type="textarea" :isShowNum="false" placeholder="请选择地址" />
				</u-form-item>
			</view>
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="内容描述:" prop="content" label-position="top" :border-bottom="false" required>
					<u-input disabled v-model="form.content" type="textarea" maxlength="300" height="140" placeholder="请输入内容描述..."/>
				</u-form-item>
				<u-upload
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:file-list="happenFile"
					:deletable="false"
					:customBtn="true"
				></u-upload>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="当班组长:" prop="squadronUserName">
					<u-input v-model="form.squadronUserName" type="popup" placeholder="当班组长"/>
				</u-form-item>
				<u-form-item label="执行人员:" prop="userNames" :border-bottom="false" >
					<u-input v-model="form.userNames" type="popup" placeholder="执行人员"/>
					<u-button v-if="form.status == 3" size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('userIds')">选择人员</u-button>
				</u-form-item>
			</view>
		</u-form>
		<view class="btn-box u-border-top u-flex">
			<u-button v-if="form.status == 3" type="primary" class="u-flex-1" shape="circle" :custom-style="subStyle" @click="handleSubmit(4)">确认指派</u-button>
			<u-button v-if="form.status == 4" type="error" class="u-flex-1" shape="circle" @click="showPopup = true">撤回</u-button>
			<u-button v-if="isShowOut" type="primary" class="u-flex-1 u-m-l-24" shape="circle" :custom-style="subStyle" @click="handleSubmit(5)">确认出警</u-button>
		</view>
		<!-- 撤回选项 -->
		<u-popup v-model="showPopup" mode="bottom" closeable>
			<u-form :model="reovkForm" ref="popForm" class="u-p-24" label-position="top" label-width="200" :label-style="labelStyle">
				<u-form-item label="撤回原因" :border-bottom="false">
					<u-input type="textarea" maxlength="300" height="140" v-model="reovkForm.revokeReason" />
				</u-form-item>
				<view class="u-flex u-row-between u-m-t-24">
					<u-button type="primary" shape="circle" @click="handleRevock" class="btn u-flex-1 u-m-t-24">确定</u-button>
				</view>
			</u-form>
		</u-popup>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import topSupervise from '@/components/top-supervise.vue'
	export default {
		components: {
			topSupervise
		},
		data() {
			return {
				form:{type:1,squadronName:'违停',address:"浙江省金华市婺城区城北街道"},
				labelStyle: {color: '#808080',fontSize: '30rpx'},
				subStyle: {height: '86rpx',backgroundColor: '#327BF0'},
				revockSubStyle: {height: '86rpx',backgroundColor: '#f03c3f'},
				caseTypeList:[],
				happenFile: [],
				rules: {
					userNames: [{ required: true, message: '请选择执行人员', trigger: 'change' }]
				},
				showPopup: false,
				reovkForm: {
					revokeReason: ''
				}
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			},
			isShowOut() {
				// 是否显示出警按钮
				return this.form.status == 4 && this.form.userIds.includes(this.vuex_id)
			}
		},
		methods: {
			handleRevock() {
				if (this.reovkForm.revokeReason.trim() == '') {
					this.$refs.uTips.show({ title: '请填写驳回原因', type: 'error', duration: '2300' })
					return
				}
				const params = {
					revokeType: 2,
					status: 3,
					revokeReason: this.reovkForm.revokeReason,
					captureId: this.form.captureId
				}
				this.$loading()
				this.$u.api.casesrevoke(params).then(res => {
					uni.hideLoading()
					this.showPopup = false
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					this.$implement()
				}).catch(() => {
					uni.hideLoading()
				})
			},
			validateFn(isValid, status) {
				// 根据传入的状态来确定是否要进行验证，用以区别暂存和办结
				return new Promise((resolve, reject) => {
					if (isValid) {
						this.$refs.uForm.validate(valid => {
							if (valid) {
								resolve()
							} else {
								reject()
							}
						})
					} else {
						resolve()
					}
				})
			},
			handleChooseHandleUser(type) {
				let params = {}
				if (this.form[type]) {
					params.defaultCheckedKeys = this.form[type]
					params.defaultExpandedKeys = this.form[type]
				}
				params.showRadio = 0
				params.type = type
				this.$u.route({ url: 'pages/common/selectUser/selectUser', params })
			},
			setUserData(checks,type) {
				// 选择好人员后的回调
				let checkData = {}
				if(checks.length == 1){
					checkData = checks[0]
				}else{
					checks.map((v,i)=>{
						if(i==0) {
							checkData.label = v.label+''
							checkData.id = v.id+''
						}else{
							checkData.label += ',' + v.label+''
							checkData.id += ',' + v.id+''
						}
					})
				}
				if(type == 'userIds'){
					if (checkData) this.form = { ...this.form, userNames: checkData.label, userIds: checkData.id }
				}
			},
			handleSubmit(status, isValid = true) {
				this.validateFn(isValid, status).then(res => {
					// 确认出警
					let parmas = { ...this.form, status }
					const timestamp = new Date().getTime()
					if (status == 4) {
						// 下派队员时间
						parmas.teamAllotTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
					}
					if (status == 5) {
						// 出警默认添加出警时间
						parmas.outTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
					}
					this.$loading()
					this.$u.api.captureEdit(parmas).then(res => {
						uni.showToast({title: '操作成功'})
						uni.hideLoading()
						this.$implement()
					}).catch(() => {
						uni.hideLoading()
					})
				}).catch(() => {})
			}
		},
		onLoad(params) {
			this.$loading()
			Promise.all([
				this.$u.api.dictList({dictType:'case_capture_type'}),
				this.$u.api.getcapture({},params.id),
				this.$u.api.getFileList({ tableName: 'case_capture', businessId: params.id })
			]).then(res=>{
				uni.hideLoading()
				// 数据字典数据
				this.caseTypeList = res[0].rows
				this.caseTypeList.forEach(v=>{
					if(v.dictLabel) v.text = v.dictLabel
					if(v.dictSort) v.type = v.dictSort
				})
				// 表单数据
				let formData = res[1].data
				const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
				this.form = {...this.form,...formData,lnglat}
				// 图片数据
				res[2].rows.map(v=>{
					const url =  { url: `${this.vuex_ip}/zqzfj${v.filePath}?id=${v.fileId}` }
					if (v.status == 2) {
						this.happenFile.push(url)
					}
				})
			}).catch(() => {
				uni.hideLoading()
			})
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

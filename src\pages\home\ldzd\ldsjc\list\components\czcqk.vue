<template>
	<view class="">
		<view class="ssjq-list u-flex u-row-between" v-for="(item,index) in taxiData" :key="index">
			<view class="ssjq-list-flex">
				<view class="u-font-32">车牌号：{{item.carNo}}</view>
				<view class="">进场时间：{{item.inTime}}</view>
				<view class="">出场时间：{{item.outTime}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		props:{
			taxiData:{
				type:Array
			}
		},
	}
</script>

<style lang="scss" scoped>
	.ssjq-list {
		width: 100%;
		height: 189rpx;
		background: rgba(255, 255, 255, 0.08);
		color: #fff;
		padding: 30rpx;
		font-size: 24rpx;
		border-radius: 14px;
		margin-bottom: 20rpx;
	
		&-right {
			align-items: flex-end;
		}
	
		&-flex {
			display: flex;
			height: 100%;
			justify-content: space-between;
			flex-direction: column;
	
			&-state {
				width: 180rpx;
				flex-shrink: 0;
				font-size: 28rpx;
				font-weight: 700;
				line-height: 60rpx;
				margin-left: 20rpx;
	
				.circle {
					width: 16rpx;
					height: 16rpx;
					background-color: #EC5656;
					border-radius: 50%;
					margin-right: 10rpx;
				}
			}
		}
	
	}
</style>

<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="150" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="案件编号:" prop="ajbh" required>
					<u-input v-model="form.ajbh" type="text" placeholder="案件编号" />
				</u-form-item>
				<u-form-item label="抄告单号:" prop="billno" required>
					<u-input v-model="form.billno" type="text" placeholder="抄告单号" />
				</u-form-item>
				<u-form-item label="采集部门:" prop="cjbmmc" required>
					<u-input v-model="form.cjbmmc" type="text" placeholder="采集部门名称" />
				</u-form-item>
				<u-form-item label="采集人员:" prop="cjmj" required>
					<u-input v-model="form.cjmj" type="text" placeholder="采集人员" />
				</u-form-item>
				<u-form-item label="处理时间:" prop="clsj" required>
					<u-input v-model="form.clsj" type="popup" placeholder="处理时间" @click="showClsjTime = true" />
					<u-icon name="calendar" @click="showClsjTime = true" size="40"></u-icon>
					<u-picker v-model="showClsjTime" mode="time" :default-time="form.clsj" :params="params" @confirm="confirmClsjTime"></u-picker>
				</u-form-item>
				<u-form-item label="处理标记:" prop="clbjName" required>
					<u-input v-model="form.clbjName" placeholder="请选择处理标记" type="select" :select-open="showClbjList" @click="showClbjList = true" />
					<u-action-sheet v-model="showClbjList" :list="clbjList" @click="clbjClick"></u-action-sheet>
				</u-form-item>
			</view>

			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="当事人:" prop="dsr" required>
					<u-input v-model="form.dsr" type="text" placeholder="当事人姓名" />
				</u-form-item>
				<u-form-item label="号牌号码:" prop="hphm" required>
					<u-input v-model="form.hphm" type="text" placeholder="号牌号码" />
				</u-form-item>
				<u-form-item label="违法时间:" prop="wfsj" required>
					<u-input v-model="form.wfsj" type="popup" placeholder="请选择违法时间" @click="showWfsjTime = true" />
					<u-icon name="calendar" @click="showWfsjTime = true" size="40"></u-icon>
					<u-picker v-model="showWfsjTime" mode="time" :default-time="form.wfsj" :params="params" @confirm="confirmWfsjTime"></u-picker>
				</u-form-item>
				<u-form-item label="违法地址:" prop="wfdz" required>
					<u-input v-model="form.wfdz" type="text" placeholder="请输入违法地址" />
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseAddress">选择地址</u-button>
				</u-form-item>
				<u-form-item label="违法行为:" prop="wfxw" required>
					<u-input v-model="form.wfxw" type="text" placeholder="请输入违法行为" />
				</u-form-item>
				<u-form-item label="罚款金额:" prop="fkje" required>
					<u-input v-model="form.fkje" type="number" placeholder="请输入罚款金额" />
				</u-form-item>
				<u-form-item label="缴款日期:" prop="jkrq">
					<u-input v-model="form.jkrq" type="popup" placeholder="请选择缴款日期" @click="showJkrqTime = true" />
					<u-icon name="calendar" @click="showJkrqTime = true" size="40"></u-icon>
					<u-picker v-model="showJkrqTime" mode="time" :default-time="form.jkrq" :params="params" @confirm="confirmJkrqTime"></u-picker>
				</u-form-item>
				<u-form-item label="缴款标记:" prop="jkbjName" required>
					<u-input v-model="form.jkbjName" placeholder="请选择缴款标记" type="select" :select-open="showJkbjList" @click="showJkbjList = true" />
					<u-action-sheet v-model="showJkbjList" :list="jkbjList" @click="jkbjClick"></u-action-sheet>
				</u-form-item>
				<u-form-item label="是否结案:" prop="sfjaName" required>
					<u-input v-model="form.sfjaName" placeholder="请选择是否结案" type="select" :select-open="showSfjaList" @click="showSfjaList = true" />
					<u-action-sheet v-model="showSfjaList" :list="sfjaList" @click="sfjaClick"></u-action-sheet>
				</u-form-item>
				<u-upload
					:file-list="fileList"
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
					:size-type="['compressed']"
					:before-remove="handleRemove"
					@on-uploaded="handleAllUpload"
					@on-error="handleError"
				></u-upload>
			</view>
		</u-form>

		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex">
			<u-button class="u-flex-1 u-m-r-20" type="primary" shape="circle" :loading="aloading" :custom-style="subStyle" @click="handleSubmit(1)">暂存</u-button>
			<u-button class="u-flex-1" type="primary" shape="circle" :loading="aloading" :custom-style="subStyle" @click="handleOver">提交</u-button>
		</view>

		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
import gps from '@/common/gps.js'
export default {
	data() {
		return {
			showClbjList: false,
			showJkbjList: false,
			showSfjaList: false,
			showClsjTime: false,
			showWfsjTime: false,
			showJkrqTime: false,
			aloading: false,
			fileList: [],

			// 处理标记选项
			clbjList: [
				{ type: 0, text: '未处理' },
				{ type: 1, text: '已处理' }
			],
			// 缴款标记选项
			jkbjList: [
				{ type: 0, text: '未缴款' },
				{ type: 1, text: '已缴款' }
			],
			// 是否结案选项
			sfjaList: [
				{ type: 0, text: '未结案' },
				{ type: 1, text: '已结案' }
			],

			params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
			labelStyle: {
				color: '#808080',
				fontSize: '30rpx'
			},
			subStyle: {
				height: '86rpx',
				backgroundColor: '#327BF0'
			},
			form: {},
			happenData: {
				tableName: 'case_sidewalk',
				status: 1
			},
			rules: {
				ajbh: [{ required: true, message: '请输入案件编号', trigger: ['change', 'blur'] }],
				billno: [{ required: true, message: '请输入抄告单号', trigger: ['change', 'blur'] }],
				cjbmmc: [{ required: true, message: '请输入采集部门', trigger: ['change', 'blur'] }],
				cjmj: [{ required: true, message: '请输入采集人员', trigger: ['change', 'blur'] }],
				clsj: [{ required: true, message: '请选择处理时间', trigger: ['change', 'blur'] }],
				clbjName: [{ required: true, message: '请选择处理标记', trigger: ['change', 'blur'] }],
				dsr: [{ required: true, message: '请输入当事人', trigger: ['change', 'blur'] }],
				hphm: [{ required: true, message: '请输入号牌号码', trigger: ['change', 'blur'] }],
				wfsj: [{ required: true, message: '请选择违法时间', trigger: ['change', 'blur'] }],
				wfdz: [{ required: true, message: '请输入违法地址', trigger: ['change', 'blur'] }],
				wfxw: [{ required: true, message: '请输入违法行为', trigger: ['change', 'blur'] }],
				fkje: [{ required: true, message: '请输入罚款金额', trigger: ['change', 'blur'] }],
				jkbjName: [{ required: true, message: '请选择缴款标记', trigger: ['change', 'blur'] }],
				sfjaName: [{ required: true, message: '请选择是否结案', trigger: ['change', 'blur'] }]
			}
		}
	},
	computed: {
		action() {
			// #ifdef H5
			return `/prod-api/system/file/upload`
			// #endif
			// #ifndef H5
			return `${this.vuex_ip}/prod-api/system/file/upload`
			// #endif
		},
		header() {
			return {
				Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
			}
		}
	},
	methods: {
		// 处理标记选择
		clbjClick(idx) {
			const { type, text } = this.clbjList[idx]
			this.form = { ...this.form, clbj: type, clbjName: text }
		},

		// 缴款标记选择
		jkbjClick(idx) {
			const { type, text } = this.jkbjList[idx]
			this.form = { ...this.form, jkbj: type, jkbjName: text }
		},

		// 是否结案选择
		sfjaClick(idx) {
			const { type, text } = this.sfjaList[idx]
			this.form = { ...this.form, sfja: type, sfjaName: text }
		},

		// 处理时间确认
		confirmClsjTime(time) {
			const { year, month, day, hour, minute, second } = time
			this.form = { ...this.form, clsj: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
		},

		// 违法时间确认
		confirmWfsjTime(time) {
			const { year, month, day, hour, minute, second } = time
			this.form = { ...this.form, wfsj: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
		},

		// 缴款日期确认
		confirmJkrqTime(time) {
			const { year, month, day, hour, minute, second } = time
			this.form = { ...this.form, jkrq: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
		},

		// 选择地址
		handleChooseAddress() {
			uni.chooseLocation({
				latitude: 29.110764,
				longitude: 119.635857,
				geocode: true,
				success: res => {
					const { address, longitude, latitude } = res
					const lnglat = gps.gcj_decrypt(parseFloat(latitude), parseFloat(longitude))
					this.form = { ...this.form, wfdz: address, longitude: lnglat.lng, latitude: lnglat.lat }
				},
				fail: () => {
					uni.showToast({
						title: '地图打开失败',
						icon: 'none',
						position: 'bottom'
					})
				}
			})
		},

		// 文件上传完成
		handleAllUpload(lists) {
			const isFail = lists.some(item => item.progress !== 100)
			if (!isFail) {
				uni.showToast({title: '操作成功'})
				uni.hideLoading()
				this.$implement()
			}
		},

		// 删除文件
		handleRemove(index, lists) {
			const fileInfo = lists[index]
			const fileId = fileInfo.url.split('?id=')[1]
			if (fileId) {
				return new Promise((resolve, reject) => {
					uni.showModal({
						title: '提示',
						content: '删除后将无法恢复，是否确认删除？',
						success: ({ confirm }) => {
							if (confirm) {
								this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
							} else {
								reject()
							}
						}
					})
				})
			} else {
				return true
			}
		},

		// 文件上传错误
		handleError() {
			uni.hideLoading()
			uni.showModal({
				title: '提示',
				content: '图片上传失败，是否重新上传？',
				success: ({ confirm }) => {
					if (confirm) {
						this.$loading('图片上传中')
						this.$refs.happenfile.reUpload()
					} else {
						this.$implement({ immediately: true })
					}
				}
			})
		},

		// 提交表单
		handleSubmit(status) {
			this.aloading = true
			this.$loading()
			const params = { ...this.form, status }
			let methodsFn = this.form.id ? this.$u.api.editSidewalk : this.$u.api.addSidewalk
			methodsFn(params).then(res => {
				const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
				this.happenData.businessId = this.form.id || res.data.id
				if (uploadFile) {
					this.$loading('图片上传中')
					this.$refs.happenfile.upload()
				} else {
					uni.hideLoading()
					uni.showToast({title: '操作成功'})
					this.$implement()
				}
			}).catch(() => {
				uni.hideLoading()
				this.aloading = false
			})
		},

		// 最终提交
		handleOver() {
			this.$refs.uForm.validate(valid => {
				if (!this.$refs.happenfile.lists.length) {
					this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
					return
				}

				if (valid) {
					uni.showModal({
						title: '提示',
						content: '是否确认提交？',
						success: ({ confirm }) => {
							if (confirm) {
								this.handleSubmit(9)
							}
						}
					})
				}
			})
		}
	},

	async onLoad(params) {
		if (params.id) {
			this.$loading()
			const [standardRes, fileRes] = await Promise.all([
				this.$u.api.getSidewalk({}, params.id),
				this.$u.api.getFileList({ tableName: 'case_sidewalk', businessId: params.id })
			])
			uni.hideLoading()
			if (standardRes.code == 200 && fileRes.code == 200) {
				this.form = standardRes.data

				// 处理标记
				const clbjText = this.clbjList.find(item => item.type == this.form.clbj)
				// 缴款标记
				const jkbjText = this.jkbjList.find(item => item.type == this.form.jkbj)
				// 是否结案
				const sfjaText = this.sfjaList.find(item => item.type == this.form.sfja)

				this.form = {
					...this.form,
					clbjName: clbjText ? clbjText.text : '',
					jkbjName: jkbjText ? jkbjText.text : '',
					sfjaName: sfjaText ? sfjaText.text : ''
				}

				this.fileList = fileRes.rows.map(item => {
          return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
				})
			}
		} else {
			const timestamp = new Date().getTime()
			const currentTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
			this.form = {
				clsj: currentTime,
				wfsj: currentTime,
				clbj: 0,
				clbjName: '未处理',
				jkbj: 0,
				jkbjName: '未缴款',
				sfja: 0,
				sfjaName: '未结案'
			}
    }
	},

	onReady() {
		this.$refs.uForm.setRules(this.rules)
	}
}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}

.pos-r {
	height: 50rpx;
	line-height: 50rpx;
	font-size: 24rpx;
	background-color: #2979ff;
	border-radius: 10rpx;
	position: absolute;
	top: 30rpx;
	right: 0;
	color: #FFFFFF;
	padding: 0 15rpx;
}

.container {
	padding-bottom: 145rpx;
}

.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}

// 表单项样式
.u-form-item {
	&__body {
		&__right {
			&__content {
				&__slot {
					display: flex;
					align-items: center;
				}
			}
		}
	}
}

// 上传组件样式
.upload-box {
	margin-top: 20rpx;
}

// 按钮样式
.u-button {
	&--primary {
		background-color: #327BF0 !important;
	}
}

// 选择器样式
.u-action-sheet {
	&__item {
		color: #333;
		font-size: 28rpx;
		height: 100rpx;
		line-height: 100rpx;
	}
}

// 时间选择器样式
.u-picker {
	&__title {
		color: #333;
		font-size: 32rpx;
		font-weight: 500;
	}
}
</style>

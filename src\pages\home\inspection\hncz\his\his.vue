<template>
	<view>
		<top-supervise :caseId="form.toutId" caseType="tout" :status="form.status"/>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<!-- <u-form-item label="标题:" prop="title" required>
					<u-input v-model="form.title" disabled placeholder="请输入标题" />
				</u-form-item> -->
				<u-form-item label="违法人姓名:" prop="lawbreakers" required>
					<u-input v-model="form.lawbreakers" disabled type="text"  placeholder="请选择违法人姓名"  />
				</u-form-item>
				<!-- <u-form-item label="身份证:" prop="identityCard" required>
					<u-input v-model="form.identityCard" disabled type="text"  placeholder="请选择身份证"  />
				</u-form-item> -->
				<u-form-item label="联系电话:" prop="phone" required>
					<u-input v-model="form.phone"  disabled placeholder="请输入联系电话" />
				</u-form-item>
				<u-form-item label="登记时间:" prop="happenDate" required>
					<u-input v-model="form.happenDate" type="popup" placeholder="请选择登记时间" @click="showHappenDate = true"  />
				</u-form-item>
				<!-- <u-form-item label="处理时间:" prop="handleDate" >
					<u-input v-model="form.handleDate" type="popup" placeholder="请选择处理时间" @click="showHandleDate = true"  />
				</u-form-item> -->
				<u-form-item label="办结时间:" prop="completeDate" >
					<u-input v-model="form.completeDate" type="popup" placeholder="请选择办结时间" @click="showCompleteDate = true"  />
				</u-form-item>
				<u-form-item label="发生地址:" prop="address" required>
					<u-input v-model="form.address" type="popup" disabled placeholder="请选择地址" />
				</u-form-item>
				<u-form-item label="经纬度:" prop="lnglat" :border-bottom="false" required>
					<u-input v-model="form.lnglat" type="popup" disabled  placeholder="请选择地址"/>
				</u-form-item>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="内容描述:" prop="content" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.content" disabled type="textarea" maxlength="300" height="140" placeholder="请输入内容描述..."/>
				</u-form-item>
				<u-upload
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
					:size-type="['compressed']"
					:file-list="happenFile"
					:before-remove="handleRemove"
					@on-uploaded="handleAllUpload"
					@on-error="handleError"
					:customBtn="true"
					:deletable="false"
				></u-upload>
				</view>
		</u-form>

	</view>
</template>

<script>
	import gps from '@/common/gps.js'
	import topSupervise from '@/components/top-supervise.vue'
	export default {
		components: {
			topSupervise
		},
		data() {
			return {
				form:{address:""},
				showHappenDate:false,
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				rules: {
					title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
					content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
					lawbreakers: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
					// identityCard: [{ required: true, message: '请输入身份证', trigger: 'blur' }],
					phone: [
						{ required: true, message: '请输入当事人联系电话', trigger: 'blur' },
						// {
						// 	validator: (rule, value, callback) => {
						// 		return this.$u.test.mobile(value);
						// 	},
						// 	message: '手机号码不正确',
						// 	trigger: ['change','blur'],
						// }
					],
					registerTime: [{ required: true, message: '请选择发生时间', trigger: 'change' }],
					address: [{ required: true, message: '请选择地址', trigger: 'change' }],
					lnglat: [{ required: true, message: '请选择地址', trigger: 'change' }],
				},
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				happenData: {
					tableName: 'case_tout',
					status: 1
				},
				happenFile: []
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			handleSubmit(state) {
				const params = { ...this.form ,status:state}
				console.log(params,'确定');
				this.$refs.uForm.validate(valid => {
					if (valid) {
						// 图片验证，没有图片不通过验证
						if (!this.$refs.happenfile.lists.length) {
							this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
							return
						}
						// 开始上传
						this.$loading('数据上传中')
						this.$u.api.toutAdd(params).then(res => {
							console.log(res);
							// 遍历列表，查询是否有未上传的图片
							const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
							this.happenData.businessId = res.data.toutId
							if (uploadFile) {
								this.$loading('图片上传中')
								this.$refs.happenfile.upload()
							} else {
								uni.showToast({title: '操作成功'})
								uni.hideLoading()
								this.$implement()
							}
						}).catch((err) => {
							console.log(err);
							uni.hideLoading()
						})
					}
				});
			},
			handHappenDate(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, happenDate: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleChooseLocation() {
				uni.chooseLocation({
					latitude: 29.110764,
					longitude: 119.635857,
					success: res => {
						const { address, longitude, latitude } = res
						const lnglat = gps.gcj_decrypt(parseFloat(latitude), parseFloat(longitude))
						this.form = { ...this.form, address: "浙江省金华市婺城区城北街道" + address, longitude: lnglat.lng, latitude: lnglat.lat, lnglat: `${lnglat.lng}, ${lnglat.lat}` }
					},
					fail: () => {
						uni.showToast({
							title: '地图打开失败',
							icon: 'none',
							position: 'bottom'
						})
					}
				})
			},
			handleRemove(index, lists) {
				const fileInfo = lists[index]
				const fileId = fileInfo.url.split('?id=')[1]
				if (fileId) {
						return new Promise((resolve, reject) => {
							uni.showModal({
								title: '提示',
								content: '删除后将无法恢复，是否确认删除？',
								success: ({ confirm }) => {
									if (confirm) {
										this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
									} else {
										reject()
									}
								}
							})
						})
				} else {
					return true
				}
			},
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				if (!isFail) {
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					this.$implement()
				}
			},
			handleError() {
				// 文件上传失败，弹出提示是否重新上传
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					content: '图片上传失败，是否重新上传？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading('图片上传中')
							this.$refs.happenfile.reUpload()
						} else {
							this.$implement({ immediately: true })
						}
					}
				})
			},
		},
		onLoad(params) {
			if(params.id){
				this.$loading()
				Promise.all([
					this.$u.api.getCaseTout({}, params.id),
					this.$u.api.getFileList({ tableName: 'case_tout', businessId: params.id })
				]).then(res=>{
					let formData = res[0].data
					this.happenFile = res[1].rows.map(item => {
					return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
				})
					const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
					this.form = {...formData,lnglat}
					uni.hideLoading()
				}).catch(err=>{
					console.log(err);
					uni.hideLoading()
				})
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}

</style>

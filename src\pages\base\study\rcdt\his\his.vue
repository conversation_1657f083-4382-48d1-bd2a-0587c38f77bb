<template>
	<view>
		<view class="top u-flex u-row-between u-p-30 u-border-top">
			<text style="color: #2979ff;">[{{ typeName }}](左滑进入下一题)</text>
			<view>
				<text style="color: #2979ff;">{{ current + 1 }}</text>
				<text>/{{ all }}</text>
			</view>
		</view>
		<swiper class="swiper" :current="current" :duration="200" @animationfinish="animationfinish">
			<swiper-item v-for="(item,idx) in questionList" :key="idx">
				<view class="question u-p-l-30 u-p-r-30">
					<text>{{item.questionBank.content}}</text>
					<!-- 单选题 -->
					<view class="u-p-t-30" v-if="item.questionBank.type == 1">
						<view v-for="option in optionList" :key="option">
							<view class="u-flex u-col-center u-m-b-30" style="color: #696969;" v-if="item.questionBank[`choice${option}`]">
								<text class="option" :class="{ 'user-checked': answerList[idx].userAnswer == option, check: answerList[idx].answer == option }" style="border-radius: 50%;">{{ option }}</text>
								<text class="u-flex-1">{{item.questionBank[`choice${option}`]}}</text>
							</view>
						</view>
					</view>
					<!-- 多选题 -->
					<view class="u-p-t-30" v-if="item.questionBank.type == 2">
						<view v-for="option in optionList" :key="option">
							<view class="u-flex u-col-center u-m-b-30" style="color: #696969;" v-if="item.questionBank[`choice${option}`]">
								<text class="option" :class="{ 'user-checked': answerList[idx].userAnswer.includes(option), check: answerList[idx].answer.includes(option) }">{{ option }}</text>
								<text class="u-flex-1">{{item.questionBank[`choice${option}`]}}</text>
							</view>
						</view>
					</view>
					<!-- 判断题 -->
					<view class="u-p-t-30" v-if="item.questionBank.type == 3">
						<view class="u-flex u-col-center u-m-b-30" style="color: #696969;">
							<view class="option" :class="{ 'user-checked': answerList[idx].userAnswer == 'T', check: answerList[idx].answer == 'T'}" style="border-radius: 50%;">
								<u-icon name="checkmark" size="24"></u-icon><!-- ✔ -->
							</view>
							<text class="u-flex-1">正确</text>
						</view>
						<view class="u-flex u-col-center u-m-b-30" style="color: #696969;">
							<view class="option" :class="{ 'user-checked': answerList[idx].userAnswer == 'F', check: answerList[idx].answer == 'F'}" style="border-radius: 50%;">
								<u-icon name="close" size="24"></u-icon><!-- ✖ -->
							</view>
							<text class="u-flex-1">错误</text>
						</view>
					</view>
					<!-- 答案结果 -->
					<view class="u-p-t-30 u-p-b-30">
						<text class="u-m-r-20">正确答案</text>
						<text style="color: #00ca00">{{ answerList[idx].answer | answerName }}</text>
						<text v-if="answerList[idx].userAnswer == answerList[idx].answer">，回答正确</text>
						<text v-if="answerList[idx].userAnswer != answerList[idx].answer" class="u-m-r-20">，您的答案</text>
						<text v-if="answerList[idx].userAnswer != answerList[idx].answer" style="color: #ea0000;"> {{ answerList[idx].userAnswer | answerName }}</text>
					</view>
					<!-- 解析 -->
					<view class="u-p-b-30">
						<text style="color: #2979ff;">[题目解析]</text>
					</view>
					<view>
						<text>{{ item.questionBank.knowledgePoint || '略' }}</text>
					</view>
				</view>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
	export default {
		filters: {
			answerName(answer) {
				if (answer == 'T') return '正确'
				if (answer == 'F') return '错误'
				return answer
			}
		},
		data() {
			return {
				typeName: '单选题',
				all: 1,
				current: 0,
				questionList: [],
				optionList: ['A','B','C','D','E','F','G','H'],
				answerList: [],
				examId: ''
			}
		},
		methods: {
			fetchData(examId) {
				this.$loading()
				this.$u.api.getQuestionInfo({ examId, userId: this.vuex_id }).then(res => {
					uni.hideLoading()
					this.questionList = res.questionList
					this.answerList = res.questionList.map(item => {
						return {
							id: item.id,
							answer: item.answer,
							userAnswer: item.userAnswer,
							answerDate: item.answerDate,
							status: item.status,
							examId: res.data.id
						}
					})
					this.all = res.questionList.length
				}).catch(() => {
					uni.hideLoading()
				})
			},
			animationfinish(e) {
				this.current = e.detail.current
				this.typeName = this.questionList[this.current].questionBank.typeName
			},
			hadnleJump(idx) {
				this.current = idx
			}
		},
		onLoad(params) {
			this.examId = params.id
			if (params.idx != undefined) this.current = parseInt(params.idx)
			this.fetchData(this.examId)
		},
		onNavigationBarButtonTap() {
			// 点击右上角按钮
			this.$u.route({ url: 'pages/base/study/rcdt/answer/answer', params: { id: this.examId } })
		}
	}
</script>

<style lang="scss">
page {
	background-color: #fff;
}
.top {
	height: 100rpx;
}
.swiper {
	min-height: calc(100vh - 100rpx);
	/* #ifdef H5 */
	min-height: calc(100vh - 100rpx - 44px);
	/* #endif */
	.option {
		width: 50rpx;
		height: 50rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		border: 1px solid #c1c1c1;
		border-radius: 10rpx;
		flex-shrink: 1;
		margin-right: 10rpx;
		.u-icon {
			width: 50rpx;
			height: 50rpx;
			justify-content: center;
		}
		&.user-checked {
			border-color: #ea0000;
			background-color: #ea0000;
			color: #fff;
		}
		&.check {
			background-color: #00ca00;
			border-color: #00ca00;
			color: #fff;
		}
	}
}
.btn-box {
	width: 100%;
	position: absolute;
	bottom: 0;
	left: 0;
	background: #fff;
	padding: 30rpx;
}
</style>

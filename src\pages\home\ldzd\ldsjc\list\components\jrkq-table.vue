<template>
  <view class="attendance-table-container">
    <!-- 表格头部 -->
    <view class="table-header">
      <view class="header-cell dept-name">部门名称</view>
      <view class="header-cell user-name">人员名称</view>
      <view class="header-cell start-time">上班打卡</view>
      <view class="header-cell end-time">下班打卡</view>
    </view>

    <!-- 表格内容 -->
    <scroll-view
      class="table-content"
      scroll-y
      :style="{ height: contentHeight }"
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view v-if="!attendanceList.length && !loading" class="empty-state">
        <u-empty text="暂无考勤数据" mode="list"></u-empty>
      </view>

      <view v-else>
        <view
          v-for="(item, index) in attendanceList"
          :key="index"
          class="table-row"
          :class="{ 'row-even': index % 2 === 0 }"
        >
          <view class="table-cell dept-name">
            <text class="cell-text">{{ item.deptName || '-' }}</text>
          </view>
          <view class="table-cell user-name">
            <text class="cell-text">{{ item.userName || '-' }}</text>
          </view>
          <view class="table-cell start-time">
            <text
              class="cell-text time-text"
              :class="getStartTimeClass(item.actualStartTime)"
            >
              {{ formatTime(item.actualStartTime) }}
            </text>
          </view>
          <view class="table-cell end-time">
            <text
              class="cell-text time-text"
              :class="getEndTimeClass(item.actualEndTime)"
            >
              {{ formatTime(item.actualEndTime) }}
            </text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="loading" class="loading-more">
        <u-loading mode="flower"></u-loading>
        <text class="loading-text">加载中...</text>
      </view>

      <view v-if="!hasMore && attendanceList.length" class="no-more">
        <text class="no-more-text">没有更多数据了</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: 'JrkqTable',
  props: {
    height: {
      type: String,
      default: '400rpx'
    },
    autoLoad: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      attendanceList: [],
      loading: false,
      refreshing: false,
      hasMore: true,
      pageNum: 1,
      pageSize: 20
    }
  },
  computed: {
    contentHeight() {
      return this.height
    }
  },
  mounted() {
    if (this.autoLoad) {
      this.loadData()
    }
  },
  methods: {
    // 加载数据
    async loadData(isRefresh = false) {
      if (this.loading) return

      this.loading = true

      if (isRefresh) {
        this.pageNum = 1
        this.hasMore = true
      }

      try {
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }

        const res = await this.$u.api.getTodayAttendance(params)
        const newData = res.rows || []

        if (isRefresh) {
          this.attendanceList = newData
        } else {
          this.attendanceList = [...this.attendanceList, ...newData]
        }

        // 判断是否还有更多数据
        this.hasMore = newData.length === this.pageSize

        if (this.hasMore) {
          this.pageNum++
        }

      } catch (error) {
        console.error('获取今日考勤表格数据失败:', error)
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },

    // 下拉刷新
    onRefresh() {
      this.refreshing = true
      this.loadData(true)
    },

    // 加载更多
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.loadData()
      }
    },

    // 格式化时间显示
    formatTime(timeStr) {
      if (!timeStr) return '-'

      // 如果是完整的日期时间格式，只取时间部分
      if (timeStr.includes(' ')) {
        return timeStr.split(' ')[1] || '-'
      }

      // 如果已经是时间格式，直接返回
      if (timeStr.includes(':')) {
        return timeStr
      }

      return '-'
    },

    // 获取上班打卡时间样式
    getStartTimeClass(timeStr) {
      if (!timeStr) return 'time-absent'

      const time = this.formatTime(timeStr)
      if (time === '-') return 'time-absent'

      // 假设上班时间是9:00，迟到判断
      const [hour, minute] = time.split(':').map(Number)
      const timeMinutes = hour * 60 + minute
      const workStartMinutes = 9 * 60 // 9:00

      if (timeMinutes <= workStartMinutes) {
        return 'time-normal'
      } else if (timeMinutes <= workStartMinutes + 30) {
        return 'time-late'
      } else {
        return 'time-very-late'
      }
    },

    // 获取下班打卡时间样式
    getEndTimeClass(timeStr) {
      if (!timeStr) return 'time-absent'

      const time = this.formatTime(timeStr)
      if (time === '-') return 'time-absent'

      // 假设下班时间是18:00，早退判断
      const [hour, minute] = time.split(':').map(Number)
      const timeMinutes = hour * 60 + minute
      const workEndMinutes = 18 * 60 // 18:00

      if (timeMinutes >= workEndMinutes) {
        return 'time-normal'
      } else {
        return 'time-early'
      }
    },

    // 手动刷新数据（供外部调用）
    refresh() {
      this.loadData(true)
    }
  }
}
</script>

<style lang="scss" scoped>
.attendance-table-container {
  background-color: rgba(255, 255, 255, 0.08);
  border-radius: 12rpx;
  overflow: hidden;
  color: #fff;
}

.table-header {
  display: flex;
  background-color: rgba(66, 134, 248, 0.5);
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.1);
}

.header-cell {
  padding: 24rpx 16rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  border-right: 1rpx solid rgba(255, 255, 255, 0.1);

  &:last-child {
    border-right: none;
  }

  &.dept-name {
    flex: 1.5;
  }

  &.user-name {
    flex: 1.2;
  }

  &.start-time,
  &.end-time {
    flex: 1;
  }
}

.table-content {
  background-color: rgba(255, 255, 255, 0.03);
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.05);

  &.row-even {
    background-color: rgba(255, 255, 255, 0.02);
  }

  &:hover {
    background-color: rgba(66, 134, 248, 0.1);
  }
}

.table-cell {
  padding: 20rpx 16rpx;
  border-right: 1rpx solid rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;

  &:last-child {
    border-right: none;
  }

  &.dept-name {
    flex: 1.5;
  }

  &.user-name {
    flex: 1.2;
  }

  &.start-time,
  &.end-time {
    flex: 1;
  }
}

.cell-text {
  font-size: 26rpx;
  text-align: center;
  word-break: break-all;
  line-height: 1.4;
}

.time-text {
  font-weight: 500;

  &.time-normal {
    color: #4CAF50; // 正常 - 绿色
  }

  &.time-late {
    color: #FF9800; // 迟到 - 橙色
  }

  &.time-very-late {
    color: #F44336; // 严重迟到 - 红色
  }

  &.time-early {
    color: #2196F3; // 早退 - 蓝色
  }

  &.time-absent {
    color: #9E9E9E; // 未打卡 - 灰色
  }
}

.empty-state {
  padding: 80rpx 0;
  text-align: center;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;

  .loading-text {
    margin-left: 20rpx;
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.6);
  }
}

.no-more {
  text-align: center;
  padding: 30rpx 0;

  .no-more-text {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.4);
  }
}
</style>

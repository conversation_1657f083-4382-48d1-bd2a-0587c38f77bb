<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>APP下载</title>
    <script src="./flexible.js"></script>
    <style>
    body {
      margin: 0;
      padding: 0;
      height: 100%;
      -moz-osx-font-smoothing: grayscale;
      -webkit-font-smoothing: antialiased;
      text-rendering: optimizeLegibility;
      font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
      font-size: 16px;
    }
    .content {
      padding-top: 65px;
      background: #fff;
      margin-top: -14px;
      border-radius: 15px 15px 0 0;
      position: relative;
    }
    .header {
      height: 154px;
      background: url(./banner.png) no-repeat left -45px / cover;
    }
    .content .logo {
      border: 5px solid #fff;
      position: absolute;
      width: 0.78125rem;
      height: 0.78125rem;
      background: url(./logo.png) no-repeat center center/contain;
      -webkit-box-shadow: 0 2px 12px 0 rgb(0 0 0 / 16%);
      box-shadow: 0 2px 12px 0 rgb(0 0 0 / 16%);
      border-radius: 50%;
      top: -0.4rem;
      left: 50%;
      transform: translateX(-50%);
    }
    .content h3.title {
      text-align: center;
      font-size: 24px;
      font-weight: 500;
    }
    .content p.text {
      font-size: 14px;
      color: #888;
      text-align: center;
    }
    .qrcode {
      width: 1.30208rem;
      height: 1.30208rem;
      margin: 0 auto;
      background: url(./qrcode.png) no-repeat center center / contain;
    }
    .btn {
      width: 1.875rem;
      height: 0.41667rem;
      line-height: 0.41667rem;
      border-radius: 0.41667rem;
      background: #062f65;
      margin: 0.26042rem auto 0;
      color: #fff;
      text-align: center;
      transition: all 0.3s;
    }
    .btn:active {
      opacity: 0.3;
    }
    </style>
</head>
<body>
    <div class="header"></div>
    <div class="content">
      <div class="logo"></div>
      <h3 class="title">金华火车站交通枢纽联合执法管理应用</h3>
      <p class="text">版本：<span id="version"></span>丨大小：18M</p>
      <p class="text">更新时间：<span id="date"></span></p>
      <div class="qrcode"></div>
      <div class="btn" onclick="download()">下载</div>
      <script>
        //步骤一:创建异步对象
        var ajax = new XMLHttpRequest();
        //步骤二:设置请求的url参数,参数一是请求的类型,参数二是请求的url,可以带参数,动态的传递参数starName到服务端
        ajax.open('get', '/zqzfj/business/app/version/1');
        //步骤三:发送请求
        ajax.send();
        //步骤四:注册事件 onreadystatechange 状态改变就会调用
        ajax.onreadystatechange = function () {
          if (ajax.readyState==4 &&ajax.status==200) {
            //步骤五 如果能够进到这个判断 说明 数据 完美的回来了,并且请求的页面是存在的
            var $version = document.getElementById('version')
            var $date = document.getElementById('date')
            var res = JSON.parse(ajax.responseText)
            $version.innerHTML = res.data.version
            $date.innerHTML = res.data.updateTime
          }
        }

        function download() {
          window.location.href = 'https://zqzf.xzzfj.jinhua.gov.cn/zqzfj/system/file/downLoad/zqzfj.apk'
        }
      </script>
</body>
</html>
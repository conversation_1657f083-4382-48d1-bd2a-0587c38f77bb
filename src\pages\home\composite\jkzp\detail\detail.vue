<template>
  <view>
    <top-supervise
      :case-id="form.captureId"
      case-type="capture"
      :status="form.status"
    />
    <u-form
      ref="uForm"
      :model="form"
      class="container u-border-top"
      label-width="150"
      :label-style="labelStyle"
    >
      <view class="p-lr-30">
        <u-form-item label="发起人:" prop="userName">
          <u-input v-model="form.userName" disabled placeholder="发起人" />
        </u-form-item>
        <u-form-item label="发生时间:" prop="happenTime">
          <u-input
            v-model="form.happenTime"
            disabled
            placeholder="请选择发生时间"
          />
        </u-form-item>
        <u-form-item label="案件类型:" prop="caseTypeName">
          <u-input
            v-model="form.caseTypeName"
            disabled
            placeholder="请选择案件类型"
          />
        </u-form-item>
        <u-form-item label="案件内容:" label-position="top">
          <u-input
            v-model="form.content"
            disabled
            type="textarea"
            :is-show-num="false"
            placeholder="请输入案件内容"
          />
        </u-form-item>
        <u-form-item
          label="发生地址:"
          prop="address"
          label-position="top"
          :border-bottom="false"
        >
          <u-input
            v-model="form.address"
            disabled
            type="textarea"
            :is-show-num="false"
            placeholder="请选择地址"
          />
        </u-form-item>
      </view>
      <u-gap height="20" bg-color="#F5F5F5"></u-gap>
      <view class="p-lr-30">
        <u-form-item
          label="内容描述:"
          prop="content"
          label-position="top"
          :border-bottom="false"
          required
        >
          <u-input
            v-model="form.content"
            disabled
            type="textarea"
            maxlength="300"
            height="140"
            placeholder="请输入内容描述..."
          />
        </u-form-item>
        <u-upload
          ref="happenfile"
          name="files"
          max-count="4"
          width="157"
          height="157"
          :header="header"
          :auto-upload="false"
          :action="action"
          :file-list="happenFile"
          :deletable="false"
          :custom-btn="true"
        ></u-upload>
      </view>
      <!-- 间隔 -->
      <u-gap height="20" bg-color="#F5F5F5"></u-gap>
      <view class="p-lr-30">
        <u-form-item label="当班组长:" prop="squadronUserName">
          <u-input
            v-model="form.squadronUserName"
            type="popup"
            placeholder="当班组长"
          />
        </u-form-item>
        <view v-if="form.status >= 3">
          <u-form-item
            label="执行人员:"
            prop="userNames"
            :border-bottom="false"
          >
            <u-input
              v-model="form.userNames"
              type="popup"
              placeholder="执行人员"
            />
          </u-form-item>
        </view>
      </view>
      <u-gap height="20" bg-color="#F5F5F5"></u-gap>
      <view v-if="form.status >= 5" class="p-lr-30">
        <u-form-item label="出警时间:" prop="outTime" required>
          <u-input v-model="form.outTime" type="popup" placeholder="出警时间" />
        </u-form-item>
        <u-form-item
          label="现场情况:"
          prop="doDescn"
          label-position="top"
          :border-bottom="false"
          required
        >
          <u-input
            v-model="form.doDescn"
            type="textArea"
            maxlength="300"
            height="140"
            placeholder="请输入现场情况..."
          />
        </u-form-item>
        <u-upload
          ref="descnFile"
          name="files"
          max-count="4"
          width="157"
          height="157"
          :header="header"
          :auto-upload="false"
          :action="action"
          :form-data="descnFormData"
          :size-type="['compressed']"
          :file-list="descnFile"
          :before-remove="handleRemove"
          @on-uploaded="handleAllUpload"
          @on-error="handleError"
        ></u-upload>
      </view>
      <u-gap height="20" bg-color="#F5F5F5"></u-gap>
      <view v-if="form.status >= 6" class="p-lr-30">
        <u-form-item label="到达时间:" prop="arriveTime" required>
          <u-input
            v-model="form.arriveTime"
            type="popup"
            placeholder="到达时间"
          />
        </u-form-item>
        <u-form-item
          label="处警结果:"
          prop="doResult"
          label-position="top"
          :border-bottom="false"
          required
        >
          <u-input
            v-model="form.doResult"
            type="textarea"
            maxlength="300"
            height="140"
            placeholder="请输入出警结果..."
          />
        </u-form-item>
        <u-upload
          ref="resultFile"
          name="files"
          max-count="4"
          width="157"
          height="157"
          :header="header"
          :auto-upload="false"
          :action="action"
          :form-data="resultFormData"
          :size-type="['compressed']"
          :file-list="resultFile"
          :before-remove="handleRemove"
          @on-uploaded="handleAllUpload"
          @on-error="handleError"
        ></u-upload>
      </view>
      <!-- 退回原因 -->
      <u-gap v-if="form.approveReason" height="20" bg-color="#F5F5F5"></u-gap>
      <view class="p-lr-30">
        <u-form-item
          v-if="form.approveReason"
          label="退回原因:"
          prop="approveReason"
          label-position="top"
          :border-bottom="false"
        >
          <u-input
            v-model="form.approveReason"
            type="textarea"
            disabled
            maxlength="300"
            height="140"
            placeholder="退回原因"
          />
        </u-form-item>
      </view>
    </u-form>
    <view class="btn-box u-border-top u-flex">
      <u-button
        v-if="isShowOut"
        type="primary"
        class="u-flex-1"
        shape="circle"
        :custom-style="subStyle"
        @click="handleSubmit(5)"
      >
        确认出警
      </u-button>
      <u-button
        v-if="isShowArrive"
        type="primary"
        class="u-flex-1"
        shape="circle"
        :custom-style="subStyle"
        @click="handleSubmit(6)"
      >
        到达现场
      </u-button>
      <u-button
        v-if="isShowOver"
        type="primary"
        class="u-flex-1"
        shape="circle"
        :custom-style="subStyle"
        @click="handleSubmit(6, false)"
      >
        暂存
      </u-button>
      <u-button
        v-if="isShowOver"
        type="primary"
        class="u-flex-1 u-m-l-24"
        shape="circle"
        :custom-style="subStyle"
        @click="handleOver"
      >
        办结
      </u-button>
    </view>
    <!-- 提示 -->
    <u-top-tips ref="uTips"></u-top-tips>
  </view>
</template>

<script>
import topSupervise from '@/components/top-supervise.vue'
export default {
  components: {
    topSupervise,
  },
  data() {
    return {
      form: {
        type: 1,
        squadronName: '违停',
        address: '浙江省金华市婺城区城北街道',
      },
      labelStyle: { color: '#808080', fontSize: '30rpx' },
      subStyle: { height: '86rpx', backgroundColor: '#327BF0' },
      caseTypeList: [],
      descnFormData: {
        tableName: 'case_capture',
        status: 6,
      },
      resultFormData: {
        tableName: 'case_capture',
        status: 9,
      },
      happenFile: [],
      descnFile: [],
      resultFile: [],
      rules: {
        doDescn: [
          { required: true, message: '请填写现场情况', trigger: 'change' },
        ],
        doResult: [
          { required: true, message: '请输入处警结果', trigger: 'change' },
        ],
      },
    }
  },
  computed: {
    action() {
      // #ifdef H5
      return `/prod-api/system/file/upload`
      // #endif
      // #ifndef H5
      return `${this.vuex_ip}/prod-api/system/file/upload`
      // #endif
    },
    header() {
      return {
        Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token'),
      }
    },
    isShowOut() {
      // 是否显示出警按钮
      return this.form.status == 4 && this.form.userIds.includes(this.vuex_id)
    },
    isShowArrive() {
      // 是否显示到达现场按钮
      return this.form.status == 5 && this.form.userIds.includes(this.vuex_id)
    },
    isShowOver() {
      // 是否显示办结和暂存按钮
      return this.form.status == 6 && this.form.userIds.includes(this.vuex_id)
    },
  },
  methods: {
    validateFn(isValid, status) {
      // 根据传入的状态来确定是否要进行验证，用以区别暂存和办结
      return new Promise((resolve, reject) => {
        if (isValid) {
          this.$refs.uForm.validate((valid) => {
            if (valid) {
              // 图片验证，没有图片不通过验证
              if (status >= 6 && !this.$refs.descnFile.lists.length) {
                this.$refs.uTips.show({
                  title: '请上传到达现场图片',
                  type: 'error',
                  duration: '2300',
                })
                reject()
              } else if (status >= 9 && !this.$refs.resultFile.lists.length) {
                this.$refs.uTips.show({
                  title: '请上传处理结果图片',
                  type: 'error',
                  duration: '2300',
                })
                reject()
              } else {
                resolve()
              }
            } else {
              reject()
            }
          })
        } else {
          resolve()
        }
      })
    },
    handleOver() {
      uni.showModal({
        title: '提示',
        content: '确定要办结此案件？',
        success: ({ confirm }) => {
          if (confirm) {
            this.handleSubmit(9)
          }
        },
      })
    },
    handleSubmit(status, isValid = true) {
      this.validateFn(isValid, status)
        .then((res) => {
          // 确认出警
          const parmas = { ...this.form, status }
          const timestamp = new Date().getTime()
          if (status == 5) {
            // 出警默认添加出警时间
            parmas.outTime = this.$u.timeFormat(
              timestamp,
              'yyyy-mm-dd hh:MM:ss'
            )
          }
          if (status == 6 && isValid) {
            // 到达现场默认添加到达现场时间
            parmas.arriveTime = this.$u.timeFormat(
              timestamp,
              'yyyy-mm-dd hh:MM:ss'
            )
          }
          if (status == 9) {
            // 办结添加办结时间
            parmas.fbTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
          }
          this.$loading()
          this.$u.api
            .captureEdit(parmas)
            .then((res) => {
              const isPass = this.allFileUpload()
              if (isPass) {
                uni.showToast({ title: '操作成功' })
                uni.hideLoading()
                this.$implement()
              }
            })
            .catch(() => {
              uni.hideLoading()
            })
        })
        .catch(() => {})
      /* if(state == 9) {
					uni.showModal({
						title: '提示',
						content: '确定要办结此案件？',
						success: ({ confirm }) => {
							if (confirm) {
								this.$refs.uForm.validate(valid => {
									if (valid) {
										if (this.$refs.happenfile2&&!this.$refs.happenfile2.lists.length) {
											this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
											return
										}
									}
								})
							} else {
								reject()
							}
						}
					})
				} */
    },
    handleRemove(index, lists) {
      const fileInfo = lists[index]
      const fileId = fileInfo.url.split('?id=')[1]
      if (fileId) {
        return new Promise((resolve, reject) => {
          uni.showModal({
            title: '提示',
            content: '删除后将无法恢复，是否确认删除？',
            success: ({ confirm }) => {
              if (confirm) {
                this.$u.api
                  .deleteFileList({}, fileId)
                  .then(resolve)
                  .catch(reject)
              } else {
                reject()
              }
            },
          })
        })
      } else {
        return true
      }
    },
    allFileUpload() {
      // 先检测是否还存在到达现场图片没有上传
      let isPass = false
      if (!this.$refs.descnFile) return true
      const uploadFile = this.$refs.descnFile.lists.some(
        (item) => item.progress !== 100
      )
      if (uploadFile) {
        this.$loading('现场图片上传中')
        this.$refs.descnFile.upload()
      } else if (this.$refs.resultFile) {
        const resUploadFile = this.$refs.resultFile.lists.some(
          (item) => item.progress !== 100
        )
        if (resUploadFile) {
          this.$loading('处警图片上传中')
          this.$refs.resultFile.upload()
        } else {
          isPass = true
        }
      } else {
        isPass = true
      }
      return isPass
    },
    handleAllUpload(lists) {
      // 所有文件上传成功，返回上一级页面
      const isFail = lists.some((item) => item.progress !== 100)
      if (!isFail) {
        uni.showToast({ title: '操作成功' })
        uni.hideLoading()
        this.$implement()
      }
    },
    handleError() {
      // 文件上传失败，弹出提示是否重新上传
      uni.hideLoading()
      uni.showModal({
        title: '提示',
        content: '图片上传失败，是否重新上传？',
        success: ({ confirm }) => {
          if (confirm) {
            this.allFileUpload()
          } else {
            this.$implement({ immediately: true })
          }
        },
      })
    },
  },
  onLoad(params) {
    this.$loading()
    Promise.all([
      this.$u.api.dictList({ dictType: 'case_capture_type' }),
      this.$u.api.getcapture({}, params.id),
      this.$u.api.getFileList({
        tableName: 'case_capture',
        businessId: params.id,
      }),
    ])
      .then((res) => {
        uni.hideLoading()
        // 数据字典数据
        this.caseTypeList = res[0].rows
        this.caseTypeList.forEach((v) => {
          if (v.dictLabel) v.text = v.dictLabel
          if (v.dictSort) v.type = v.dictSort
        })
        // 表单数据
        const formData = res[1].data
        const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
        this.form = { ...this.form, ...formData, lnglat }
        // 图片数据
        this.descnFormData.businessId = this.form.captureId
        this.resultFormData.businessId = this.form.captureId
        res[2].rows.map((v) => {
          const url = { url: `${this.vuex_ip}${v.filePath}?id=${v.fileId}` }
          if (v.status == 2) {
            this.happenFile.push(url)
          } else if (v.status == 6) {
            this.descnFile.push(url)
          } else if (v.status == 9) {
            this.resultFile.push(url)
          }
        })
      })
      .catch(() => {
        uni.hideLoading()
      })
  },
  onReady() {
    this.$refs.uForm.setRules(this.rules)
  },
}
</script>

<style lang="scss">
.p-lr-30 {
  padding: 0 30rpx;
  background-color: #fff;
}
.container {
  padding-bottom: 145rpx;
}
.btn-box {
  width: 100%;
  padding: 14rpx 30rpx;
  position: fixed;
  bottom: 0;
  background-color: #ffffff;
  z-index: 10;
}
</style>

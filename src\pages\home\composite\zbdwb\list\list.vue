<template>
	<view class="zbdwb">
		<zzx-calendar
			class="top-calendar"
			@selected-change="datechange"
			@change-mode="changeMode"
			@change-mon="changeMon"
		></zzx-calendar>
		<view v-if="isShowTable" class="content u-border-top" @touchstart="touchStart" @touchend="isScroll = true">
			<u-line color="#e4e7ed" class="top-line" :style="{ top: `${ cH }rpx` }" />
			<!-- 上部分显示地址 -->
			<scroll-view
				class="address-box u-border-bottom"
				:scroll-left="cellScrollLeft"
				:scroll-x="isScroll"
				:style="{ height: `${ cH }rpx`, paddingLeft: `${ hW }rpx` }"
			>
				<view :style="{ width: `${configData.address.length * cW}rpx`, overflow: 'hidden' }">
					<view
						class="address u-border-right"
						v-for="(item,index) in configData.address"
						:key="index"
						:style="{ width: `${ cW }rpx`, height: `${ cH }rpx`, lineHeight: `${ cH }rpx`, fontSize: `${ fS }rpx` }"
					>
						<text>{{ item.name }}</text>
					</view>
				</view>
			</scroll-view>
			<!-- 左边部分显示时刻 -->
			<scroll-view
				class="hour-box u-border-right"
				:scroll-top="cellScrollTop"
				:scroll-y="isScroll"
				:style="{ width: `${ hW }rpx`, paddingTop: `${ cH }rpx` }"
			>
				<view 
					class="hour u-border-bottom"
					v-for="(item,index) in configData.hour"
					:key="index"
					:style="{ width: `${ hW }rpx`, height: `${ cH }rpx`, lineHeight: `${ cH }rpx`, fontSize: `${ fS }rpx` }"
				>
					<text>{{ item.name }}</text>
				</view>
			</scroll-view>
			<view :style="{ width: '100%', height: '100%', boxSizing: 'border-box', paddingTop: `${ cH }rpx`, paddingLeft: `${ hW }rpx` }">
				<scroll-view
					id="cellContent"
					class="content-inner"
					:scroll-left="cellScrollLeft"
					:scroll-top="cellScrollTop"
					:scroll-x="true"
					:scroll-y="true"
					@scroll="handleScroll"
				>
					<view class="content-box" :style="{ width: `${configData.address.length * cW}rpx` }">
						<view
							class="content-inner-item u-border-right u-border-top"
							v-for="(item,index) in configData.cell"
							:key="index"
							:style="{ width: `${ cW }rpx`, height: `${ cH }rpx`, lineHeight: `${ cH }rpx`, fontSize: `${ fS }rpx` }"
						>{{ item.userName }}</view>
					</view>
				</scroll-view>
			</view>
		</view>
		<u-empty v-else text="暂无排班点位表" mode="history"></u-empty>
		<!-- 底部操作 -->
		<view class="footer">
			<view class="u-border-top u-p-t-30 u-p-b-30" style="background-color:#fff;overflow: hidden;">
				<u-slider v-model="sliderValue" min="1" class="u-m-l-40 u-m-r-40" @end="sliderEnd"></u-slider>
			</view>
			<view v-if="vuex_postKeys.includes('zz')" class="btn-box u-border-top u-flex u-row-between">
				<view class="u-flex-1">
					<u-button v-if="isShowTable" type="primary" shape="circle" :custom-style="subStyle" @click="$u.route({ url: 'pages/home/<USER>/zbdwb/detail/detail', params: { id: configData.hourHeaderId }})">修改值班</u-button>
					<u-button v-else type="primary" shape="circle" :custom-style="subStyle" @click="$u.route('pages/home/<USER>/zbdwb/detail/detail' )">新增值班</u-button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import zzxCalendar from "@/components/zzx-calendar/zzx-calendar.vue"
	
	export default {
		components: {
			zzxCalendar
		},
		data() {
			return {
				configData: {
					address: [],
					hour: [],
					cell: []
				},
				cellScrollTop: 0,
				cellScrollLeft: 0,
				isShowTable: false,
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				cellH: 100,
				cellW: 200,
				hourW: 120,
				scale: 1.5,
				sliderValue: 50,
				fontSize: 28,
				isScroll: true,
				nowDate: ''
			}
		},
		watch: {
			sliderValue() {
				this.scale = this.sliderValue * 2 * 0.01
			}
		},
		computed: {
			cW() {
				return this.cellW * this.scale
			},
			cH() {
				return this.cellH * this.scale
			},
			hW() {
				return this.hourW * this.scale
			},
			fS() {
				return this.fontSize * this.scale
			}
		},
		methods: {
			rePageData() {
				this.datechange({ fullDate: this.nowDate })
			},
			sliderEnd() {
				this.$u.vuex('vuex_slider', this.sliderValue)
			},
			touchStart(e) {
				const query = uni.createSelectorQuery().in(this);
				query.select('#cellContent').boundingClientRect(data => {
				  console.log("得到布局位置信息", data);
					const { clientX, clientY } = e.touches[0]
					const { left, top } = data
					this.isScroll = !!(clientX > left && clientY > top)
				}).exec();
			},
			datechange(e) {
				const { fullDate } = e
				this.nowDate = fullDate
				this.$loading()
				this.$u.api.getHourHeaderList({ distributeTime: fullDate, deptId: this.vuex_deptId }).then(res => {
					uni.hideLoading()
					if (res.rows[0] && res.rows[0].abscissa && res.rows[0].ordinate && res.rows[0].details.length) {
						let detailObj = {}
						res.rows[0].details.forEach(item => {
							detailObj[item.title] = { userName: item.userName }
						})
						this.configData.hourHeaderId = res.rows[0].hourHeaderId
						this.configData.address = JSON.parse(res.rows[0].abscissa)
						this.configData.hour = JSON.parse(res.rows[0].ordinate)
						let cellData = []
						this.configData.hour.forEach((hour, yIdx) => {
							this.configData.address.forEach((addres,xIdx) => {
								let userName = detailObj[`${xIdx},${yIdx}`] ? detailObj[`${xIdx},${yIdx}`].userName : ''
								cellData.push({
									xIdx,
									yIdx,
									userName,
									addres: addres.name,
									hour: hour.name
								})
							})
						})
						this.configData.cell = [...cellData]
						this.isShowTable = true
						console.log(this.configData)
					} else {
						this.isShowTable = false
					}
				}).catch(() => {
					this.isShowTable = false
					uni.hideLoading()
				})
			},
			changeMode(isWeek) {
				console.log('切换为周或者月',isWeek)
			},
			changeMon() {
				console.log()
			},
			handleScroll(e) {
				this.cellScrollTop = e.detail.scrollTop
				this.cellScrollLeft = e.detail.scrollLeft
			}
		},
		onLoad() {
			if (this.vuex_slider) this.sliderValue = this.vuex_slider
			const timestamp = new Date().getTime()
			const fullDate = this.$u.timeFormat(timestamp, 'yyyy-mm-dd')
			this.nowDate = fullDate
			this.datechange({ fullDate })
		}
	}
</script>

<style lang="scss">
	.zbdwb {
		display: flex;
		flex-direction: column;
		height: 100vh;
		font-size: 28rpx;
		/* #ifdef H5 */
		height: calc(100vh - 44px);
		/* #endif */
		.top-calendar {
			flex-shrink: 0;
		}
		.btn-box {
			width: 100%;
			padding: 14rpx 30rpx;
			background-color: #FFFFFF;
		}
		.footer {
			flex-shrink: 0;
		}
		.content {
			width: 100%;
			height: 0;
			background-color: #fff;
			flex: 1;
			position: relative;
			overflow: hidden;
			.top-line {
				position: absolute;
				left: 0;
				z-index: 20;
			}
			.address-box {
				width: 100%;
				position: absolute;
				top: 1rpx;
				left: 0;
				background-color: #fff;
				z-index: 10;
				box-sizing: border-box;
				.address {
					float: left;
					text-align: center;
				}
			}
			.hour-box {
				height: 100%;
				position: absolute;
				top: 1rpx;
				left: 0;
				background-color: #fff;
				z-index: 10;
				box-sizing: border-box;
				.hour {
					text-align: center;
				}
			}
			&-inner {
				width: 100%;
				height: 100%;
				overflow: auto;
				box-sizing: border-box;
				.content-box {
					overflow: hidden;
					.content-inner-item {
						text-align: center;
						float: left;
						border-color: #333;
					}
				}
			}
		}
	}
	// 按钮
	.bottom-btn {
		width: 220rpx;
		height: 88rpx;
		position: fixed;
		bottom: 40rpx;
		left: 50%;
		margin-left: -110rpx;
		background-color: #327BF0;
		color: #FFFFFF;
		border-radius: 88rpx;
		box-shadow: 0px 0px 14px 0px rgba(82, 97, 121, 0.3);
		transition: all 0.5s;
		z-index: 10;
		&:active {
			background-color: #73a6f7;
		}
		.text {
			font-size: 28rpx;
			margin-left: 15rpx;
		}
	}
</style>

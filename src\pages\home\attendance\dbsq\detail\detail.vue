<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="代班人:" prop="userName" required>
					<u-input v-model="form.userName" type="popup" placeholder="代班人" />
				</u-form-item>
				<u-form-item label="代班人部门:" prop="deptName" required>
					<u-input v-model="form.deptName" type="popup" placeholder="代班人部门" />
				</u-form-item>
				<u-form-item label="被代班人:" prop="substituteUserName" required>
					<u-input v-model="form.substituteUserName" type="popup" placeholder="被代班人" />
				</u-form-item>
				<u-form-item label="被代班人部门:" prop="substituteDeptName" required>
					<u-input v-model="form.substituteDeptName" type="popup" placeholder="被代班人部门" />
				</u-form-item>
				<u-form-item label="代班日期:" prop="happenTime" required>
					<u-input v-model="form.happenTime" type="popup" placeholder="请选择代班日期"  />
				</u-form-item>
				<u-form-item label="申请理由:" prop="title" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.title" type="textarea" placeholder="请输入申请理由" disabled />
				</u-form-item>	
			</view>
			
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30" v-if="form.approveType != null">
				<h3 style="padding: 20rpx 0;">督查审批 :</h3>
				<u-form-item label="审批人:">
					<u-input v-model="form.approveUserName" disabled />
				</u-form-item>
				<u-form-item label="是否通过:" required>
					<u-radio-group v-model="form.approveType" disabled>
						<u-radio name="1">通过</u-radio>
						<u-radio name="0">拒绝</u-radio>
					</u-radio-group>
				</u-form-item>
				
				<u-form-item label="审批理由:"  label-position="top" :border-bottom="false">
					<u-input v-model="form.approveReason" type="textarea" disabled placeholder="请输入审批理由"></u-input>
				</u-form-item>
			</view>
			
	
		</u-form>
		
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import gps from '@/common/gps.js'
	export default {
		data() {
			return {
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				form: {},
				showHappenTime: false
			}
		},
		computed: {
		
		},
		methods: {
			
		},
		async onLoad(params) {
			this.$loading()
			const standardRes = await this.$u.api.getSubstitute({}, params.id)
			
			/* 数据请求完毕 */
			uni.hideLoading()
			if (standardRes.code == 200) {
				/* 表单数据 */
				standardRes.data.happenTime = standardRes.data.happenTime.split(' 00:00:00')[0]
				this.form = standardRes.data
			}
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.pos-r {
	height: 50rpx;
	line-height: 50rpx;
	font-size: 24rpx;
	background-color: #2979ff;
	border-radius: 10rpx;
	position: absolute;
	top: 30rpx;
	right: 0;
	color: #FFFFFF;
	padding: 0 15rpx;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

<template>
	<view>
		<view class="banner">
			<u-image src="../../static/img/banner.png" width="750rpx" height="400rpx"></u-image>
		</view>
		<view class="content">
			<view class="logo" @longpress="showPopup = true">
				<u-image src="../../static/img/logo.png" width="160rpx" height="160rpx"></u-image>
			</view>
			<view class="title">
				<text class="t-title">金华火车站交通枢纽</text>
				<text class="b-title">联合执法管理应用</text>
			</view>
			<!-- 登录表单 -->
			<u-form :model="form" ref="uForm" :error-type="errorType" class="login-from" label-position="top">
				<view class="u-flex u-col-center u-border-bottom">
					<u-icon name="account-fill" :style="leftIconStyle"></u-icon>
					<u-form-item class="u-flex-1" prop="username" :border-bottom="false" :labelStyle="{ 'height': isShowUserLabel ? '50rpx' : 'auto', 'color': '#808080' }" :label="isShowUserLabel ? '账号' : ''">
						<u-input v-model="form.username" :height="isShowUserLabel ? 50 : 80" placeholder-style="color: #808080;" placeholder="请输入账号" @focus="handleFocus('isShowUserLabel')" @blur="handleBlur('isShowUserLabel')"/>
					</u-form-item>
				</view>
				<view class="u-flex u-col-center u-border-bottom">
					<u-icon name="lock-fill" :style="leftIconStyle"></u-icon>
					<u-form-item class="u-flex-1" prop="password" :border-bottom="false" :labelStyle="{ 'height': isShowPswLabel ? '50rpx' : 'auto', 'color': '#808080' }" :label="isShowPswLabel ? '密码' : ''">
						<u-input v-model="form.password" :height="isShowPswLabel ? 50 : 80" placeholder-style="color: #808080;" type="password" placeholder="请输入密码" @focus="handleFocus('isShowPswLabel')" @blur="handleBlur('isShowPswLabel')"/>
					</u-form-item>
				</view>
        <view class="u-flex u-col-center u-border-bottom">
          <u-form-item class="u-flex-1" prop="password" :border-bottom="false" :labelStyle="{ 'height': 'auto', 'color': '#808080' }">
            <u-input v-model="form.code" :height="50" placeholder-style="color: #808080;" placeholder="请输入验证码" @focus="handleFocus('isShowPswLabel')" @blur="handleBlur('isShowPswLabel')"/>
            <img :src="imgSrc" style="height: 76rpx" @click="handleCodeClick" />
          </u-form-item>
        </view>
				<u-form-item :border-bottom="false">
					<view style="width: 100%;" class="u-flex u-row-right">
						<u-checkbox v-model="form.isAutoLogin" active-color="#000"  shape="circle">记住密码</u-checkbox>
					</view>
				</u-form-item>
			</u-form>
			<!-- 提交 -->
			<u-button class="btn" :custom-style="customStyle" @click="handleSubmit">登录</u-button>
			<!-- IP修改 -->
			<u-popup v-model="showPopup" mode="bottom">
				<u-input type="text" v-model="ip" class="u-m-20"></u-input>
				<u-button type="primary" class="u-m-20" @click="handleLongPress">确认</u-button>
			</u-popup>
		</view>
	</view>
</template>

<script>
	import { AESEncrypt } from '@/common/aesutil.js'

	export default {
		data() {
			return {
				leftIconStyle: {
					color: '#4689F5',
					fontSize: "35rpx",
					marginRight: '30rpx'
				},
				customStyle: {
					fontSize: "36rpx",
					fontWeight: '500',
					height: '100rpx',
					margin: '20rpx 100rpx 0',
					backgroundColor: '#327BF0',
					color: '#fff',
					borderRadius: '16rpx'
				},
				errorType:['toast'],
				isShowUserLabel: false,
				isShowPswLabel: false,
				form: {
					username: '',
					password: '',
          code: ''
				},
				rules: {
					username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
					password: [{ required: true, message: '请输入密码', trigger: 'change' }],
          code: [{ required: true, message: '请输入验证码', trigger: 'change' }],
				},
				showPopup: false,
				ip: '',
				key: null,
        imgSrc:'',
        uuid: ""
			};
		},
		methods: {
			handleFocus(key) {
				this[key] = true
			},
			handleBlur(key) {
				if (this.form.username.trim() === '') {
					this[key] = false
				}
			},
			handleLongPress() {
				this.$u.vuex('vuex_ip', this.ip);
				plus.runtime.restart();
			},
      handleCodeClick()  {
        this.$u.api.getCaptchaImage().then(res => {
          if (res.code === 200) {
            this.imgSrc = `data:image/gif;base64,${res.img}`
            this.uuid = res.uuid
          } else {
            // 登录失败 提示信息
            uni.showToast({
              title: res.msg,
              duration: 2000,
              icon: 'none'
            })
          }
        })

      },
			getSyncKey() {
				return new Promise((resolve, reject) => {
					let url = '/zqzfj/key'
					// #ifndef H5
					url = `${this.vuex_ip}/zqzfj/key`
					// #endif
					uni.request({
						url,
						method: 'get',
						timeout: 10000,
						dataType: 'json',
						success: (res) => {
							resolve(res.data.msg)
						},
						fail: () => {
							uni.hideLoading()
							this.mToase('服务器或网络异常，请联系管理员')
							reject(null)
						}
					})
				})
			},
			handleSubmit() {
				this.$refs.uForm.validate(async valid => {
					if (valid) {
						const { username, password, code, isAutoLogin } = this.form
						let params = {username, password, code }
						// #ifndef H5
						let clientInfo = plus.push.getClientInfo()
						let systemInfo = uni.getSystemInfoSync()
						params.clientId = clientInfo.clientid;
						params.phoneModel = systemInfo.model;
						// #endif
						uni.showLoading({ title: '登录中...', mask: true })



            //新登陆接口带验证码
            this.$u.api.login({ ...params, uuid: this.uuid }).then(res => {
              //账号名称
              this.$u.vuex('vuex_username', username)
              //token
              this.$u.vuex('vuex_token', res.token)
              this.$u.api.getInfo().then(res2 => {
                //用户姓名
                this.$u.vuex('vuex_nickName', res2.user.nickName)
                //用户联系方式
                this.$u.vuex('vuex_phone', res2.user.phonenumber)
                //部门名称
                this.$u.vuex('vuex_deptName', res2.user.dept.deptName)
                this.$u.vuex('vuex_postKeys', res2.user.postKeys || '')
                this.$u.vuex('vuex_id', res2.user.userId || '')
                this.$u.vuex('vuex_deptId', res2.user.deptId || '')
              })
              if (isAutoLogin) {
                this.$u.vuex('vuex_password', password)
              } else {
                this.$u.vuex('vuex_password', '')
              }
              uni.reLaunch({
                url:"/pages/home/<USER>/index",
                success: () => {
                  uni.hideLoading()
                }
              })
              this.mToase('登录成功')
            }).catch(() => {
              this.handleCodeClick()
              uni.hideLoading()
            })


						// const paramsKey = AESEncrypt(JSON.stringify(params), 'faprwTsQM2S6k3p5')
						// this.$u.api.login({ requestData: paramsKey }).then(res => {
						// 	this.$u.vuex('vuex_username', username)
						// 	this.$u.vuex('vuex_token', res.token)
						// 	if (isAutoLogin) {
						// 		this.$u.vuex('vuex_password', password)
						// 	} else {
						// 		this.$u.vuex('vuex_password', '')
						// 	}
						// 	uni.reLaunch({
						// 		url:"/pages/home/<USER>/index",
						// 		success: () => {
						// 			uni.hideLoading()
						// 		}
						// 	})
						// 	this.mToase('登录成功')
						// }).catch(() => {
            //  this.handleCodeClick()
						// 	uni.hideLoading()
						// })


					}
				})
			}
		},
		onReady() {
			if (this.vuex_username) {
				this.form.username = this.vuex_username
				this.isShowUserLabel = true
			}
			if (this.vuex_password) {
				this.form.password = this.vuex_password
				this.isShowPswLabel = true
				this.form.isAutoLogin = true
			}
			this.$refs.uForm.setRules(this.rules);
			this.ip = this.vuex_ip
      this.handleCodeClick()
		}
	}
</script>

<style lang="scss" scoped>
.content {
	width: 750rpx;
	background-color: #fff;
	position: absolute;
	top: 304rpx;
	bottom: 0;
	border-radius: 58rpx 58rpx 0px 0px;
	.logo {
		position: absolute;
		top: -100rpx;
		left: 100rpx;
		padding: 20rpx;
		background-color: #fff;
		box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.16);
		border-radius: 50%;
	}
	.title {
		margin: 147rpx 0 0 100rpx;
		.t-title {
			display: block;
			font-size: 40rpx;
			color: #A2ACBC;
		}
		.b-title {
			font-size: 48rpx;
			color: #327BF0;
			font-weight: 700;
		}
	}
	.login-from {
		padding: 72rpx 100rpx 0;
	}
	.btn {

	}
}
</style>

<template>
	<view class="u-m-t-30 u-m-b-18">
		<view class="tab">
			<u-tabs-swiper ref="uTabs" :active-item-style="barStyle" inactive-color="#ffffff"
				bg-color="rgba(0,0,0,0)" font-size="24rpx" :show-bar="false" :list="list" :current="current"
				@change="tabsChange" :is-scroll="false" swiperWidth="100%"></u-tabs-swiper>
		</view>
		<view class="container">
			<swiper class="swiper" :current="swiperCurrent" @transition="transition"
				@animationfinish="animationfinish">
				<!-- 我的 -->
				<swiper-item class="swiper-item" v-for="(item, index) in ajlxData" :key="index">
					<ringUcharts v-if="item.data" :datas="item.data || []" :canvasId = "'canvas'+index"></ringUcharts>
					<u-empty  text="暂无数据" mode="list"></u-empty>
				</swiper-item>
			</swiper>
		</view>
	</view>
</template>

<script>
	import ringUcharts from '@/components/ring-ucharts.vue'
	export default{
		props:{
			ajlxData:{
				type:Array,
			}
		},
		components:{ringUcharts},
		data() {
			return{
				datas:[],
				current: 0,
				swiperCurrent: 0,
				styles:{width:"100%",height:'547rpx'},
				sIsFirst : true,
				pIsFirst : true,
				barStyle:{
					color: "#fff",
					background: 'linear-gradient(180deg, #29E2FA 0%, #4286F8 100%)',
				},
				list: [{name: '巡查发现'}, {name: '电子抓拍'}, {name: '违规处置'}],
			}
		},
		methods: {
			fetchData() {
		
			},
			tabsChange(index) {
				this.swiperCurrent = index;
			},
			transition(e) {
				let dx = e.detail.dx;
				this.$refs.uTabs.setDx(dx);
			},
			animationfinish(e) {
				let current = e.detail.current;
				this.$refs.uTabs.setFinishCurrent(current);
				this.swiperCurrent = current;
				this.current = current;
				
			},
		},
	}
</script>

<style lang="scss" scoped="scoped">
	.container {
		width: 100%;
		height: 607rpx;
		background: rgba(255, 255, 255, 0.08);
		color: #fff;
		padding: 30rpx;
		font-size: 24rpx;
		border-radius: 14px;
		margin: 18rpx 0 20rpx;
		.swiper {
			height: 100%;
		
			.swiper-item-inner {
				width: 100%;
				height: 607rpx;
		
			}
		}
		}
		::v-deep .u-tabs-scorll-flex{
			display: inline-block;
		}
</style>

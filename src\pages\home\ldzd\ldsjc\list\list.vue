<template>
  <view class="u-p-30 pagess">
    <view class="u-flex u-row-between">
      <view class="u-m-t-18 u-m-b-18 u-p-l-14 u-font-36 s-title">实时警情</view>
      <!-- <view class="u-font-26">更多<u-icon name="arrow-right-double"></u-icon> -->
      <!-- </view> -->
    </view>
    <ssjq :ssjqData="ssjqData"></ssjq>
    <view class="u-flex u-row-between">
      <view class="u-m-t-18 u-m-b-18 u-p-l-14 u-font-36 s-title">案件类型</view>
    </view>
    <ajlx :ajlxData="ajlxData"></ajlx>
    <!-- <view class="u-flex u-row-between ">
			<view class="u-m-t-18 u-m-b-18 u-p-l-14 u-font-36 s-title">出租车情况</view>
			<view class="u-font-26">更多<u-icon name="arrow-right-double"></u-icon>
			</view>
		</view>
		<czcqk :taxiData='taxiData'></czcqk> -->
    <view class="u-flex u-row-between">
      <view class="u-m-t-18 u-m-b-18 u-p-l-14 u-font-36 s-title">今日考勤</view>
    </view>

    <!-- 原有的统计图组件 -->
<!--    <jrkq :jrkqData="jrkqData" height="290rpx"></jrkq>-->

    <!-- 新的表格组件 -->
    <jrkq-table height="400rpx" ref="attendanceTable"></jrkq-table>
    <view class="u-flex u-row-between">
      <view class="u-m-t-18 u-m-b-18 u-p-l-14 u-font-36 s-title">
        志愿者统计
      </view>
    </view>
    <zyztj v-if="volData.sexCountData" :volData="volData"></zyztj>
    <view class="u-flex u-row-between">
      <view class="u-m-t-18 u-m-b-18 u-p-l-14 u-font-36 s-title">案件趋势</view>
    </view>
    <ajqs :ajqsData="ajqsData"></ajqs>
  </view>
</template>

<script>
import PageContent from '@/components/page-content.vue'
import ssjq from './components/ssjq.vue'
import jrkq from './components/jrkq'
import jrkqTable from './components/jrkq-table.vue'
import zyztj from './components/zyztj.vue'
import ajqs from './components/ajqs.vue'
import czcqk from './components/czcqk.vue'
import ajlx from './components/ajlx.vue'

export default {
  components: {
    PageContent,
    jrkq,
    zyztj,
    ajqs,
    czcqk,
    ssjq,
    ajlx,
    jrkqTable
  },
  data() {
    return {
      list: [
        { name: '行政执法中队' },
        { name: '运营中队' },
        { name: '管理科' },
        { name: '研发部门' },
      ],
      listParams: [{ listData: [] }, { listData: [] }],
      ssjqData: [],
      ajlxData: [],
      taxiData: [],
      jrkqData: [],
      volData: {},
      ajqsData: {},
    }
  },
  onLoad() {
    this.getData()
  },
  methods: {
    getData() {
      this.$loading()
      this.$u.api.listRealTimeCase({ pageNum: 1, pageSize: 3 }).then((res) => {
        this.ssjqData = res.rows
      })

      this.$u.api.caseTypeCount().then((res) => {
        const { captureData, insData, transData } = res.data
        this.ajlxData = [
          { data: captureData },
          { data: insData },
          { data: transData },
        ]
      })

      this.$u.api.taxiInOut({ pageNum: 1, pageSize: 3 }).then((res) => {
        this.taxiData = res.data
      })
      this.$u.api.inCount({ punchTime: '2021-08-03' }).then((res) => {
        this.jrkqData = res.data
        this.jrkqData.pop(1)
      })
      this.$u.api.countVolCount().then((res) => {
        const { jobCountData, sexCountData } = res.data
        this.volData = {
          jobCountData: jobCountData ? jobCountData : [],
          sexCountData: sexCountData ? sexCountData : [],
        }
      })
      this.$u.api.caseTrendCount().then((res) => {
        this.ajqsData = res.data
      })
      uni.hideLoading()
    },
  },
}
</script>

<style lang="scss" scoped>
.pagess {
  background-color: #1b345e;
  color: #fff;
}
.s-title {
  border-left: 8rpx #4689f5 solid;
  font-weight: bold;
}
::v-deep .u-tabs-scorll-flex {
  justify-content: center !important;
}
::v-deep .u-tabs-scorll-flex .u-tabs-item {
  flex: none !important;
  color: #fff;
  line-height: 60rpx;
  background: rgba(66, 134, 248, 0.3);
  // width: 140rpx;
  height: 60rpx;
  border-radius: 12rpx;
  margin-left: 20rpx;
}
</style>

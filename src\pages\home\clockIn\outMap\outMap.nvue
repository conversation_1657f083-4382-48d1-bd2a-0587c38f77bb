<template>
	<view>
		<map
			id="map"
			ref="map"
			class="map"
			:scale="scale"
			:longitude="longitude"
			:latitude="latitude"
			:polygons="polygons"
			:show-location="true"
			:include-points="includePoints"
			:style='{ "width": "750rpx", "height": `${windowHeight}px` }'
		>
			<cover-view class="bottom-box" v-if="schedulingDetailId">
				<cover-view class="btn" @click="handleClick">
					<text class="text">开始巡查</text>
				</cover-view>
			</cover-view>
		</map>
	</view>
</template>

<script>
	import gps from '@/common/gps.js'
	
	export default {
		data() {
			return {
				mapContext: null,
				longitude: 119.635857,
				latitude: 29.110764,
				windowHeight: 0,
				scale: 18,
				includePoints: [],
				polygons: [],
				schedulingDetailId: '',
				address: ''
			}
		},
		methods: {
			moveCenter() {
				getApp().$loading('获取定位中')
				uni.getLocation({
					type: 'gcj02',
					geocode: true,
					success: res => {
						uni.hideLoading()
						this.longitude = res.longitude;
						this.latitude = res.latitude;
						if (res.address) this.address = `${res.address.city}${res.address.district}${res.address.street}${res.address.streetNum}${res.address.poiName}`
					},
					fail: () => {
						getApp().mToase('定位获取失败')
						uni.hideLoading()
					}
				})
			},
			handleClick() {
				let params = {}
				const timestamp = new Date().getTime()
				params.patrolTime = getApp().$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				params.schedulingDetailId = this.schedulingDetailId
				if (this.longitude && this.latitude) {
					const lnglat = gps.gcj_decrypt(this.latitude, this.longitude)
					params.latitude = lnglat.lng
					params.longitude = lnglat.lat
				} 
				if (this.address) params.address = this.address 
				getApp().$loading()
				getApp().$u.api.addFencePatrol(params).then(res => {
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					getApp().$implement()
				}).catch(() => {
					uni.hideLoading()
				})
			}
		},
		onLoad(params) {
			console.log(params)
			if (params.schedulingDetailId) this.schedulingDetailId = params.schedulingDetailId
			if (params.area) {
				// 遍历返回组好的打卡区域
				const polygons = params.area.split('/').map(poy => {
					return poy.split(';').map(lnglat => {
						const ll = lnglat.split(',')
						return { longitude: ll[0], latitude: ll[1] }
					})
				})
				this.polygons = polygons.map(points => {
					return {
						points,
						strokeWidth: 2,
						strokeColor: '#04a9e7',
						fillColor: '#04a9e74D'
					}
				})
				console.log(polygons)
			}
			this.moveCenter();
		},
		onReady() {
			const res = uni.getSystemInfoSync();
			this.windowHeight = res.windowHeight;
			this.mapContext = uni.createMapContext('map', this)
		}
	}
</script>

<style lang="scss">
.bottom-box {
	width: 750rpx;
	padding: 14rpx 30rpx;
	background-color: #FFFFFF;
	position: fixed;
	bottom: -1px;
	left: 0;
	.btn {
		width: 690rpx;
		height: 85rpx;
		background-color: #327BF0;
		border-radius: 85rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		&:active {
			opacity: 0.3;
		}
		.text {
			color: #fff;
			font-size: 30rpx;
		}
	}
}
</style>

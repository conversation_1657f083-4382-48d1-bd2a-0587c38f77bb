<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<!-- <u-form-item label="巡查对象:" prop="obj" >
					<u-radio-group v-model="objValue">
						<u-radio
							v-for="(item, index) in objList" :key="index"
							:name="item.id"
							:disabled="item.disabled"
						>
							{{item.name}}
						</u-radio>
					</u-radio-group>
				</u-form-item> -->
				<!-- <u-form-item label="标题:" prop="inspectionName" required>
					<u-input v-model="form.inspectionName" placeholder="请输入标题" />
				</u-form-item> -->
				<!-- <view v-if="objValue == 1">
					<u-form-item label="店铺名称:" prop="businessName" required>
						<u-input v-model="form.businessName" type="popup" placeholder="请扫码自动完成填写" />
						<view class="u-m-l-20">
							<u-icon name="scan"  @click="business"  color="#2979ff" size="36"></u-icon>
						</view>
					</u-form-item>
					<u-form-item label="营业执照号码:" prop="businessNo" required>
						<u-input v-model="form.businessNo" type="popup" placeholder="请扫码自动完成填写" />
					</u-form-item>
				</view> -->
				<u-form-item label="车牌号:" prop="contact" required>
					<u-input v-model="form.contact" type="text"  placeholder="请输入车牌号"  />
				</u-form-item>
				<u-form-item label="联系电话:" prop="phone">
					<u-input v-model="form.phone" placeholder="请输入人员联系电话" />
				</u-form-item>
				<u-form-item label="性别:" prop="sex" required>
					<u-radio-group v-model="form.sex"  >
						<u-radio
							v-for="(item, index) in sexList" :key="index"
							:name="item.id"
							:disabled="item.disabled"
						>
							{{item.name}}
						</u-radio>
					</u-radio-group>
				</u-form-item>

				<u-form-item label="发生时间:" prop="happenDate" required>
					<u-input v-model="form.happenDate" type="popup" placeholder="请选择发生时间" @click="showHappenDate = true"  />
					<u-icon name="calendar"  @click="showHappenDate = true"  size="40"></u-icon>
					<u-picker v-model="showHappenDate" mode="time" :params="params" :default-time="form.happenDate" @confirm="handleHpDateCon"></u-picker>
				</u-form-item>
				<u-form-item label="发生地址:" prop="address" required>
					<u-input v-model="form.address" type="text" placeholder="请选择地址" />
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseLocation">选择地址</u-button>
				</u-form-item>
				<!-- <u-form-item label="经纬度:" prop="lnglat" :border-bottom="false" required>
					<u-input v-model="form.lnglat" type="popup" disabled  placeholder="请选择地址"/>
				</u-form-item> -->
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="巡查人员:" prop="userName" required>
					<u-input v-model="form.userName" type="popup" placeholder="请选择巡查人员"/>
					<!-- <u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('userId')">选择人员</u-button> -->
				</u-form-item>
				<u-form-item label="辅助人员:" prop="userNames">
					<u-input v-model="form.userNames" type="popup" placeholder="请选择辅助人员"/>
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('userIds')">选择人员</u-button>
				</u-form-item>
				<!-- <u-form-item label="是否有问题:" prop="isProblem" :border-bottom="form.isProblem == 1">
					<u-radio-group v-model="form.isProblem">
						<u-radio name="1" >有</u-radio>
						<u-radio name="0" >无</u-radio>
					</u-radio-group>
				</u-form-item> -->
			</view>
			<view>
				<view class="p-lr-30">
					<!-- <u-form-item label="警情类型:" prop="policeCategoryName" required>
						<u-input v-model="form.policeCategoryName" type="select" :select-open="showList" placeholder="请选择警情类型" @click="showList = true" />
						<u-action-sheet v-model="showList" :list="categoryList" @click="handlePoliceClick"></u-action-sheet>
					</u-form-item> -->
					<u-form-item label="警情类别:" prop="policeTypeName" required>
						<u-input v-model="form.policeTypeName" type="select" :select-open="showTypeList" placeholder="请选择警情类别" @click="showTypeList = true" />
						<u-action-sheet v-model="showTypeList" :list="typeList" @click="handleTypeClick"></u-action-sheet>
					</u-form-item>
					<u-form-item label="案由名称:" prop="summaryName" required :border-bottom="false">
						<u-input v-model="form.summaryName" type="popup" placeholder="请选择案由"/>
						<u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('summaryId')">选择案由</u-button>
					</u-form-item>
					<!-- <u-form-item label="指派时间:" prop="assignDate" required>
						<u-input v-model="form.assignDate" type="popup" placeholder="请选择指派时间" @click="showAssignDate = true"  />
						<u-picker v-model="showAssignDate" mode="time" :params="params"  @confirm="handleAssignDate"></u-picker>
					</u-form-item> -->
				</view>
				<u-gap height="20" bg-color="#F5F5F5"></u-gap>
				<view class="p-lr-30">
					<!-- <u-form-item label="处理类型:" prop="handleTypeName" required>
						<u-input v-model="form.handleTypeName" type="select" :select-open="showhandleTypeList" placeholder="请选择警情类别" @click="showhandleTypeList = true" />
						<u-action-sheet v-model="showhandleTypeList" :list="handleTypeList" @click="handleHandleTypeClick"></u-action-sheet>
					</u-form-item> -->
					<view class="u-rela">
						<u-form-item label="警情内容:" prop="policeContent" label-position="top" :border-bottom="false" required>
							<u-input v-model="form.policeContent" type="textarea" maxlength="300" height="140" placeholder="请输入警情内容..."/>
						</u-form-item>
						<view class="pos-r" @click="handleConfirm">回填</view>
					</view>
				</view>
			</view>
			<!-- <view class="p-lr-30">
				<u-form-item label="是否店铺巡查:" prop="isTemporary" :border-bottom="form.isTemporary == 1">
					<u-radio-group v-model="form.isTemporary"  >
						<u-radio name="1" >是</u-radio>
						<u-radio name="0" >否</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="店铺巡查:" prop="temporary" required :border-bottom="false" v-if="form.isTemporary == 1">
					<u-input v-model="form.temporaryName" type="select" :select-open="showTemporaryList" placeholder="请选择店铺巡查" @click="showTemporaryList = true" />
					<u-action-sheet v-model="showTemporaryList" :list="temporaryList" @click="handleTemporaryClick"></u-action-sheet>
				</u-form-item>
			</view> -->
		<!-- 	<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="是否联合执法:" prop="isUnion" :border-bottom="form.isUnion == 1">
					<u-radio-group v-model="form.isUnion" >
						<u-radio name="1" >是</u-radio>
						<u-radio name="0" >否</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="联合执法:" prop="unionName" required :border-bottom="false" v-if="form.isUnion == 1">
					<u-input v-model="form.unionName" type="select" :select-open="showunionList" placeholder="请选择联合执法" @click="popupUnionShow = true" />
				</u-form-item>
			</view> -->
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<!-- <u-form-item label="内容描述:" prop="inspectionContent" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.inspectionContent" type="textarea" maxlength="300" height="140" placeholder="请输入内容描述..."/>
				</u-form-item> -->
				<u-form-item label="现场情况（需要有车辆照片，执法队员照片，抄牌单据照片）:" label-position="top">
					<u-upload
						ref="happenfile"
						name="files"
						max-count="4"
						width="157"
						height="157"
						:header="header"
						:auto-upload="false"
						:action="action"
						:form-data="happenData"
						:size-type="['compressed']"
						:file-list="happenFile"
						:before-remove="handleRemove"
						@on-uploaded="handleAllUpload"
						@on-error="handleError"
					></u-upload>
				</u-form-item>
			</view>

			<!-- 处理结果 -->
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="处理结果:" prop="inspectionContent" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.inspectionContent" type="textarea" maxlength="300" height="140" placeholder="请输入处理结果..."/>
				</u-form-item>
				<u-upload
					ref="resfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
					:size-type="['compressed']"
					:file-list="resFile"
					:before-remove="handleRemove"
					@on-uploaded="handleAllUpload"
					@on-error="handleError"
				></u-upload>
			</view>

		</u-form>
		<u-popup v-model="popupUnionShow" mode="bottom" length="60%">
				<view class="" >
					<view class="popup" v-for="(item,i) in unionLists" :key="i" @click="handUnion(item)">
						<view class="u-m-24">
							<view class="lp">
								任务名称： <text>{{item.title}}</text>
							</view>
							<view class="lp">
								开始时间：<text>{{item.startTime}}</text>
							</view>
							<view class="lp">
								结束时间：<text>{{item.endTime}}</text>
							</view>
						</view>
						<u-line></u-line>
					</view>

				</view>
		</u-popup>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex u-row-between">
			<view class="u-flex-1">
				<u-button type="primary" shape="circle" :custom-style="subStyle" @click="handleSubmit(2)">暂存</u-button>
			</view>

			<view class="u-flex-1 u-m-l-20">
				<u-button type="primary" shape="circle" :custom-style="subStyle" @click="handleEdit(9)">办结</u-button>
			</view>
		</view>
		<!-- 提示 -->

		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import gps from '@/common/gps.js'
	export default {
		data() {
			return {
				popupUnionShow:false,
				unionLists : [],
				objList:[{id:1,name:'店铺'},{id:2,name:'其他'}],
				sexList:[{id:1,name:'男'},{id:2,name:'女'}],
				isList:[{id:1,name:'是'},{id:0,name:'否'}],
				objValue:1,
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				showList: false,
				showTypeList: false,
				showunionList:false,
				showTemporaryList:false,
				typeList: [],
				categoryList:[],
				unionList:[],
				temporaryList:[],
				showHappenDate: false,
				showAssignDate:false,
				form: {address:""},
				rules: {
					// inspectionName: [{ required: true, message: '请输入标题', trigger: 'blur' }],
					inspectionContent: [{ required: true, message: '请输入内容', trigger: 'blur' }],
					typeText: [{ required: true, message: '请选择类型', trigger: 'change' }],
					userName: [{ required: true, message: '请选择巡查人员', trigger: 'change' }],
					contact: [{ required: true, message: '请填写车牌号', trigger: 'change' }],
					// phone: [
					// 	// { required: true, message: '请输入当事人联系电话', trigger: 'blur' },
					// 	{
					// 		validator: (rule, value, callback) => {
					// 			return this.$u.test.mobile(value);
					// 		},
					// 		message: '手机号码不正确',
					// 		trigger: ['change','blur'],
					// 	}
					// ],
					happenDate: [{ required: true, message: '请选择发生时间', trigger: 'change' }],
					address: [{ required: true, message: '请选择地址', trigger: 'change' }],
					lnglat: [{ required: true, message: '请选择地址', trigger: 'change' }],
					// userNames: [{ required: true, message: '请选择处理人员', trigger: 'change' }],
					businessNo: [{ required: true, message: '请输入营业执照号码', trigger: 'change' }],
					businessName:[{ required: true, message: '请输入店铺名称', trigger: 'change' }],
					policeCategoryName: [{ required: true, message: '请选择警情类型', trigger: 'change' }],
					policeTypeName:[{ required: true, message: '请选择警情类别', trigger: 'change' }],
					summaryName:[{ required: true, message: '请选择案由名称', trigger: 'change' }],
					handleTypeName:[{ required: true, message: '请选择处理类型', trigger: 'change' }],
					policeContent:[{ required: true, message: '请输入警情内容', trigger: 'change' }],
				},
				happenData: {
					tableName: 'case_inspection',
					status: 2
				},
				happenFile: [],
				resFile: [],
				showhandleTypeList: false,
				handleTypeList: [
					{type:1,text:'当场整改'},
					{type:2,text:'简易处罚'}
				]
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			handUnion(data){
				this.form.unionId = data.unionId
				this.form.unionName = data.title
				this.popupUnionShow = false
			},
			handleConfirm() {
				const text = `${this.form.happenDate || ''}，金华市婺城区综合行政执法局站前中队执法队员${this.form.userName || ''}${this.form.userNames ? ',' + this.form.userNames : ''}巡查中发现在${this.form.address || ''}存在${this.form.summaryName || ''}。经初步查实，该行为是${this.form.contact || ''}所为。`
				this.form = { ...this.form, policeContent: text }
			},
			handleHandleTypeClick(idx) {
				this.form = { ...this.form, handleTypeName: this.handleTypeList[idx].text, handleType: this.handleTypeList[idx].type }
			},
			handleTypeClick(idx) {
				const { dictValue, dictLabel } = this.typeList[idx]
				this.form = { ...this.form, policeType:dictValue, policeTypeName: dictLabel }
			},
			handlePoliceClick(idx){
				const { dictValue, dictLabel } = this.categoryList[idx]
				this.form = { ...this.form, policeCategory:dictValue, policeCategoryName: dictLabel }
			},
			handleTemporaryClick(idx){
				const { dictValue, dictLabel } = this.temporaryList[idx]
				this.form = { ...this.form, temporaryId:dictValue, temporaryName: dictLabel }
			},
			business(){
				let _this = this
				uni.scanCode({
				    success(res) {
						_this.$u.api.shopScanCode({},res.result).then(ress=>{
							let data = ress.data
							_this.form  = {..._this.form, businessNo:data.businessNo, businessName:data.shopName, shopId:data.shopId }
						})
				    },
					fail(err){
						uni.showToast({
							title: '扫码失败，请重试',
							icon: 'err',
							position: 'bottom'
						})
						console.log(err);
					}
				});
			},
			handleChooseLocation() {
				uni.chooseLocation({
					latitude: 29.110764,
					longitude: 119.635857,
					success: res => {
						const { address, longitude, latitude } = res
						const lnglat = gps.gcj_decrypt(parseFloat(latitude), parseFloat(longitude))
						this.form = { ...this.form, address: "浙江省金华市婺城区城北街道" + address, longitude: lnglat.lng, latitude: lnglat.lat, lnglat: `${lnglat.lng}, ${lnglat.lat}` }
					},
					fail: () => {
						uni.showToast({
							title: '地图打开失败',
							icon: 'none',
							position: 'bottom'
						})
					}
				})
			},
			handleHpDateCon(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, happenDate: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleAssignDate(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, assignDate: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleChooseHandleUser(type) {
				let params = {}
				if (this.form[type]) {
					params.defaultCheckedKeys = this.form[type]
					params.defaultExpandedKeys = this.form[type]
				}
				if(type == 'summaryId'){
					params.name = 'anyou'
				}
				if(type == 'userIds') params.showRadio = 0
				params.type = type
				this.$u.route({ url: 'pages/common/selectUser/selectUser', params })
			},
			setUserData(checks,type) {
				// 选择好人员后的回调
				let checkData = {}
				if(checks.length == 1){
					checkData = checks[0]
				}else{
					checks.map((v,i)=>{
						if(i==0) {
							checkData.label = v.label+''
							checkData.id = v.id+''
						}else{
							checkData.label += ',' + v.label+''
							checkData.id += ',' + v.id+''
						}
					})
				}
				if(type == 'userIds'){
					if (checkData) this.form = { ...this.form, userNames: checkData.label, userIds: checkData.id }
				}else if(type == 'summaryId'){
					if (checkData) this.form = { ...this.form, summaryName: checkData.label, summaryId: checkData.id }
				}else {
					if (checkData) this.form = { ...this.form, userName: checkData.label, userId: checkData.id }
				}
			},
			handleRemove(index, lists) {
				const fileInfo = lists[index]
				const fileId = fileInfo.url.split('?id=')[1]
				if (fileId) {
						return new Promise((resolve, reject) => {
							uni.showModal({
								title: '提示',
								content: '删除后将无法恢复，是否确认删除？',
								success: ({ confirm }) => {
									if (confirm) {
										this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
									} else {
										reject()
									}
								}
							})
						})
				} else {
					return true
				}
			},
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				if (!isFail && this.happenData.status == 2) {
					this.happenData.status = 9
					this.$loading('处理结果图片上传中')
					this.$refs.resfile.upload()
					return
				}
				if (!isFail) {
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					this.$implement()
				}
			},
			handleError() {
				// 文件上传失败，弹出提示是否重新上传
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					content: '图片上传失败，是否重新上传？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading('图片上传中')
							this.$refs.happenfile.reUpload()
						} else {
							this.$implement({ immediately: true })
						}
					}
				})
			},
			handleSubmit(state) {
				let params = { ...this.form,isProblem:'1', status: state,type:2 }
				// if (params.isProblem != 1) {
				// 	params.status = 9
				// 	params.inspectionContent = '正常巡查'
				// }
				// this.$refs.uForm.validate(valid => {
				// 	if (valid) {
						// 图片验证，没有图片不通过验证
						// if (!this.$refs.happenfile.lists.length) {
						// 	this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
						// 	return
						// }
						// 开始上传
						this.$loading('数据上传中')
						this.$u.api.inspectionAdd(params).then(res => {
							// 遍历列表，查询是否有未上传的图片
							const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
							this.happenData.businessId = res.data.inspectionId
							if (uploadFile) {
								this.$loading('图片上传中')
								this.$refs.happenfile.upload()
							} else {
								uni.showToast({title: '操作成功'})
								uni.hideLoading()
								this.$implement()
							}
						}).catch((err) => {
							console.log(err);
							uni.hideLoading()
						})
				// 	}
				// });
			},
			handleEdit(state) {
				// const params = { ...this.form,isProblem:1,status:state}
				this.$refs.uForm.validate(valid => {
					if (valid) {
						// 图片验证，没有图片不通过验证
						if (!this.$refs.happenfile.lists.length) {
							this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
							return
						}
						if (!this.$refs.resfile.lists.length) {
							this.$refs.uTips.show({ title: '请处理结果图片', type: 'error', duration: '2300' })
							return
						}

						this.handleSubmit(state)
						// 开始上传
						// this.$loading('数据上传中')
						// this.$u.api.inspectionAdd(params).then(res => {
						// 	// 遍历列表，查询是否有未上传的图片
						// 	const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
						// 	this.happenData.businessId = res.data.inspectionId
						// 	console.log(uploadFile);
						// 	if (uploadFile) {
						// 		this.$loading('图片上传中')
						// 		this.$refs.happenfile.upload()
						// 	} else {
						// 		uni.showToast({title: '操作成功'})
						// 		uni.hideLoading()
						// 		this.$implement()
						// 	}
						// }).catch(() => {
						// 	uni.hideLoading()
						// })
					}
				});
			}
		},
		onLoad(params) {
				this.$loading()
				Promise.all([
					this.$u.api.dictList({dictType:"case_alert_type"}),//警情类别
					this.$u.api.dictList({dictType:"case_call_type"}),//警情类型
					this.$u.api.unionList({status :2, searchDeptId: this.vuex_deptId})//联合执法
				]).then(res=>{
					this.typeList = res[0].rows
					this.categoryList= res[1].rows
					this.unionLists = res[2].rows
					this.categoryList.forEach(v=>{
						if(v.dictLabel) v.text = v.dictLabel
						if(v.dictValue) v.type = v.dictValue
					})
					this.typeList.forEach(v=>{
						if(v.dictLabel) v.text = v.dictLabel
						if(v.dictValue) v.type = v.dictValue
					})
					if (params.id) {
						Promise.all([
							this.$u.api.getInspection({}, params.id),
							this.$u.api.getFileList({ tableName: 'case_inspection', businessId: params.id }),
						]).then(resAry => {
							const formData = resAry[0].data
							if(!formData.shopId) this.objValue = 2
							else this.objValue = 1
							let typeTexts = this.typeList.find(item => item.dictValue == formData.policeType)
							let policeCategory = this.categoryList.find(item => item.dictValue == formData.policeCategory)
							// let union = this.unionLists.find(item => item.unionId == formData.unionId)
							const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
							this.form = { ...formData, policeTypeName:typeTexts? typeTexts.text:'',	policeCategoryName:policeCategory?policeCategory.text:'', lnglat }
							this.happenFile = resAry[1].rows.map(item => {
								return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
							})
							uni.hideLoading()
						}).catch((err) => {
							console.log(err);
							uni.hideLoading()
							uni.showToast({title: '加载失败', icon: 'error'})
						})
						uni.hideLoading()
					} else {
						const timestamp = new Date().getTime()
						const happenDate = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
						this.form = {sex:1,isProblem:1,policeType:'7',policeTypeName:'公安交通',summaryId:644,summaryName:'在人行道违法停放机动车影响其他车辆和行人通行案',isTemporary:0,isUnion:0, happenDate, userName: this.vuex_nickName, userId: this.vuex_id }
					}
					uni.hideLoading()
				}).catch((err)=>{
					console.log(err);
					uni.hideLoading()
					uni.showToast({title: '加载失败', icon: 'error'})
				})


		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.pos-r {
	height: 50rpx;
	line-height: 50rpx;
	font-size: 24rpx;
	background-color: #2979ff;
	border-radius: 10rpx;
	position: absolute;
	top: 30rpx;
	right: 0;
	color: #FFFFFF;
	padding: 0 15rpx;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
.popup{
	// padding: 12rpx 24rpx;
	// margin-bottom: 12rpx;
}
.lp{
	padding: 10rpx;
}
.lp text{
	margin-left: 24rpx;
}
</style>

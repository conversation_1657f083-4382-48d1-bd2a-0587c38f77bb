<template>
	<view style="overflow: hidden;">
		<view class="banner">
			<text class="title">我的</text>
			<view class="my-info u-flex">
				<u-avatar :src="userInfo.avatar || avatar" :size="120" @click="previewAvatarImage" style="border: 3rpx solid #fff;"></u-avatar>
				<!-- <view >
					<u-image :src="userInfo.avatar || avatar" width="120rpx" height="120rpx" style="border: 3rpx solid #fff; border-radius: 50%;"></u-image>
				</view> -->
				<view class="u-m-l-40">
					<text class="username u-m-b-18">{{ userInfo.nickName }}</text>
					<text>手机号丨{{ userInfo.phonenumber }}</text>
				</view>
			</view>
			<u-image src="@/static/img/my_banner.png" width="750rpx" height="400rpx"></u-image>
		</view>
		<view class="container">
			<u-cell-group :border="false">
				<u-cell-item title="个人中心" :border-bottom="false" :arrow="true" @click="$u.route({ url: 'pages/my/perCenter/perCenter' })">
					<u-image slot="icon" src="@/static/img/my-user.png" width="48" height="48" class="u-m-r-30"></u-image>
				</u-cell-item>
				<u-cell-item title="修改密码" :border-bottom="false" :arrow="true" @click="$u.route({ url: 'pages/my/changePsd/changePsd' })">
					<u-image slot="icon" src="@/static/img/my-psd.png" width="48" height="48" class="u-m-r-30"></u-image>
				</u-cell-item>
				<u-cell-item title="惯用语设置" :border-bottom="false" :arrow="true" @click="$u.route({ url: 'pages/my/myWords/myWords' })">
					<u-image slot="icon" src="@/static/img/my-words.png" width="48" height="48" class="u-m-r-30"></u-image>
				</u-cell-item>
				<u-cell-item title="通知公告" :border-bottom="false" :arrow="true" @click="$u.route({ url: 'pages/my/notice/list/list' })">
					<u-image slot="icon" src="@/static/img/notice.png" width="48" height="48" class="u-m-r-30"></u-image>
				</u-cell-item>
				<u-cell-item title="工作统计" :border-bottom="false" :arrow="true" @click="$u.route({ url: 'pages/my/myWorkReport/myWorkReport' })">
					<u-image slot="icon" src="@/static/img/my-work-report.png" width="48" height="48" class="u-m-r-30"></u-image>
				</u-cell-item>
				<u-cell-item title="工作日志" :border-bottom="false" :arrow="true" @click="$u.route({ url: 'pages/my/workLog/list/list' })">
					<u-image slot="icon" src="@/static/img/my-work-log.png" width="48" height="48" class="u-m-r-30"></u-image>
				</u-cell-item>
				<!-- <u-cell-item title="系统保活设置" :border-bottom="false" :arrow="false" @click="handleKeepAlive">
					<u-image slot="icon" src="@/static/img/my-keep.png" width="48" height="48" class="u-m-r-30"></u-image>
				</u-cell-item> -->
				<u-cell-item title="检查更新" :value="vuex_version" :border-bottom="false" :arrow="false" @click="handleUpdate">
					<u-image slot="icon" src="@/static/img/my-update.png" width="48" height="48" class="u-m-r-30"></u-image>
				</u-cell-item>
				<u-cell-item title="注销登录" :border-bottom="false" :arrow="true" @click="handleLogout">
					<u-image slot="icon" src="@/static/img/my-logout.png" width="48" height="48" class="u-m-r-30"></u-image>
				</u-cell-item>
			</u-cell-group>
			<!-- <h2>账号：</h2>
			<view>cid: <u-input v-model="cid" disabled></u-input></view>
			<view><text>phoneModel: <u-input v-model="phoneModel" disabled></u-input></text></view> -->
			<!-- <u-button type="primary" @click="handleLogout" class="u-m-t-50">退出登录</u-button> -->
		</view>
	</view>
</template>

<script>
	import avatar from '@/static/img/avatar.png'
	import { closeWebSocket } from '@/common/websocket.js'
	
	export default {
		data() {
			return {
				cid: '',
				phoneModel: '',
				avatar: avatar,
				userInfo: {}
			}
		},
		methods: {
			handleKeepAlive() {
				const keepAliveModule = uni.requireNativePlugin('Lyuan-KeepAlive'); 
				keepAliveModule.showSafeSetting();
				
				if(keepAliveModule.checkIfLimited()){
				    keepAliveModule.requestIgnoreLimit();
				} 
			},
			fetchData() {
				this.$loading()
				this.$u.api.getUserProfile().then(res => {
					uni.hideLoading()
					uni.stopPullDownRefresh()
					if (res.data.avatar) res.data.avatar = `${this.vuex_ip}/zqzfj${res.data.avatar}`
					this.userInfo = res.data
				}).catch(() => {
					uni.hideLoading()
				})
			},
			wgtUpdate(url) {
				uni.downloadFile({
					url: url,
					success: (downloadResult) => {
						console.log(downloadResult)
						if (downloadResult.statusCode === 200) {
							plus.runtime.install(downloadResult.tempFilePath, {
								force: false
							}, function() {
								uni.showModal({
									title: '提示',
									content: '更新成功',
									showCancel: false,
									success: () => {
										plus.runtime.restart();
									}
								})
							}, function(e) {
								uni.showModal({
									title: '提示',
									showCancel: false,
									content: "更新包安装失败，请重试"
								})
							});
						} else {
							uni.showModal({
								title: '提示',
								showCancel: false,
								content: "更新包下载失败，请重试"
							})
						}
					},
					fail: () => {
						uni.showModal({
							title: '提示',
							showCancel: false,
							content: "更新包下载失败，请重试"
						})
					}
				});
			},
			getAppVersion() {
				return new Promise((resolve, reject) => {
					let url = '/zqzfj/business/app/version/1'
					// #ifndef H5
					url = `${this.vuex_ip}/zqzfj/business/app/version/1`
					// #endif
					uni.request({
						url,
						method: 'get',
						timeout: 10000,
						dataType: 'json',
						success: (res) => {
							resolve(res.data)
						},
						fail: () => {
							uni.hideLoading()
							this.mToase('服务器或网络异常，请联系管理员')
							reject(null)
						}
					})
				})
			},
			handleUpdate(){
				let version = this.vuex_version;
				this.getAppVersion().then(appInfo => {
					let seriesVersion = appInfo.data.version;
					if (!seriesVersion) return;
					if (version[0] < seriesVersion[0]) {
						// 整包大版本更新
						uni.showModal({ //提醒用户更新
								title: "更新提示",  
								content: "有大版本需要更新，请更新",  
								showCancel: false,
								success: (res) => {  
									if (res.confirm && appInfo.data.packageUpdateUrl) {
										plus.runtime.openURL(appInfo.data.packageUpdateUrl);
									}  
								}  
						})
					} else if (version < seriesVersion){
						uni.showModal({ //提醒用户更新
								title: "更新提示",  
								content: "有版本需要更新，请更新!",  
								showCancel: false,
								success: (res) => {  
									if (res.confirm && appInfo.data.partUpdateUrl) {  
											this.wgtUpdate(appInfo.data.partUpdateUrl)
									}  
								}  
						})
					} else {
						uni.showModal({ //提醒用户更新
							title: "提示",  
							content: "当前版本为最新版本",  
							showCancel: false 
						})
					}
				})
			},
			handleLogout() {
				uni.showModal({
					title: '提示',
					content: '是否确认退出登录？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$u.api.logout().then(res => {
								this.$u.vuex('vuex_token', '')
								// this.$u.vuex('vuex_password', "");
								closeWebSocket() // 关闭websocket
								uni.reLaunch({ url:"/pages/login/login" })
							}).catch(() => {
								this.$u.vuex('vuex_token', '')
								// this.$u.vuex('vuex_password', "");
								uni.reLaunch({ url:"/pages/login/login" })
							})
						}
					}
				})
			},
			previewAvatarImage() {
				uni.previewImage({
					current: 0,
					urls: [this.userInfo.avatar || this.avatar]
				})
			}
		},
		onLoad() {
			this.fetchData()
			// #ifndef H5
			let clientInfo = plus.push.getClientInfo()
			let systemInfo = uni.getSystemInfoSync()
			this.cid = clientInfo.clientid;
			console.log('cid---', this.cid)
			this.phoneModel = systemInfo.model;
			// #endif
		},
		onPullDownRefresh() {
			this.fetchData()
		}
	}
</script>

<style lang="scss">
.banner {
	position: absolute;
	.title {
		font-size: 40rpx;
		color: #fff;
		position: absolute;
		top: calc(23rpx + var(--status-bar-height));
		left: 30rpx;
		z-index: 10;
		font-weight: 700;
	}
	.my-info {
		position: absolute;
		top: calc(128rpx + var(--status-bar-height));
		left: 40rpx;
		z-index: 10;
		color: #fff;
		font-size: 26rpx;
		.username {
			display: block;
			font-size: 40rpx;
			font-weight: 700;
		}
	}
}
.name {
	font-size: 26rpx;
	font-weight: 400;
}
.container {
	background-color: #fff;
	margin-top: calc(298rpx + var(--status-bar-height));
	height: calc(100vh - 298rpx - var(--status-bar-height) - 100rpx);
	overflow: auto;
	position: relative;
	z-index: 15;
	border-radius: 36rpx 36rpx 0px 0px;
	padding: 44rpx 30rpx 30rpx;
}
</style>

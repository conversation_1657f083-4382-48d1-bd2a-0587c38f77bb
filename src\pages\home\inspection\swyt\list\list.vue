<template>
	<view>
		<!-- 主体 -->
		<view class="tab u-border-top">
			<u-tabs-swiper ref="uTabs" :list="list" :current="current" @change="tabsChange" :is-scroll="false" swiperWidth="750"></u-tabs-swiper>
		</view>
		<view class="container">
			<swiper class="swiper" :current="swiperCurrent" @transition="transition" @animationfinish="animationfinish">
				<!-- 我的 -->
				<swiper-item class="swiper-item" v-for="(scrollIItem, index) in listParams" :key="index">
					<!-- 搜索 -->
					<view class="top-search u-flex u-col-center">
            <searchSelect v-model="typeText" :list="typeList" @change="fetchData" :SelectStyle="'width: 160rpx;margin-right: 20rpx'"></searchSelect>
						<view class="top-search-left u-flex u-flex-1">
							<u-image src="@/static/img/top-search.png" width="35rpx" height="35rpx"></u-image>
							<u-input v-model="scrollIItem.searchValue" type="text" class="u-flex-1 u-m-l-20"></u-input>
						</view>
						<view class="top-search-right">
							<view class="top-search-right-btn u-flex u-row-center u-col-center" @click="handleSearch">
								<u-image src="@/static/img/filter.png" width="28rpx" height="28rpx" class="img"></u-image>
								<text>筛选</text>
							</view>
						</view>
            <!-- 操作按钮(放在这里保证层级不会盖住pop弹窗) -->
            <view class="bottom-btn u-flex u-row-center u-col-center" @click="$u.route({ url: 'pages/home/<USER>/swyt/add/add' })">
              <u-image src="@/static/img/btn-add-icon.png" width="33rpx" height="33rpx"></u-image>
              <text class="text">新增违规</text>
            </view>
					</view>
					<!-- 内容 -->
					<page-content refresher infiniting height="calc(100vh - 183rpx)" @refresh="refresh" @infinite="infiniteScroll" class="swiper-item-inner">
						<view class="list">
							<view class="list-item u-flex u-col-top u-row-between" v-for="(item, idx) in index ? hListData : mListData" :key="idx" @click="handleOpen(item)">
								<u-image class="img" src="@/static/img/list-icon.png" width="60rpx" height="60rpx"></u-image>
								<view class="list-item-content u-flex u-flex-col u-flex-1 u-col-top">
									<text class="title u-line-1">{{ item.type | typeName }}</text>
									<text class="text u-line-1">发现人: {{ item.userName }}</text>
									<text class="text u-line-1">处理人: {{ item.handleUserName }}</text>
									<!-- <text class="text u-line-1">事件类型: {{ item.type | typeName }}</text> -->
									<text class="text u-line-1">详细地址: {{ item.address }}</text>
									<text class="text u-line-1">发生时间: {{ item.happenDate }}</text>
								</view>
								<view class="list-item-state u-flex u-col-center u-row-right">
									<view class="circle" :style="{ backgroundColor: circleColor[item.status] }"></view>
									<text>{{ item | statusName }}</text>
								</view>
							</view>
							<u-loadmore v-if="scrollIItem.noPermission" :status="index ? sStatus : fStatus" class="u-m-t-20" />
							<u-empty mode="permission" v-if="!scrollIItem.noPermission" class="u-m-t-80"></u-empty>
						</view>
					</page-content>
				</swiper-item>
			</swiper>
		</view>
	</view>
</template>

<script>
	import PageContent from '@/components/page-content.vue'
  import searchSelect from "@/components/searchSelect";
	export default {
		components: {
			PageContent,
      searchSelect
		},
		filters: {
			typeName(type) {
				const typeObj = { 1:'绿化', 2:'环卫保洁', 3:'市政道路', 4:'园林' }
				return typeObj[type]
			},
			statusName(item) {
				const statusObj = { 0: '已驳回', 1: '已下派', 2: '处理完成', 3: '已办结' }
				return statusObj[item.status]
			}
		},
		data() {
			return {
        typeText: "全部",
        typeList: [
          { type: '', text: '全部' },
          { type: 3, text: '市政道路' },
          { type: 4, text: '园林' },
          { type: 1, text: '绿化' },
          { type: 2, text: '环卫保洁' }
        ],

				list: [
					{ name: '我的' },
					{ name: '已办结' }
				],
				current: 0,
				swiperCurrent: 0,
				listParams: [
					{
						searchValue: '',
						pageNum: 1,
						pageSize: 10,
						noPermission: true,
					},
					{
						searchValue: '',
						pageNum: 1,
						pageSize: 10,
						noPermission: true,
					}
				],
				triggered: true,
				mListData: [],
				hListData: [],
				fStatus: 'loadmore',
				sStatus: 'loadmore',
				sIsFirst: true,
				circleColor: {
					0: '#EC5656',
					1: '#FAB71C',
					2: '#25c548',
					9: '#bdc3bf'
				},
			}
		},
		methods: {
			refresh({ complete }) {
				this.listParams[this.current].pageNum = 1
				this.fetchData(complete)
			},
			infiniteScroll({ setStatus }) {
				if (!this.current && this.fStatus == 'nomore') return
				if (this.current && this.sStatus == 'nomore') return
				this.listParams[this.current].pageNum++
				this.fetchData()
			},
			handleSearch() {
				this.listParams[this.current].pageNum = 1
				this.fetchData()
			},
			fetchData(complete) {
				const { pageNum, pageSize, searchValue } = this.listParams[this.current]
				let params = { pageNum, pageSize, type: this.typeNumber(this.typeText) }
				if (searchValue) params.searchValue = searchValue
				if (this.current) {
					this.sStatus = 'loading'
					params.status = this.swiperCurrent == 0?null:3
					this.$u.api.getCaseList(params).then(res => {
						if (pageNum === 1) {
							this.hListData = res.rows
						} else {
							this.hListData = this.hListData.concat(res.rows)
						}
						this.sStatus = res.rows.length < 10 ? 'nomore' : 'loadmore'
						if (complete && typeof complete === 'function') complete()
					}).catch((err) => {
						this.sStatus = 'loadmore'
						if (complete && typeof complete === 'function') complete()
						if (err.data.code === 403) this.listParams[this.current].noPermission = false
					})
				} else {
					this.fStatus = 'loading'
					this.$u.api.getMyCaseList(params).then(res => {
						if (pageNum === 1) {
							this.mListData = res.rows
						} else {
							this.mListData = this.mListData.concat(res.rows)
						}
						this.fStatus = res.rows.length < 10 ? 'nomore' : 'loadmore'
						if (complete && typeof complete === 'function') complete()
					}).catch((err) => {
						this.fStatus = 'loadmore'
						if (complete && typeof complete === 'function') complete()
						if (err.data.code === 403) this.listParams[this.current].noPermission = false
					})
				}
			},
			rePageData() {
				this.listParams[this.current].pageNum = 1
				this.sIsFirst = true
				this.fetchData()
			},
			handleOpen(item){
				const { userId, handleUserId, status }  = item
				if (handleUserId == this.vuex_id && (status == 0 || status == 1)) {
					this.$u.route({ url: 'pages/home/<USER>/swyt/detail/detail', params: { id: item.fourId } })
				} else if (userId == this.vuex_id && status == 1) {
					this.$u.route({ url: 'pages/home/<USER>/swyt/add/add', params: { id: item.fourId } })
				} else {
					this.$u.route({ url: 'pages/home/<USER>/swyt/his/his', params: { id: item.fourId } })
				}
			},
			tabsChange(index) {
				this.swiperCurrent = index;
        this.rePageData()
			},
			transition(e) {
				let dx = e.detail.dx;
				this.$refs.uTabs.setDx(dx);
			},
			animationfinish(e) {
				let current = e.detail.current;
				this.$refs.uTabs.setFinishCurrent(current);
				this.swiperCurrent = current;
				this.current = current;
				if (this.sIsFirst && current == 1) {
					// 第一次切换到第二页加载数据
					this.fetchData()
					this.sIsFirst = false
				}
			},
      typeNumber(typeName) {
        const typeObj = { '全部': '', '绿化':1, '环卫保洁':2, '市政道路':3, '园林':4 }
        return typeObj[typeName]
      }
		},
		onLoad() {
			this.fetchData()
		}
	}
</script>

<style lang="scss">
.top-search {
	width: 100%;
	height: 103rpx;
	background-color: #fff;
	padding: 0 30rpx;
	box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1);
	position: relative;
	z-index: 10;
	&-left {
		height: 68rpx;
		background-color: #F5F5F5;
		border-radius: 68rpx;
		padding: 0 20rpx;
		margin-right: 20rpx;
	}
	&-right {
		&-btn {
			width: 136rpx;
			height: 68rpx;
			border-radius: 68rpx;
			background-color: #327BF0;
			color: #fff;
			font-size: 28rpx;
			transition: all 0.5s;
			.img {
				margin-right: 10rpx;
			}
			&:active {
				opacity: 0.3;
			}
		}
	}
}

.tab {

}

// 主体
.container {
	height: calc(100vh - 80rpx);
	background-color: #f5f5f5;
	// min-height: 100vh;
	.swiper {
		height: 100%;
		.swiper-item-inner {
			width: 100%;
			height: calc(100vh - 183rpx);
		}
	}
	.list {
		padding-bottom: 209rpx;
		.list-item {
			margin: 20rpx 30rpx 0;
			background-color: #FFFFFF;
			border-radius: 12rpx;
			box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1);
			padding: 20rpx 20rpx 30rpx;
			.img {
				flex-shrink: 0;
				margin-right: 20rpx;
			}
			&-content {
				width: 360rpx;
				color: #808080;
				line-height: 32rpx;
				font-size: 24rpx;
				.title {
					width: 100%;
					line-height: 60rpx;
					font-weight: 700;
					font-size: 34rpx;
					color: #333333;
				}
				.text {
					width: 100%;
				}
			}
			&-state {
				width: 180rpx;
				flex-shrink: 0;
				font-size: 28rpx;
				font-weight: 700;
				line-height: 60rpx;
				margin-left: 20rpx;
				.circle {
					width: 16rpx;
					height: 16rpx;
					background-color: #EC5656;
					border-radius: 50%;
					margin-right: 10rpx;
				}
			}
		}
	}
}

// 按钮
.bottom-btn {
	width: 220rpx;
	height: 88rpx;
	position: fixed;
	bottom: 140rpx;
	left: 50%;
	margin-left: -110rpx;
	background-color: #327BF0;
	color: #FFFFFF;
	border-radius: 88rpx;
	box-shadow: 0px 0px 14px 0px rgba(82, 97, 121, 0.3);
	transition: all 0.5s;
	z-index: 10;
	&:active {
		background-color: #73a6f7;
	}
	.text {
		font-size: 28rpx;
		margin-left: 15rpx;
	}
}
</style>

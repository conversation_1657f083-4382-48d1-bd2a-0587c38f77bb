<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="260" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="车牌:" prop="plateNo">
					<u-input v-model="form.plateNo" type="text" placeholder="车牌"  disabled />
				</u-form-item>
				<u-form-item label="车辆品牌:" prop="brand">
					<u-input v-model="form.brand" type="text" placeholder="车辆品牌" disabled />
				</u-form-item>
				<u-form-item label="车辆颜色:" prop="taxiColor">
					<u-input v-model="form.taxiColor" type="idcard" placeholder="车辆颜色" disabled  />
				</u-form-item>
				<u-form-item label="车辆编号:" prop="vehicleIndexCode">
					<u-input v-model="form.vehicleIndexCode" type="number" placeholder="车辆编号" disabled />
				</u-form-item>
				<u-form-item label="车辆自编号:" prop="selfNo">
					<u-input v-model="form.selfNo" type="text" placeholder="车辆自编号" disabled />
				</u-form-item>
				<u-form-item label="车辆车型:" prop="taxiModel">
					<u-input v-model="form.taxiModel" type="text" placeholder="请输入车辆车型" disabled />
				</u-form-item>
				<u-form-item label="燃油类型:" prop="fuelType">
					<u-input v-model="form.fuelType" type="text"  placeholder="燃油类型" disabled />
				</u-form-item>
				<u-form-item label="车牌颜色:" prop="cardColor">
					<u-input v-model="form.cardColor" type="text"  placeholder="车牌颜色" disabled />
				</u-form-item>
				<u-form-item label="运输类型:" prop="transportType">
					<u-input v-model="form.transportType" type="text"  placeholder="运输类型" disabled />
				</u-form-item>
				<u-form-item label="区域编号:" :border-bottom="false" prop="regionIndexCode">
					<u-input v-model="form.regionIndexCode" type="text"  placeholder="区域编号" disabled />
				</u-form-item>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="车主姓名:" prop="ownerName">
					<u-input v-model="form.ownerName" type="text" placeholder="车主姓名" disabled />
				</u-form-item>
				<u-form-item label="车主编号:" prop="ownerCode">
					<u-input v-model="form.ownerCode" type="text" placeholder="车主编号" disabled />
				</u-form-item>
				<u-form-item label="驾驶员:" prop="driverName">
					<u-input v-model="form.driverName" type="text" placeholder="驾驶员" disabled />
				</u-form-item>
				<u-form-item label="联系电话:" prop="tel">
					<u-input v-model="form.tel" type="text" placeholder="联系电话" disabled />
				</u-form-item>
				<u-form-item label="驾驶员编号:" prop="driverIndexCode">
					<u-input v-model="form.driverIndexCode" type="text" placeholder="驾驶员编号" disabled />
				</u-form-item>
				<u-form-item label="出租车公司:" prop="taxiCompany">
					<u-input v-model="form.taxiCompany" type="text" placeholder="出租车公司" disabled />
				</u-form-item>
				<u-form-item label="营运证号:" prop="operationLicense">
					<u-input v-model="form.operationLicense" type="text" placeholder="营运证号" disabled />
				</u-form-item>
				<u-form-item label="营运证使用年限:" prop="operationLicenseYears">
					<u-input v-model="form.operationLicenseYears" type="text" placeholder="营运证使用年限" disabled />
				</u-form-item>
				<u-form-item label="营运证登记日期:" prop="operationLicenseTime">
					<u-input v-model="form.operationLicenseTime" type="text" placeholder="营运证登记日期" disabled />
				</u-form-item>
				<u-form-item label="设备编号:" prop="deviceIndexCode">
					<u-input v-model="form.deviceIndexCode" type="text" placeholder="设备编号" disabled />
				</u-form-item>
				<u-form-item label="备注:" prop="remark" label-position="top" :border-bottom="false">
					<u-input v-model="form.remark" type="textarea" placeholder="备注" disabled />
				</u-form-item>
			</view>
		</u-form>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				form: {},
			}
		},
		methods: {
		},
		onLoad(params) {
			// 获取业务数据
			if (params.id) {
				this.$loading()
				this.$u.api.getTaxiDetail({}, params.id).then(res => {
					const taxiColor = {0:'其它',1:'白色',10:'棕色',11:'粉色',12:'紫色',2:'银色',3:'灰色',4:'黑色',5:'红色',6:'深蓝',7:'蓝色',8:'黄色',9:'绿色'}
					const cardColor = {1:'黄色',2:'蓝色',3:'黑色',4:'白色',9:'其它'}
					res.data.taxiColor = res.data.vehicleColor ? taxiColor[res.data.vehicleColor] : ''
					res.data.cardColor = res.data.plateColor ? cardColor[res.data.plateColor] : ''
					this.form = res.data
					uni.hideLoading()
				}).catch(() => {
					uni.hideLoading()
				})
			} else {
				this.form = {}
			}
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

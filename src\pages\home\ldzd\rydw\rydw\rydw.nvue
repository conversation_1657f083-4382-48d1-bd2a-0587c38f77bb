<template>
	<view>
		<map
			id="map"
			ref="map"
			class="map"
			:scale="scale"
			:longitude="longitude"
			:latitude="latitude"
			:markers="markers"
			:style='{ "width": "750rpx", "height": `${windowHeight}px` }'
			@markertap="handleMarkerTap"
		>
			<cover-view class="top">
				<cover-view class="top-item" :class="{ active: item.active }" v-for="(item,idx) in topList" :key="idx" @click="handleClick(idx)">
					<text class="item-font">{{ item.name }}</text>
					<text class="item-font">{{ item.num }}</text>
				</cover-view>
			</cover-view>
			
			<cover-view class="m-area-box" :style="{ height: `${move.height}rpx` }">
				<view class="line" @touchstart.stop="handleStart" @touchmove.stop="handleMove" @touchend.stop="handleEnd">
					<view class="line-inner"></view>
				</view>
				<cover-view class="search-box">
					<cover-view class="search">
						<icon type="search" size="17"/>
						<input class="serch-input" type="text" v-model="searchValue" :cursor-spacing="10" placeholder="搜索人员" @focus="isHide = false" @blur="isHide = true">
					</cover-view>
					<button class="btn" :class="{ hide: isHide }" type="default" @click="handleSearch">
						<text class="btn-text">搜索</text>
					</button>
				</cover-view>
				<!-- 内容主体 -->
				<cover-view class="content">
					<text class="title">执法人员{{userInfo.nickName}}</text>
					<text class="online">{{userInfo.status == '1' ? '在线' : '离线'}}</text>
					<text class="text">岗位：{{userInfo.postName || '暂无'}}</text>
					<text class="text">手机号码：{{userInfo.phonenumber}}</text>
					<!-- 按钮 -->
					<cover-view class="btns">
						<button class="c-btn phone" type="default" plain @click="handleCall(userInfo.phonenumber)">
							<text class="btn-text">拨打电话</text>
						</button>
						<!-- <button class="c-btn tra" type="default" plain>
							<text class="btn-text">查看轨迹</text>
						</button> -->
					</cover-view>
				</cover-view>
			</cover-view>
		</map>
	</view>
</template>

<script>
	import gps from '@/common/gps.js'
	
	export default {
		data() {
			return {
				mapContext: null,
				longitude: 119.635857,
				latitude: 29.110764,
				windowHeight: 0,
				scale: 18,
				topList: [
					{ name: '全部', num: 0, active: true },
					{ name: '在线', num: 0, active: false },
					{ name: '离线', num: 0, active: false }
				],
				searchValue: '',
				isHide: true,
				move: {
					isMove: false,
					touchY: 0,
					height: 126
				},
				screenWidth: 0,
				markers: [],
				onlineMarker: [],
				offlineMarker: [],
				userInfo: {}
			}
		},
		methods: {
			fetchData() {
				getApp().$loading()
				getApp().$u.api.listRecorderGps().then(res => {
					if (res.data && res.data.length) {
						const [onlineIcon, offlineIcon] = ['../../../../../static/img/map-icon-online.png', '../../../../../static/img/map-icon-offline.png']
						this.markers = res.data.map(item => {
							let x = -18, lnglat = gps.gcj_encrypt(parseFloat(item.latitude), parseFloat(item.longitude))
							if (item.name) x = (item.name.length / 2 * 12 + 6) * -1
							const marker = {
								id: item.id,
								userId: item.userId || 0,
								status: item.status,
								width: 60,
								height: 66,
								latitude: lnglat.latitude,
								longitude: lnglat.longitude,
								iconPath: item.status == '1' ? onlineIcon : offlineIcon,
								label: {
									x:x,
									y: -5,
									content: ` ${item.name || item.terminalNo} `,
									fontSize: 12,
									padding: 2,
									borderRadius: 18,
									bgColor: item.status == '1' ? '#FAB71C' : '#BBC3CD',
									color: '#ffffff'
								}
							}
							if (item.status == '1') {
								this.onlineMarker.push(marker)
							} else {
								this.offlineMarker.push(marker)
							}
							return marker
						})
						this.topList[0].num = this.onlineMarker.length + this.offlineMarker.length
						this.topList[1].num = this.onlineMarker.length
						this.topList[2].num = this.offlineMarker.length
						setTimeout(() => {
							this.includePointsFn()
						}, 300)
					}
					uni.hideLoading()
				}).catch(() => {
					uni.hideLoading()
				})
			},
			handleCall(phone) {
				if (phone) {
					if (getApp().$u.test.mobile(phone)) {
						console.log(phone)
						uni.makePhoneCall({
							phoneNumber: phone,
							fail() {
								getApp().mToase('拨打电话失败')
							}
						})
					} else {
						getApp().mToase('当前号码不是一个手机号码')
					}
				} else {
					getApp().mToase('手机号码不存在')
				}
			},
			handleSearch() {
				const searchValue = this.searchValue.trim()
				if (searchValue == '') return
				const allMarkers = [...this.onlineMarker, ...this.offlineMarker]
				const result = allMarkers.filter(item => {
					return item.label.content.includes(searchValue)
				})
				if (result.length) {
					this.markers = result
					this.includePointsFn()
					this.move.height = 126
				} else {
					getApp().mToase('未搜索到相关用户')
				}
			},
			handleMarkerTap(e) {
				const markerId = e.detail.markerId
				this.markers = this.markers.map(item => {
					if (item.id === markerId) {
						item.width = 70
						item.height = 77
						if (item.userId) {
							this.getUserInfo(item.userId, item.status)
							this.move.height = 445
						} else {
							getApp().mToase('未关联用户，无法查询用户信息')
							this.userInfo = {}
							this.move.height = 126
						}
					} else {
						item.width = 60
						item.height = 66
					}
					return item
				})
			},
			getUserInfo(userId, status) {
				getApp().$loading()
				getApp().$u.api.getUserInfo({}, userId).then(res => {
					console.log(res)
					this.userInfo = res.data
					const postIds = res.postIds
					let postNameAry = []
					if (postIds.length) {
						res.posts.forEach(item => {
							if (postIds.includes(item.postId)) postNameAry.push(item.postName)
						})
					}
					this.userInfo.postName = postNameAry.length ? postNameAry.join(',') : ''
					this.userInfo.status = status
					// 打开详情
					uni.hideLoading()
				}).catch(() => {
					uni.hideLoading()
				})
			},
			moveCenter() {
				uni.getLocation({
					type: 'gcj02',
					success: res => {
						this.longitude = res.longitude;
						this.latitude = res.latitude;
					}
				})
			},
			handleClick(idx) {
				this.topList = this.topList.map((item,index) => {
					if (idx === index) item.active = true
					else item.active = false
					return item
				})
				if (idx == 0) {
					this.markers = [...this.onlineMarker, ...this.offlineMarker]
				} else if (idx == 1) {
					this.markers = [...this.onlineMarker]
				} else if (idx == 2) {
					this.markers = [...this.offlineMarker]
				}
				this.includePointsFn()
				this.move.height = 126
			},
			includePointsFn() {
				const points = this.markers.map(item => {
					return { latitude: item.latitude, longitude: item.longitude }
				})
				this.$refs.map.includePoints({ points, padding: [80] })
			},
			handleStart(e) {
				this.move.isMove = true
				this.move.touchY = e.touches[0].screenY
				this.$nextTick(() => {
					this.move.transition = false
				})
			},
			handleMove(e) {
				if (this.move.isMove) {
					const moveLength = e.touches[0].screenY - this.move.touchY
					if (this.move.height < 120 || this.move.height > 486) return
					let h = parseInt(750 * moveLength / this.screenWidth)
					this.move.height -= h
					this.move.touchY = e.touches[0].screenY
				}
			},
			handleEnd(e) {
				this.move.isMove = false
				if (this.move.height <= 300) {
					this.move.height = 126
				} else {
					this.move.height = 445
				}
			}
		},
		onLoad() {
			const systemInfo = uni.getSystemInfoSync()
			this.screenWidth = systemInfo.screenWidth
			this.fetchData()
		},
		onReady() {
			const res = uni.getSystemInfoSync();
			this.windowHeight = res.windowHeight;
			this.mapContext = uni.createMapContext('map', this)
		}
	}
</script>

<style lang="scss">
.top {
	height: 82rpx;
	position: absolute;
	top: 30rpx;
	left: 20rpx;
	right: 20rpx;
	background-color: #fff;
	border-radius: 14rpx;
	box-shadow: 0px 2px 16px 0px rgba(0, 0, 0, 0.15);
	display: flex;
	justify-content: space-between;
	flex-direction: row;
	padding: 14rpx;
	/* #ifdef H5 */
	box-sizing: border-box;
	/* #endif */
	&-item {
		display: flex;
		flex-direction: row;
		width: 220rpx;
		height: 54rpx;
		border-radius: 10rpx;
		align-items: center;
		justify-content: center;
		&.active {
			background-color: #4689F5;
			.item-font {
				color: #fff;
			}
		}
		.item-font {
			font-size: 30rpx;
		}
	}
}
.m-area-box {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	// height: 480rpx;
	// transition: height 0.3s ease-in-out;
	border-top-left-radius: 28rpx;
	border-top-right-radius: 28rpx;
	box-shadow: 0px 0px 40px 0px rgba(0, 0, 0, 0.15);
	.line {
		width: 300rpx;
		height: 40rpx;
		position: absolute;
		top: 0;
		left: 225rpx;
		display: flex;
		justify-content: center;
		align-items: center; 
		.line-inner {
			width: 64rpx;
			height: 8rpx;
			background-color: #D6D6D6;
			border-radius: 4rpx;
		}
	}
	.search-box {
		width: 690rpx;
		margin-top: 40rpx;
		margin-left: 30rpx;
		display: flex;
		flex-direction: row;
		overflow: hidden;
		.search {
			flex: 1;
			padding: 0 22rpx;
			background-color: #F0F0F0;
			border-radius: 14rpx;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			height: 66rpx;
			align-items: center;
			.serch-input {
				flex: 1;
				font-size: 30rpx;
				padding-left: 20rpx;
			}
		}
		.btn {
			border-width: 0;
			padding: 0;
			margin-left: 20rpx;
			&.hide {
				margin-right: -120rpx;
			}
			.btn-text {
				color: #fff;
				font-size: 30rpx;
				background-color: #327BF0;
				width: 100rpx;
				height: 66rpx;
				line-height: 66rpx;
				text-align: center;
				border-radius: 14rpx;
			}
		}
	}
	.content {
		padding: 30rpx 0rpx 0;
		.title {
			font-size: 40rpx;
			color: #333333;
			font-weight: 700;
			margin-bottom: 10rpx;
			margin-left: 30rpx;
		}
		.online {
			width: 84rpx;
			height: 42rpx;
			background-color: #E5F0FF;
			color: #327BF0;
			font-size: 26rpx;
			border-radius: 8rpx;
			text-align: center;
			line-height: 42rpx;
			margin-bottom: 12rpx;
			margin-left: 30rpx;
		}
		.text {
			color: #7F7F7F;
			font-size: 26rpx;
			margin-bottom: 15rpx;
			margin-left: 30rpx;
		}
		.btns {
			box-shadow: 0px -1px 0px 0px #EBEBEB;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			padding: 23rpx 30rpx 23rpx;
			.c-btn {
				border-width: 0;
				padding: 0;
				transition: opacity 0.3s;
				.btn-text {
					width: 700rpx;
					height: 76rpx;
					line-height: 76rpx;
					text-align: center;
					border-radius: 76rpx;
					font-size: 30rpx;
					border-width: 2rpx;
				}
				&:active {
					opacity: 0.3;
				}
			}
			.phone {
				.btn-text {
					color: #327BF0;
					background-color: #fff;
					border-color: #327BF0;
					
				}
			}
			.tra {
				.btn-text {
					border-color: #327BF0;
					background-color: #327BF0;
					color: #fff;
				}
			}
		}
	}
}
</style>

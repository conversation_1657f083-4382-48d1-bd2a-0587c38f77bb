<template>
	<view>
		<learn-time ref="learnTime" learnContent="常见问答"></learn-time>
		<!-- 搜索 -->
		<view class="top-search u-flex u-col-center u-border-bottom">
			<view class="top-search-left u-flex u-flex-1">
				<u-image src="@/static/img/top-search.png" width="35rpx" height="35rpx"></u-image>
				<u-input v-model="searchValue" type="text" class="u-flex-1 u-m-l-20"></u-input>
			</view>
			<view class="top-search-right">
				<view class="top-search-right-btn u-flex u-row-center u-col-center" @click="handleSearch">
					<u-image src="@/static/img/filter.png" width="28rpx" height="28rpx" class="img"></u-image>
					<text>筛选</text>
				</view>
			</view>
		</view>
		<!-- 内容列表 -->
		<view class="content">
			<view class="u-p-t-30 u-border-bottom" v-for="item in dataList" :key="item.id">
				<view class="u-flex u-col-top u-m-b-24">
					<u-image class="u-m-r-10" src="@/static/img/question-icon.png" width="38" height="38" style="flex-shrink: 0;"></u-image>
					<text class="u-font-b">{{ item.question }}</text>
				</view>
				<view class="u-flex u-col-top">
					<u-image class="u-m-r-10" src="@/static/img/answer-icon.png" width="38" height="38" style="flex-shrink: 0;"></u-image>
					<text style="color: #666666;">{{ item.answer }}</text>
				</view>
				<view class="u-text-right u-m-t-20 u-p-b-30">
					<text style="color: #808080;">更新于{{ item.updateTime ? item.updateTime : item.createTime }}</text>
				</view>
			</view>
			<u-loadmore :status="status" class="u-m-t-20" />
		</view>
	</view>
</template>

<script>
	import LearnTime from '@/components/learn-time.vue'
	
	export default {
		components: {
			LearnTime
		},
		data() {
			return {
				searchValue: '',
				pageNum: 1,
				pageSize: 10,
				status: 'loadmore',
				dataList: []
			}
		},
		methods: {
			fetchData() {
				const { pageNum, pageSize } = this 
				let params = { pageNum, pageSize }
				if (this.searchValue.trim() != '') params.searchValue = this.searchValue
				this.status = 'loading'
				this.$u.api.answerList(params).then(res => {
					if (pageNum == 1) {
						this.dataList = res.rows
						uni.stopPullDownRefresh()
					} else {
						this.dataList = this.dataList.concat(res.rows)
					}
					this.status = res.rows.length < pageSize ? 'nomore' : 'loadmore'
				}).catch(() => {
					this.status = 'loadmore'
					uni.stopPullDownRefresh()
				})
			},
			handleSearch() {
				this.pageNum = 1
				this.fetchData()
			}
		},
		onLoad() {
			this.fetchData()
		},
		onReachBottom() {
			if (this.status != 'nomore') {
				this.pageNum++
				this.fetchData()
			}
		},
		onPullDownRefresh() {
			this.pageNum = 1
			this.fetchData()
		},
		onBackPress(e) {
			if (e.from == 'backbutton' && !this.$refs.learnTime.isEndStudy) {
				uni.showModal({
					title: '提示',
					content: '还未结束学习，是否保存学习记录？',
					confirmText: '保存记录',
					cancelText: '直接退出',
					success: ({ confirm }) => {
						if (confirm) {
							this.$refs.learnTime.addLearnRecord()
						} else {
							uni.navigateBack()
						}
					}
				})
				return true
			}
		}
	}
</script>

<style lang='scss'>
page {
	background-color: #fff;
}
.top-search {
	width: 100%;
	height: 51px;
	background-color: #fff;
	padding: 0 30rpx;
	/* box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1); */
	position: fixed;
	top: 40px;
	/* #ifdef H5 */
	top: 84px;
	/* #endif */
	left: 0;
	z-index: 10;
	&-left {
		height: 68rpx;
		background-color: #F5F5F5;
		border-radius: 68rpx;
		padding: 0 20rpx;
		margin-right: 20rpx;
	}
	&-right {
		&-btn {
			width: 136rpx;
			height: 68rpx;
			border-radius: 68rpx;
			background-color: #327BF0;
			color: #fff;
			font-size: 28rpx;
			transition: all 0.5s;
			.img {
				margin-right: 10rpx;
			}
			&:active {
				opacity: 0.3;
			}
		}
	}
}
.content {
	padding: 91px 30rpx 30rpx;
}
</style>

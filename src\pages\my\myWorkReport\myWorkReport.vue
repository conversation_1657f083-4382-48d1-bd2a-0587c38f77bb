<template>
	<view>
		<!-- 搜索 -->
		<view class="top-search u-flex u-col-center u-border-top u-border-bottom">
			<view class="top-search-left u-flex u-flex-1">
				<!-- <u-image src="@/static/img/top-search.png" width="35rpx" height="35rpx"></u-image> -->
				<u-icon name="calendar" color="#3e83f0" size="35" @click="showStartTime = true"></u-icon>
				<u-input v-model="startTime" type="text" class="u-flex-1 u-m-l-20" disabled placeholder="开始日期" @click="showStartTime = true"></u-input>
				<u-picker v-model="showStartTime" mode="time" :params="params" :default-time="startTime" @confirm="handleStartTime"></u-picker>
			</view>
			<span>-</span>
			<view class="top-search-left u-flex u-flex-1">
				<!-- <u-image src="@/static/img/top-search.png" width="35rpx" height="35rpx"></u-image> -->
				<u-icon name="calendar" color="#3e83f0" size="35" @click="showEndTime = true"></u-icon>
				<u-input v-model="endTime" type="text" class="u-flex-1 u-m-l-20" disabled placeholder="结束日期" @click="showEndTime = true"></u-input>
				<u-picker v-model="showEndTime" mode="time" :params="params" :default-time="endTime" @confirm="handleEndTime"></u-picker>
			</view>
			<view class="top-search-right">
				<view class="top-search-right-btn u-flex u-row-center u-col-center" @click="fetchDate">
					<u-image src="@/static/img/filter.png" width="28rpx" height="28rpx" class="img"></u-image>
					<text>筛选</text>
				</view>
			</view>
		</view>
		<u-table>
				<u-tr>
					<u-th>类型</u-th>
					<u-th>总数</u-th>
					<u-th>完成数</u-th>
					<u-th>未完成数</u-th>
					<u-th>完成率</u-th>
				</u-tr>
				<u-tr v-for="(item,idx) in dataList" :key="idx">
					<u-td>{{ item.name }}</u-td>
					<u-td>{{ item.totalCount }}</u-td>
					<u-td>{{ item.finishCount }}</u-td>
					<u-td>{{ item.unfinishCount }}</u-td>
					<u-td>{{ (item.finishRate * 100).toFixed(2) }}%</u-td>
				</u-tr>
			</u-table>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				showStartTime: false,
				showEndTime: false,
				startTime: '',
				endTime: '',
				dataList: [],
				params: { year: true, month: true, day: true, hour: false, minute: false, second: false },
			}
		},
		methods: {
			fetchDate() {
				this.$loading()
				let params = { userId: this.vuex_id }
				if (this.startTime) params.startTime = this.startTime
				if (this.endTime) params.endTime = this.endTime
				this.$u.api.getWorkReport(params).then(res => {
					uni.hideLoading()
					this.dataList = res.data
				}).catch(() => {
					uni.hideLoading()
				})
			},
			handleStartTime(res) {
				const { year, month, day } = res
				this.startTime = `${year}-${month}-${day} 00:00:00`
			},
			handleEndTime(res) {
				const { year, month, day } = res
				this.endTime = `${year}-${month}-${day} 00:00:00`
			}
		},
		onLoad() {
			const nowDate = new Date()
			const ym = this.$u.timeFormat(nowDate.getTime(), 'yyyy-mm')
			
			const endofMon = new Date(nowDate.getFullYear(), nowDate.getMonth() + 1, 0).getDate()
			this.startTime = `${ym}-01`
			this.endTime = `${ym}-${endofMon}`
			this.fetchDate()
		}
	}
</script>

<style lang="scss">
.top-search {
	width: 100%;
	height: 103rpx;
	background-color: #fff;
	padding: 0 30rpx;
	&-left {
		height: 68rpx;
		background-color: #F5F5F5;
		border-radius: 68rpx;
		padding: 0 20rpx;
		margin-right: 20rpx;
	}
	&-right {
		&-btn {
			width: 136rpx;
			height: 68rpx;
			border-radius: 68rpx;
			background-color: #327BF0;
			color: #fff;
			font-size: 28rpx;
			transition: all 0.5s;
			.img {
				margin-right: 10rpx;
			}
			&:active {
				opacity: 0.3;
			}
		}
	}
}
</style>

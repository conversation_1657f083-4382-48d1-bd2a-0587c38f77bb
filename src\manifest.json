{
  "name": "aijinhua<PERSON><PERSON><PERSON>",
  "appid": "__UNI__EA3480D",
  "description": "站前移动端",
  "versionName": "1.0.0",
  "versionCode": "100",
  "transformPx": false,
  "h5": {
    "publicPath": "./", // 修改1 不修改此处会出现应用白屏的情况
    "router": {
      "base": "./", // 修改2 不修改此处会出现图片拿不到的情况
      "mode": "history" // 修改3 浙里办只支持hash路由
    },
    "sdkConfigs": {
      "maps": {
        "amap": {
          "key": "08c65eaf9ba8c27a6c6fef43e4901973",
          "securityJsCode": "fe23d028d1620e84e5cac41f02e951c0",
          "serviceHost": ""
        }
      }
    },
    "title": "站前移动端",
    "optimization": {
      "treeShaking": {
        "enable": true
      }
    }
  },
  "app-plus": {
    /* 5+App特有相关 */
    "usingComponents": true,
    "splashscreen": {
      "alwaysShowBeforeRender": true,
      "waiting": true,
      "autoclose": true,
      "delay": 0
    },
    "modules": {
      "OAuth": {},
      "Maps": {}
    },
    /* 模块配置 */
    "distribute": {
      /* 应用发布信息 */
      "android": {
        /* android打包配置 */
        "permissions": [
          "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
          "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
          "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
          "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
          "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CAMERA\"/>",
          "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
          "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
          "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
          "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
          "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
          "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
          "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
          "<uses-feature android:name=\"android.hardware.camera\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
        ]
      },
      "ios": {
        "dSYMs": false
      },
      /* ios打包配置 */
      "sdkConfigs": {
        "oauth": {
          "weixin": {
            "appid": "wx93fe6114b1db078f",
            "UniversalLinks": ""
          }
        },
        "maps": {
          "amap": {
            "appkey_ios": "",
            "appkey_android": "0de4e77e11afa522928a24aec2b2e306"
          }
        },
        "ad": {}
      }
    }
  },
  /* SDK配置 */
  "quickapp": {},
  /* 快应用特有相关 */
  "mp-weixin": {
    /* 微信小程序特有相关 */
    "appid": "wxe583b1c7ed17ac52",
    "setting": {
      "urlCheck": false
    },
    "usingComponents": true,
    "permission": {
      "scope.userLocation": {
        "desc": "你的位置信息将用于小程序地址定位"
      }
    },
    "plugins": {
      "WechatSI": {
        "version": "0.3.5",
        "provider": "wx069ba97219f66d99"
      }
    },
    "requiredPrivateInfos": ["getLocation", "chooseLocation"],
    "lazyCodeLoading": "requiredComponents",
    "optimization": {
      "subPackages": true
    },
    "runmode": "liberate" // 开启分包优化后，必须配置资源释放模式
  },
  "mp-alipay": {
    "usingComponents": true
  },
  "mp-baidu": {
    "usingComponents": true
  },
  "mp-toutiao": {
    "usingComponents": true
  },
  "mp-qq": {
    "usingComponents": true
  }
}

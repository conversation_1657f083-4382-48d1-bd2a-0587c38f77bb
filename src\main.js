import Vue from 'vue'
import App from './App'
import uView from "uview-ui";
import store from './store'
import httpInterceptor from '@/common/http.interceptor.js'
import httpApi from '@/common/http.api.js'
import vuexStore from '@/store/$u.mixin.js'
import Cookies from 'js-cookie'
Vue.config.productionTip = false

App.mpType = 'app'

Vue.use(uView);
Vue.mixin(vuexStore)
Vue.prototype.Cookies = Cookies
Vue.prototype.mToase = function (title) {
	uni.showToast({
		title: title || '',
		icon: 'none'
	})
}
// 执行上一个页面存在的方法,并返回
Vue.prototype.$implement = function (obj) {
	const { methods, immediately, data } = { methods: 'rePageData', immediately: false, ...obj }
	let pages = getCurrentPages();             //获取所有页面栈实例列表
	let prevPage = pages[ pages.length - 2 ];  //上一页页面实例
	if (prevPage.$vm[methods] && typeof prevPage.$vm[methods] === 'function') {
		if (data != undefined) {
			prevPage.$vm[methods](data)
		} else {
			prevPage.$vm[methods]()
		}
	}
	if (immediately) {
		uni.navigateBack();
	} else {
		setTimeout(() => uni.navigateBack(), 1500)
	}
}
// 公用加载框
Vue.prototype.$loading = function (text = '数据加载中...') {
	uni.showLoading({ title: text, mask: true })
}

const app = new Vue({
	store,
  ...App
})

// http拦截器，将此部分放在new Vue()和app.$mount()之间，才能App.vue中正常使用
Vue.use(httpInterceptor, app)
// http接口API抽离，免于写url或者一些固定的参数
Vue.use(httpApi, app)

app.$mount()
// #ifdef APP-PLUS
let main = plus.android.runtimeMainActivity();
plus.runtime.quit = function() {
    main.moveTaskToBack(false);
};

plus.nativeUI.toast = (function(str) {
    if (str == '再按一次退出应用') {

        main.moveTaskToBack(false);
        return false;
    }
});
// #endif

<template>
	<view>
		<u-form ref="uForm" :model="form" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="上报人:" prop="userName" required>
					<u-input v-model="form.userName" disabled type="text" placeholder="上报人" />
				</u-form-item>
				<u-form-item label="所属部门:" prop="deptName" required>
					<u-input v-model="form.deptName" disabled type="text" placeholder="所属部门" />
				</u-form-item>
				<u-form-item label="上报时间:" prop="happenTime" required>
					<u-input v-model="form.happenTime" :disabled="disable" type="popup" placeholder="请选择上报时间" @click="showHappenTime = true"/>
					<u-picker v-if="vuex_id == form.userId" v-model="showHappenTime" mode="time" :params="params" :default-time="form.happenTime" @confirm="handleItDateCon"></u-picker>
				</u-form-item>
				<u-form-item label="上报地址:" prop="address" required>
					<u-input v-model="form.address" :disabled="disable" type="text" placeholder="请选择上报地址" />
					<u-button v-if="vuex_id == form.userId" v-slot="right" size="mini" type="primary" @click="handleChooseLocation">选择地址</u-button>
				</u-form-item>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="上报内容:" prop="content" label-position="top" :border-bottom="false">
					<u-input v-model="form.content" :disabled="disable" type="textarea" maxlength="300" height="140" placeholder="请输入上报内容..." />
				</u-form-item>
				<u-upload
					ref="happenfile"
					name="files"
					:max-count="maxCount"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
				  :size-type="['compressed']"
					:file-list="happenFile"
					:before-remove="handleRemove"
					:deletable="deletabled"
					@on-uploaded="handleAllUpload"
					@on-error="handleError"
				></u-upload>
			</view>
		</u-form>
		<!-- 提交按钮 -->
		<view v-if="vuex_id == form.userId" class="btn-box u-border-top u-flex">
			<!-- <u-button class="u-flex-1 u-m-r-20" type="primary" shape="circle" :custom-style="subStyle" @click="handleSubmit(2)">暂存</u-button> -->
			<u-button class="u-flex-1 u-m-r-20" type="primary" shape="circle" :loading="aloading"  :custom-style="subStyle" @click="handleOver">提 交</u-button>
			<u-button class="u-flex-1" type="primary" shape="circle" :loading="aloading"  :custom-style="subStyle" @click="handlDelete">删除</u-button>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
import gps from '@/common/gps.js'
export default {
  data() {
    return {
      // 最大图片数
      maxCount: 4,
      disable: false,
      // 是否显示删除按钮
      deletabled: true,
      aloading: false,
      params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
      labelStyle: {
        color: '#808080',
        fontSize: '30rpx'
      },
      subStyle: {
        height: '86rpx',
        backgroundColor: '#327BF0'
      },
      showCarType: false,
      // transportCarType: [],
      showDynamicType: false,
      DynamicTypeList: [],
      showHappenTime: false,
      isShowFTaxiType: false,
      isShowSTaxiType: false,
      form: { address: '' },
      rules: {
        userName: [{ required: true, message: '上报人员', trigger: 'blur' }],
        deptName: [{ required: true, message: '所属部门', trigger: 'blur' }],
        happenTime: [{ required: true, message: '请选择上报时间', trigger: 'change' }],
        content: [{ required: true, message: '请输入上报内容', trigger: 'blur' }],
        address: [{ required: true, message: '请选择上报地址', trigger: 'change' }]
      },
      happenData: {
        tableName: 'case_work_dynamic',
        status: 1
      },
      happenFile: []
    }
  },
  computed: {
    action() {
      // #ifdef H5
      return `/prod-api/system/file/upload`
      // #endif
      // #ifndef H5
      return `${this.vuex_ip}/prod-api/system/file/upload`
      // #endif
    },
    header() {
      return {
        Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
      }
    }
  },
  methods: {
    // async handleTypeClick(idx) {
    // 	const { dictValue, text, remark } = this.transportCarType[idx]
    // 	this.form = { ...this.form, carType: dictValue, carTypeName: text }
    // },
    handleChooseLocation() {
      uni.chooseLocation({
        latitude: 29.110764,
        longitude: 119.635857,
        success: res => {
          const { address, longitude, latitude } = res
          const lnglat = gps.gcj_decrypt(parseFloat(latitude), parseFloat(longitude))
          this.form = { ...this.form, address: '浙江省金华市婺城区城北街道' + address, longitude: lnglat.lng, latitude: lnglat.lat, lnglat: `${lnglat.lng}, ${lnglat.lat}` }
        },
        fail: () => {
          uni.showToast({
            title: '地图打开失败',
            icon: 'none',
            position: 'bottom'
          })
        }
      })
    },
    handleItDateCon(res) {
      const { year, month, day, hour, minute, second } = res
      this.form = { ...this.form, happenTime: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
    },
    handleRemove(index, lists) {
      const fileInfo = lists[index]
      const fileId = fileInfo.url.split('?id=')[1]
      if (fileId) {
        return new Promise((resolve, reject) => {
          uni.showModal({
            title: '提示',
            content: '删除后将无法恢复，是否确认删除？',
            success: ({ confirm }) => {
              if (confirm) {
                this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
              } else {
                reject()
              }
            }
          })
        })
      } else {
        return true
      }
    },
    handleAllUpload(lists) {
      // 所有文件上传成功，返回上一级页面
      const isFail = lists.some(item => item.progress !== 100)
      if (!isFail) {
        uni.showToast({ title: '操作成功', duration: 3000 })
        uni.hideLoading()
        this.$implement()
      }
    },
    handleError() {
      // 文件上传失败，弹出提示是否重新上传
      uni.hideLoading()
      uni.showModal({
        title: '提示',
        content: '图片上传失败，是否重新上传？',
        success: ({ confirm }) => {
          if (confirm) {
            this.$loading('图片上传中')
            this.$refs.happenfile.reUpload()
          } else {
            this.$implement({ immediately: true })
          }
        }
      })
    },
    handleOver() {
      if (this.vuex_id != this.form.userId) return this.mToase('该记录不是您的工作动态，没有操作权限！')
      uni.showModal({
        title: '提示',
        content: '是否确认提交？',
        success: ({ confirm }) => {
          if (confirm) {
            this.handleSubmit(9)
          }
        }
      })
    },
    validateFn(isValid) {
      // 根据传入的状态来确定是否要进行验证，用以区别暂存和办结
      return new Promise((resolve, reject) => {
        if (isValid) {
          this.$refs.uForm.validate(valid => {
            if (valid) {
              // 图片验证，没有图片不通过验证
              if (!this.$refs.happenfile.lists.length) {
                this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
                reject()
              } else {
                resolve()
              }
            } else {
              reject()
            }
          })
        } else {
          resolve()
        }
      })
    },
    handleSubmit(status) {
      this.validateFn(status == 9).then(() => {
        const params = { ...this.form, status, type: 1 }
        // 开始上传
        this.aloading = true
        this.$loading('数据上传中')
        this.$u.api.editDynamic(params).then(res => {
          // 遍历列表，查询是否有未上传的图片
          const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
          console.log(uploadFile)
          this.happenData.businessId = this.form.id
          if (uploadFile) {
            this.$loading('图片上传中')
            this.$refs.happenfile.upload()
          } else {
            uni.showToast({ title: '操作成功', duration: 3000 })
            uni.hideLoading()
            this.$implement()
          }
        }).catch(() => {
          uni.hideLoading()
          this.aloading = false
        })
      }).catch(() => {})
    },
    // 删除动态
    handlDelete() {
      if (this.vuex_id != this.form.userId) return this.mToase('该记录不是您的工作动态，没有操作权限！')

      uni.showModal({
        title: '提示',
        content: '是否确认删除该条工作动态？',
        success: ({ confirm }) => {
          if (confirm) {
            this.$loading('删除中，请稍后...')
            this.$u.api.removeDynamic({}, this.form.id).then(res => {
              // this.fetchData()
              // this.mToase('操作成功')
              uni.hideLoading()
              this.$implement()
            }).catch(() => {
              uni.hideLoading()
            })
          } else {
            // this.$implement({ immediately: true })
          }
        }
      })
    }
  },
  async onLoad(params) {
    // 获取业务数据
    if (params.id) {
      this.$loading()
      Promise.all([
        this.$u.api.getDynamic({}, params.id),
        this.$u.api.getFileList({ tableName: 'case_work_dynamic', status: 1, businessId: params.id })
      ]).then(resAry => {
        // console.log(resAry)
        uni.hideLoading()
        const formData = resAry[0].data
        const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
        this.form = { ...formData, lnglat }
        this.happenFile = resAry[1].rows.map(item => {
          return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
        })
        if (this.vuex_id != this.form.userId) {
          this.deletabled = false
          this.disable = true
          this.maxCount = this.happenFile.length
        }
        // console.log(this.happenFile)
      }).catch(() => {
        uni.hideLoading()
      })
    } else {
      const timestamp = new Date().getTime()
      const happenTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
      this.form = { happenTime, userName: this.vuex_nickName, userId: this.vuex_id }
    }
  },
  onReady() {
    this.$refs.uForm.setRules(this.rules)
  }
}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

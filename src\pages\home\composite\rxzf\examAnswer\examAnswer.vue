<template>
	<view :style="isShow ? 'padding-bottom: 140rpx;' : ''">
		<view class="top u-flex u-flex-col u-col-top u-p-30 u-border-top">
			<text class="u-p-b-20 u-font-b">试卷名称：{{ examConfigName }}</text>
			<text v-if="updateTime">交卷时间：{{ updateTime }}</text>
		</view>
		<u-gap height="20" bg-color="#F5F5F5"></u-gap>
		<u-card title="答题情况" margin="0rpx">
			<view class="answer_con" slot="body">
				<view>答对：{{examInfo.correctCount}}题</view>
				<view>答错：{{examInfo.wrongCount}}题</view>
				<view>未答：{{examInfo.unDoCount}}题</view>
				</view>			
		</u-card>
		<!-- <u-gap height="20" bg-color="#F5F5F5"></u-gap> -->
		<!-- <u-card title="答题分值" margin="0rpx" v-else>
			<view class="charts" slot="body">
				<qiun-data-charts  type="arcbar" :chartData="chartData" :opts="opts" />
			</view>
		</u-card> -->
		<u-gap height="20" bg-color="#F5F5F5"></u-gap>
		<u-card title="答题卡" margin="0rpx">
			<view class="answer-list" slot="body">
				<view class="item" v-for="(item,idx) in answerList" :key="idx" @click="handleOpen(idx)">
					<text class="item-inner" :class="{ correct: item.status == 1, wrong: item.status == 2 }">{{idx + 1}}</text>
				</view>
			</view>
		</u-card>
		<view class="btn-box">
		<!-- <view class="btn-box" v-if="isShow"> -->
			<u-button type="primary" @click="handleOpenHis">查看解析</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				examConfigName: '',
				updateTime: '',
				questionList: [],
				answerList: [],
				correctRate: 0,
				isShow: false,
				examId: '',
				status:'',
				examInfo:{}
			}
		},
		computed: {
			opts() {
				return {
					title: {
						name: `${this.correctRate} 分`
					},
					subtitle: {
						name: ''
					}
				}
			},
			chartData() {
				return {
					"series": [
						{
							"name": "分",
							"data": this.correctRate,
							"color": "#2fc25b"
						}
					]
				}
			}
		},
		methods: {
			fetchData(examId) {
				this.$loading()
				this.$u.api.getExamAnswer({ examId}).then(res => {
					uni.hideLoading()
					this.examInfo= res.finishSituation
					const { correctCount, allCount } = res.finishSituation
					this.examConfigName = res.data.examConfigName
					this.updateTime = res.data.updateTime
					this.status = res.data.status
					if (correctCount != undefined && allCount != undefined) {
						this.correctRate = (100 / allCount)*correctCount
					}
					this.answerList = res.questionList.map(item => {
						return {
							id: item.id,
							userAnswer: item.userAnswer,
							answerDate: item.answerDate,
							status: item.status,
							examId: res.data.id
						}
					})
				}).catch(() => {
					uni.hideLoading()
				})
			},
			handleOpen(idx) {
				// if (this.isShow) {
					this.$u.route({ url: 'pages/home/<USER>/rxzf/examHis/examHis', params: { id: this.examId, idx } })
				// } else {
				// 	this.$implement({ methods:'hadnleJump', immediately: true, data: idx })
				// }
			},
			handleOpenHis() {
				this.handleOpen(0)
			}
		},
		onLoad(params) {
			this.examId = params.id
			this.isShow = params.isShow ? true : false
			this.fetchData(this.examId)
		},
		onBackPress(e) {
			if (this.isShow && e.from == 'backbutton') {
				this.$implement({ immediately: true })
				return true
			}
		}
	}
</script>

<style>
.top {
	background-color: #fff;
}
.charts {
	width: 750;
	background-color: #fff;
	height: 300rpx;
}
.item {
	width: 20%;
	display: inline-block;
	text-align: center;
	padding: 20rpx 0;
}
.item-inner {
	width: 60rpx;
	height: 60rpx;
	display: inline-block;
	border: 1px solid #999999;
	border-radius: 50%;
	line-height: 60rpx;
	text-align: center;
}
.correct {
	background-color: #00ca00;
	border-color: #00ca00;
	color: #fff;
}
.wrong {
	background-color: #ea0000;
	border-color: #ea0000;
	color: #fff;
}
.btn-box {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 140rpx;
	padding: 30rpx;
	background-color: #fff;
}
.answer_con{
	display: flex;
	justify-content: space-between;	
	padding:0 30rpx;	
	font-size: 28rpx;	
}
</style>

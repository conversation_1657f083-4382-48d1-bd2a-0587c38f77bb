<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="250" :label-style="labelStyle">
			<view class="p-lr-30">
					<u-form-item label="通讯地址:" prop="address">
						<u-input disabled v-model="form.address" type="textarea" placeholder="请选择通讯地址" readonly="readonly" />
					</u-form-item>
					<u-form-item label="申报者证件号码:" prop="applyCardNumber">
						<u-input disabled v-model="form.applyCardNumber" placeholder="请输入申报者证件号码" />
					</u-form-item>
					<u-form-item label="申报者证件类型:" prop="applyCardType">
						<u-input disabled v-model="form.applyCardType" placeholder="请输入申报者证件类型" />
					</u-form-item>
					<u-form-item label="申报来源:" prop="applyFrom">
						<u-input disabled v-model="form.applyFrom" placeholder="请输入申报来源" />
					</u-form-item>
					<u-form-item label="申报者名称:" prop="applyName">
						<u-input disabled v-model="form.applyName" placeholder="请输入申报者名称" />
					</u-form-item>
					<u-form-item label="项目性质:" prop="applyPropertiy">
						<u-input disabled v-model="form.applyPropertiy" placeholder="请输入项目性质" />
					</u-form-item>
					<u-form-item label="审批类型:" prop="approvetype">
						<u-input disabled v-model="form.approvetype" placeholder="请输入审批类型" />
					</u-form-item>
					<u-form-item label="区划编码:" prop="areacode">
						<u-input disabled v-model="form.areacode" placeholder="请输入收件部门所属行政区划编码" />
					</u-form-item>
					<u-form-item label="所属系统:" prop="belongsystem">
						<u-input disabled v-model="form.belongsystem" placeholder="请输入所属系统" />
					</u-form-item>
					<u-form-item label="项目关联号:" prop="belongto">
						<u-input disabled v-model="form.belongto" placeholder="请输入项目关联号" />
					</u-form-item>
					<u-form-item label="办理方式:" prop="busmode">
						<u-input disabled v-model="form.busmode" placeholder="请输入办理方式" />
					</u-form-item>
					<u-form-item label="办理方式说明:" prop="busmodeDesc">
						<u-input disabled v-model="form.busmodeDesc" placeholder="请输入办理方式说明" />
					</u-form-item>
					<u-form-item label="联系人:" prop="contactMan">
						<u-input disabled v-model="form.contactMan" placeholder="请输入联系人" />
					</u-form-item>
					<u-form-item label="代理人证件号码:" prop="contactManCardNumber">
						<u-input disabled v-model="form.contactManCardNumber" placeholder="请输入代理人证件号码" />
					</u-form-item>
					<u-form-item label="代理人证件类型:" prop="contactManCardType">
						<u-input disabled v-model="form.contactManCardType" placeholder="请输入代理人证件类型" />
					</u-form-item>
					<u-form-item label="办件类型:" prop="infoType">
						<u-input disabled v-model="form.infoType" placeholder="请输入办件类型" />
					</u-form-item>
					<u-form-item label="是否是在垂管系统中运行的事项:" class="sf" prop="isManubrium">
						<u-radio-group v-model="form.isManubrium" disabled>
							<u-radio :name="1">是</u-radio>
							<u-radio :name="0">否</u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item label="法人代表:" prop="legalman">
						<u-input disabled v-model="form.legalman" placeholder="请输入法人代表" />
					</u-form-item>
					<u-form-item label="邮编:" prop="postcode">
						<u-input disabled v-model="form.postcode" placeholder="请输入邮编" />
					</u-form-item>
					<u-form-item label="申报名称:" prop="projectName">
						<u-input disabled v-model="form.projectName" placeholder="请输入申报名称" />
					</u-form-item>
					<u-form-item label="申报号:" prop="projId">
						<u-input disabled v-model="form.projId" placeholder="请输入申报号" />
					</u-form-item>
					<u-form-item label="查询密码:" prop="projPwd">
						<u-input disabled v-model="form.projPwd" placeholder="请输入查询密码" />
					</u-form-item>
					<u-form-item label="创建用户名称:" prop="receiveName">
						<u-input disabled v-model="form.receiveName" placeholder="请输入创建用户名称" />
					</u-form-item>
					<u-form-item label="申报时间:" prop="receiveTime">
						<u-input disabled v-model="form.receiveTime" placeholder="选择日期时间" />
					</u-form-item>
					<u-form-item label="收件人ID:" prop="receiveUseId">
						<u-input disabled v-model="form.receiveUseId" placeholder="请输入收件人ID" />
					</u-form-item>
					<u-form-item label="关联业务标识:" prop="relbusId">
						<u-input disabled v-model="form.relbusId" placeholder="请输入关联业务标识" />
					</u-form-item>
					<u-form-item label="权力事项编码:" prop="serviceCode">
						<u-input disabled v-model="form.serviceCode" placeholder="请输入权力事项编码" />
					</u-form-item>
					<u-form-item label="权力事项基本码:" prop="serviceCodeId">
						<u-input disabled v-model="form.serviceCodeId" placeholder="请输入权力事项基本码" />
					</u-form-item>
					<u-form-item label="终审部门编码:" prop="serviceDeptId">
						<u-input disabled v-model="form.serviceDeptId" placeholder="请输入事项终审部门编码" />
					</u-form-item>
					<u-form-item label="实施机构代码:" prop="ssorgCode">
						<u-input disabled v-model="form.ssorgCode" placeholder="请输入实施机构组织机构代码" />
					</u-form-item>
					<u-form-item label="权力事项版本号:" prop="serviceVersion">
						<u-input disabled v-model="form.serviceVersion" placeholder="请输入权力事项版本号" />
					</u-form-item>
					<u-form-item label="权力事项名称:" prop="serviceName">
						<u-input disabled v-model="form.serviceName" placeholder="请输入权力事项名称" />
					</u-form-item>
					<u-form-item label="同步状态:" prop="syncStatus">
						<u-input disabled v-model="form.syncStatus" placeholder="请输入同步状态" />
					</u-form-item>
					<u-form-item label="联系电话:" prop="phone">
						<u-input disabled v-model="form.phone" placeholder="请输入联系电话" />
					</u-form-item>
					<u-form-item label="是否已缴费:" prop="payment">
						<u-radio-group v-model="form.payment" disabled>
							<u-radio :name="1">已缴费</u-radio>
							<u-radio :name="0">未缴费</u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item label="分类中队名称:" prop="cDeptName">
						<u-input disabled v-model="form.cDeptName" placeholder="请输入分类中队名称" />
					</u-form-item>
					<u-form-item label="截至时间:" prop="endtime">
						<u-input disabled v-model="form.endtime" placeholder="选择日期时间" />
					</u-form-item>
					<u-form-item label="处罚金额:" prop="punishment">
						<u-input disabled v-model="form.punishment" placeholder="请输入处罚金额" />
					</u-form-item>
					<u-form-item label="搜索地址:" prop="searchAddress">
						<u-input disabled type="textarea" v-model="form.searchAddress" placeholder="请输入搜索地址" />
					</u-form-item>
				</el-row>
			</view>
		</u-form>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				form: {address:"浙江省金华市婺城区城北街道"},
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
			}
		},
		methods: {

		},
		onLoad(params) {
			this.form = params
		}
	}
</script>

<style scoped>
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
::v-deep .sf .u-form-item--left {
	flex: 1 0 125px !important;
}
</style>

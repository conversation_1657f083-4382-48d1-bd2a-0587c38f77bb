<template>
	<view style="height: 100%; padding-top: 30rpx;">
		<view class="top u-flex">
			<view class="left u-flex u-row-center">
				<text>{{ vuex_nickName.length > 2 ? vuex_nickName.slice(-2) : vuex_nickName }}</text>
			</view>
			<view class="u-flex-1 u-flex u-flex-col u-col-top">
				<text class="u-font-30 u-font-b">{{ vuex_nickName }}</text>
				<text class="u-font-24" style="color: #a0a0a0;">{{ vuex_deptName }}</text>
			</view>
			<view class="right u-flex u-flex-col" style="color: #686868;" @click="$u.route({ url: 'pages/home/<USER>/map/map', params: { area: punchArea } })">
				<text>查看</text>
				<text>位置</text>
			</view>
		</view>
		<view class="content">
			<!-- 打卡情况 -->
			<view class="clock-list u-flex">
				<view class="left u-flex-1 u-m-r-20">
					<text class="u-font-30">
            上班{{startTime}}
            <u-icon name="checkmark" color="#1989fa" v-if="punchInInfo.startInTime" style="position: relative;left: 6px"></u-icon>
          </text>
					<view class="b-content">
						<text v-if="punchInInfo.startInTime">{{ punchInInfo.startInTime }}</text>
						<text v-else>未打卡</text>
						<text v-if="punchInInfo.startStatus" class="status"
							:class="{ 'wrong': punchInInfo.startStatus != 1 }">{{ punchInInfo.startStatus | punchName }}</text>
						<!-- <text v-if="punchInInfo.startStatus" class="reLocation"
							@click="handleUpdateClock(0)">更新打卡</text> -->
					</view>
				</view>
				<view class="right u-flex-1">
					<text class="u-font-30">
            下班{{endTime}}
            <u-icon name="checkmark" color="#1989fa" v-if="punchInInfo.endOutTime" style="position: relative;left: 6px"></u-icon>
          </text>
					<view class="b-content">
						<text v-if="punchInInfo.endOutTime">{{ punchInInfo.endOutTime }}</text>
						<text v-else>未打卡</text>
						<text v-if="punchInInfo.endStatus" class="status"
							:class="{ 'wrong': punchInInfo.endStatus != 1 }">{{ punchInInfo.endStatus | punchName }}</text>
						<text v-if="punchInInfo.endOutTime" class="reLocation" @click="handleUpdateClock(1)">更新打卡</text>
					</view>
				</view>
			</view>
			<!-- 打卡按钮 -->
			<view class="clock-btn-box u-flex u-flex-col u-row-center u-col-center">
				<view v-if="showWork" class="btn u-flex u-flex-col u-row-center u-col-center" @click="handleClock(0)">
					<text class="u-font-38">上班打卡</text>
					<text class="u-font-30 u-m-t-10" style="opacity: 0.6;">{{nTime}}</text>
				</view>
				<view v-else class="btn u-flex u-flex-col u-row-center u-col-center" @click="handleClock(1)">
					<text class="u-font-38">下班打卡</text>
					<text class="u-font-30 u-m-t-10" style="opacity: 0.6;">{{nTime}}</text>
				</view>
				<view class="adress">
					<u-icon v-if="isIn" name="checkmark-circle-fill" color="#33d04e"></u-icon>
					<u-icon v-else name="close-circle-fill" color="#ff5555"></u-icon>
					<text v-if="isIn">当前已在考勤区域内</text>
					<text v-else>当前未在考勤区域内</text>
				</view>
				<view v-if="$u.test.isEmpty(address)">
					<text>定位地址请求中</text>
					<text class="reLocation" @click="reLocation">刷新</text>
				</view>
				<view v-else class="adress">
					<u-icon name="map-fill"></u-icon>
					<text>{{address.city || '定位地址请求中'}}{{address.district}}{{address.street}}{{address.streetNum}}{{address.poiName}}</text>
					<text class="reLocation" @click="reLocation">刷新</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		filters: {
			punchName(status = 8) {
				const names = {
					0: '缺卡',
					1: '正常',
					2: '迟到',
					3: '早退',
					4: '定位异常',
					5: '迟到及定位异常',
					6: '早退及定位异常',
					7: '请假'
				}
				return names[status] ? names[status] : '缺卡'
			}
		},
		data() {
			return {
				startTime: '',
				endTime: '',
				timer: null,
				nowTimestamp: null,
				nTime: '',
				locationId: null,
				// #ifdef H5
				locationTimer: null, // H5平台定位定时器
				// #endif
				address: null,
				subLng: null,
				subLat: null,
				userType: null,
				punchInInfo: {}, // 打卡信息记录
				pageHide: false, // 页面隐藏
				showWork: true,
				polygons: [], // 打卡区域
				isIn: false, // 是否在打卡范围内
				punchArea: ''
			}
		},
		methods: {
			getConfig() {
				return new Promise(resolve => {
					this.$loading('获取配置中')
					this.$u.api.getPunchInConfigByDept({}, this.vuex_deptId).then(res => {
						resolve(res)
						uni.hideLoading()
					}).catch(() => {
						uni.hideLoading()
						this.mToase('获取相关配置失败，请退出重试')
					})
				})
			},
			setTime() {
				this.nowTimestamp = new Date().getTime();
				this.nTime = this.$u.timeFormat(this.nowTimestamp, 'hh:MM:ss')
			},
			setTimeInterval() {
				clearInterval(this.timer);
				this.timer = setInterval(() => {
					this.setTime()
				}, 1000)
			},
			// 检查定位权限和服务状态
			checkLocationPermission() {
				return new Promise((resolve, reject) => {
					// #ifdef APP-PLUS
					// 检查定位权限
					plus.android.requestPermissions(['android.permission.ACCESS_FINE_LOCATION', 'android.permission.ACCESS_COARSE_LOCATION'],
						(result) => {
							console.log('定位权限检查结果:', result);
							if (result.granted && result.granted.length > 0) {
								resolve(true);
							} else {
								reject('定位权限未授权');
							}
						},
						(error) => {
							console.log('定位权限检查失败:', error);
							reject('定位权限检查失败');
						}
					);
					// #endif
					// #ifdef H5
					// H5平台检查是否支持定位
					if (!navigator.geolocation) {
						reject('当前浏览器不支持定位功能');
						return;
					}
					// 检查是否为HTTPS环境
					if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
						console.warn('H5定位需要HTTPS环境，当前为HTTP可能导致定位失败');
					}
					resolve(true);
					// #endif
					// #ifndef APP-PLUS || H5
					resolve(true);
					// #endif
				});
			},
      // 刷新位置
      reLocation() {
        if (this.isApp) {
          this.$loading('正在获取定位信息')
          window.parent.postMessage(
            {
              cmd: "getPosition",
              options: {
                type: "gcj02",
              },
            },
            "*"
          );
          const watchPosition = (event) => {
            if (event.data.cmd == "getPosition") {
              if (event.data.value && event.data.value.longitude) {
                let res = event.data.value;
                uni.hideLoading()
                this.subLng = res.longitude;
                this.subLat = res.latitude;
                // 调用地图API获取详细地址
                this.getAddressByCoords(this.subLng, this.subLat);
                this.isIn = this.isInArea();
                setTimeout(() => {
                  window.removeEventListener("message", watchPosition);
                }, 2000);
              }
            }
          };
          window.addEventListener("message", watchPosition);
        } else {
          this.$loading('正在获取定位信息')
          navigator.geolocation.getCurrentPosition(
            (position) => {
              uni.hideLoading()
              this.subLng = position.coords.longitude;
              this.subLat = position.coords.latitude;
              // 调用地图API获取详细地址
              this.getAddressByCoords(this.subLng, this.subLat);
              this.isIn = this.isInArea();
            },
            (error) => {
              uni.hideLoading()
              this.mToase("定位地址获取失败，请重试");
              this.subLng = null;
              this.subLat = null;
              this.address = null;
              this.isIn = this.isInArea();
            },
            {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 5000,
            }
          );
        }
      },

      // 根据经纬度获取地址信息（使用高德地图API）
      getAddressByCoords(lon, lat) {
        this.$loading('获取地址信息...')
        // 如果没有加载过高德地图API，则先加载
        const script = document.createElement("script");
        script.type = "text/javascript";
        script.src =
          "https://webapi.amap.com/maps?v=2.0&key=b7a3da85edcdeecd9ae06c1a80f150c0&security=1";

        // 添加安全密钥配置
        window._AMapSecurityConfig = {
          securityJsCode: "758307c13c7a89718b29b7322c7b1bee",
        };

        script.onload = () => {
          this.convertCoordsToAddress(lon, lat);
        };
        script.onerror = () => {
          uni.hideLoading()
          this.mToase("地图加载失败");
          this.address = {
            city: "未知",
            district: "未知",
            street: "未知",
            streetNum: "",
            poiName: `(${lon.toFixed(6)}, ${lat.toFixed(6)})`,
          };
        };
        document.head.appendChild(script);
      },

      // 使用高德地图API进行坐标转换和地址解析
      convertCoordsToAddress(lon, lat) {
        // 使用高德地图的地理编码功能
        window.AMap.plugin("AMap.Geocoder", () => {
          const geocoder = new window.AMap.Geocoder();
          const lnglat = [lon, lat];

          geocoder.getAddress(lnglat, (status, result) => {
            uni.hideLoading()

            if (status === "complete" && result.info === "OK") {
              // 解析成功，获取地址
              if (result.regeocode && result.regeocode.addressComponent) {
                const addressComp = result.regeocode.addressComponent;
                this.address = {
                  city: addressComp.city || addressComp.province,
                  district: addressComp.district || "",
                  street: addressComp.township || addressComp.street || "",
                  streetNum: addressComp.streetNumber || "",
                  poiName:
                    result.regeocode.pois && result.regeocode.pois.length > 0
                      ? result.regeocode.pois[0].name
                      : result.regeocode.formattedAddress || "",
                };
                console.log("地址解析成功:", this.address);
              } else {
                this.address = {
                  city: "未知",
                  district: "未知",
                  street: "未知",
                  streetNum: "",
                  poiName: `(${lon.toFixed(6)}, ${lat.toFixed(6)})`,
                };
              }
            } else {
              // 解析失败，使用默认格式
              console.error("地址解析失败:", result);
              this.address = {
                city: "未知",
                district: "未知",
                street: "未知",
                streetNum: "",
                poiName: `(${lon.toFixed(6)}, ${lat.toFixed(6)})`,
              };
              this.mToase("地址解析失败");
            }
            // 更新是否在打卡范围内的状态
            this.isIn = this.isInArea();
          });
        });
      },

			getLocation() {
				// #ifdef APP-PLUS
				// 清除之前的定位监听
				if (this.locationId) {
					plus.geolocation.clearWatch(this.locationId);
					this.locationId = null;
				}

				// 先检查定位权限再开始监听
				this.checkLocationPermission().then(() => {
					this.locationId = plus.geolocation.watchPosition(e => {
						console.log('持续定位获取成功:', e);
						this.address = e.address;
						this.subLng = e.coords.longitude;
						this.subLat = e.coords.latitude;
						this.isIn = this.isInArea()
					}, (err) => {
						console.log('持续定位获取失败:', err);
						this.address = null;
						this.subLng = null;
						this.subLat = null;
						this.isIn = this.isInArea()

						let errorMsg = '定位获取失败，请确认是否开启定位权限';
						if (err.message) {
							if (err.message.includes('权限')) {
								errorMsg = '定位权限被拒绝，请在设置中开启定位权限';
							} else if (err.message.includes('超时')) {
								errorMsg = '定位超时，请检查网络连接';
							} else if (err.message.includes('服务')) {
								errorMsg = '定位服务不可用，请检查是否开启定位服务';
							}
						}

						uni.showToast({
							title: errorMsg,
							icon: 'none',
							position: 'bottom',
							duration: 3000
						})
					}, {
						provider: 'amap',
						enableHighAccuracy: true,
						timeout: 15000,  // 增加超时时间
						maximumAge: 3000,  // 减少缓存时间，获取更新的位置
						coordsType: 'gcj02',
						geocode: true
					});
				}).catch((error) => {
					console.log('定位权限检查失败:', error);
					uni.showToast({
						title: error || '定位权限检查失败',
						icon: 'none',
						position: 'bottom'
					});
				});
				// #endif

				// #ifdef H5
				// H5平台使用定时器模拟持续定位
				this.startH5LocationWatch();
				// #endif
			},

			// #ifdef H5
			// H5平台持续定位
			startH5LocationWatch() {
				// 清除之前的定时器
				if (this.locationTimer) {
					clearInterval(this.locationTimer);
					this.locationTimer = null;
				}

				// 立即获取一次定位
				this.reLocation();

				// 每30秒更新一次定位
				this.locationTimer = setInterval(() => {
					if (!this.pageHide) { // 只在页面显示时更新定位
						this.reLocation();
					}
				}, 30000);
			},

			// 停止H5定位监听
			stopH5LocationWatch() {
				if (this.locationTimer) {
					clearInterval(this.locationTimer);
					this.locationTimer = null;
				}
			},
			// #endif
			handleUpdateClock(punchType) {
				uni.showModal({
					title: '提示',
					content: '是否确认更新打卡？',
					success: ({ confirm }) => {
						if (confirm) {
							this.handleClock(punchType)
						}
					}
				})
			},
			handleClock(punchType) {
				let params = {
					type: this.userType,
					userId: this.vuex_id,
					username: this.vuex_nickName,
					punchType: punchType,
					positionStatus: this.isIn ? 0 : 1
				}
				if (!this.subLng || !this.subLat) {
					uni.showModal({
						title: '提示',
						content: '未获取到经纬度信息，请确认是否开启定位',
						showCancel: false
					})
					return
				} else if (!this.isIn) {
					uni.showModal({
						title: '提示',
						content: '当前未在打卡区域内，请在打卡区域内打卡',
						showCancel: false
					})
					return
				}
				if (punchType == 0) {
					// 上班打卡
					// params.actualStartTime = this.$u.timeFormat(this.nowTimestamp, 'yyyy-mm-dd hh:MM:ss');
					params.startLongitude = this.subLng;
					params.startLatitude = this.subLat;
					params.startAddr = this.address ? `${this.address.city}${this.address.district}${this.address.street}${this.address.streetNum}${this.address.poiName}` : '';
				} else {
					// 下班打卡
					// params.actualEndTime = this.$u.timeFormat(this.nowTimestamp, 'yyyy-mm-dd hh:MM:ss');
					params.endLongitude = this.subLng;
					params.endLatitude = this.subLat;
					params.endAddr = this.address ? `${this.address.city}${this.address.district}${this.address.street}${this.address.streetNum}${this.address.poiName}` : '';
				}
				const punchInFn = this.punchInInfo.punchId ? this.$u.api.editPunchIn : this.$u.api.addPunchIn
				if (this.punchInInfo.punchId) {
					params.punchId = this.punchInInfo.punchId
				} else {
					params.punchTime = this.$u.timeFormat(this.nowTimestamp, 'yyyy-mm-dd');
				}
				this.$loading('加载中...')
				punchInFn(params).then(res => {
					uni.hideLoading()
					uni.showToast({
						title: '打卡成功',
						mask: true,
						duration: 2000,
						success: () => {
							setTimeout(() => {
								this.reloadPunchInfo()
							}, 2000)
						}
					})
				}).catch(() => {
					this.mToase('打卡失败，请退出重新打卡')
					uni.hideLoading()
				})
			},
			reloadPunchInfo() {
				this.$loading()
				this.$u.api.getOfficeTodayInfo({}, this.vuex_id).then(res => {
					uni.hideLoading()
					res.data.startInTime = res.data.actualStartTime ? res.data.actualStartTime.substr(10, 6) : ''
					res.data.endOutTime = res.data.actualEndTime ? res.data.actualEndTime.substr(10, 6) : ''
					this.punchInInfo = { ...this.punchInInfo, ...res.data }
				}).catch(() => {
					this.mToase('打卡记录获取失败')
					uni.hideLoading()
				})
			},
			isPointInPolygon(polygon, lng, lat) {
				var numberOfPoints = polygon.length;
				var polygonLats = [];
				var polygonLngs = [];
				for (var i = 0; i < numberOfPoints; i++) {
					polygonLats.push(polygon[i]['lat']);
					polygonLngs.push(polygon[i]['lng']);
				}

				var polygonContainsPoint = false;
				for (var node = 0, altNode = (numberOfPoints - 1); node < numberOfPoints; altNode = node++) {
					if ((polygonLngs[node] > lng != (polygonLngs[altNode] > lng)) &&
						(lat < (polygonLats[altNode] - polygonLats[node]) *
							(lng - polygonLngs[node]) /
							(polygonLngs[altNode] - polygonLngs[node]) +
							polygonLats[node]
						)
					) {
						polygonContainsPoint = !polygonContainsPoint;
					}
				}

				return polygonContainsPoint;
			},
			isInArea() {
				if (!this.subLng || !this.subLat) return false
				if (this.polygons.length) {
					let isIn = false
					for(let i=0;i<this.polygons.length;i++) {
						isIn = this.isPointInPolygon(this.polygons[i], this.subLng, this.subLat)
						if (isIn) break
					}
					return isIn
				} else {
					/* 未规定打卡区域。默认为正常 */
					return true
				}
			},
		},
		onLoad(params) {
			this.userType = params.userType
			this.setTime()
			this.setTimeInterval()
			this.$loading('数据加载中...')
			Promise.all([
				this.getConfig(),
				this.$u.api.getOfficeTodayInfo({}, this.vuex_id)
			]).then(resAry => {
				// 配置信息
				uni.hideLoading()
				// 打卡记录信息
				if (resAry[1].data) {
					resAry[1].data.startInTime = resAry[1].data.actualStartTime ? resAry[1].data.actualStartTime.substr(10, 6) : ''
					resAry[1].data.endOutTime = resAry[1].data.actualEndTime ? resAry[1].data.actualEndTime.substr(10, 6) : ''
					this.punchInInfo = resAry[1].data
				}
				/* 打卡配置 */
				if (resAry[0].data) {
					this.startTime = resAry[0].data.startTime ? resAry[0].data.startTime.substr(10, 6) : ''
					this.endTime = resAry[0].data.endTime ? resAry[0].data.endTime.substr(10, 6) : ''
					const startTimeTr = new Date(`2021/07/23 ${this.startTime}:00`).getTime()
					const nowTimeTr = new Date(`2021/07/23 ${this.nTime}`).getTime()
					if (nowTimeTr - startTimeTr > 14400000) {
						// 当前时间超过打卡时间4小时，不再显示上班打卡
						this.showWork = false
						/* 超出上午打卡范围，但是没有上午打卡状态，默认设置为缺卡 */
						if (!this.punchInInfo.startStatus) this.punchInInfo.startStatus = '0'
					}
					// 遍历返回组好的打卡区域
					this.polygons = resAry[0].data.punchArea.split('/').map(poy => {
						return poy.split(';').map(lnglat => {
							const ll = lnglat.split(',')
							return { lng: parseFloat(ll[0]), lat: parseFloat(ll[1]) }
						})
					})
					this.punchArea = resAry[0].data.punchArea
					this.getLocation()
				} else {
					this.mToase('未配置打卡信息，请联系管理员')
				}
			}).catch(() => {
				this.mToase('数据获取失败，请退出重试')
				uni.hideLoading()
			})
		},
		onUnload() {
			clearInterval(this.timer);
			// #ifdef APP-PLUS
			if (this.locationId) plus.geolocation.clearWatch(this.locationId);
			// #endif
			// #ifdef H5
			this.stopH5LocationWatch();
			// #endif
		},
		onShow() {
			if (this.pageHide) {
				this.pageHide = false
				this.setTime()
				this.setTimeInterval()
				this.reLocation()
				// 避免重复启动定位监听，只在没有locationId时启动
				// #ifdef APP-PLUS
				if (!this.locationId) {
					this.getLocation()
				}
				// #endif
				// #ifdef H5
				// H5平台重新启动定位监听
				this.startH5LocationWatch();
				// #endif
			}
		},
		onHide() {
			this.pageHide = true
			clearInterval(this.timer);
			// #ifdef APP-PLUS
			if (this.locationId) plus.geolocation.clearWatch(this.locationId);
			// #endif
			// #ifdef H5
			this.stopH5LocationWatch();
			// #endif
		},

	}
</script>

<style lang="scss">
	page {
		height: 100%;
	}

	.top {
		height: 145rpx;
		padding: 35rpx;
		background-color: #fff;
		margin: 0 30rpx 30rpx;
		border-radius: 20rpx;

		.left {
			width: 75rpx;
			height: 75rpx;
			background-color: #3395f8;
			color: #fff;
			font-size: 26rpx;
			border-radius: 20rpx;
			margin-right: 30rpx;
		}
	}

	.content {
		height: calc(100% - 205rpx);
		background-color: #FFFFFF;
		border-radius: 20rpx;
		margin: 0 30rpx;

		.clock-list {
			padding: 30rpx;

			.left,
			.right {
				background-color: #f4f4f4;
				padding: 20rpx;
				border-radius: 20rpx;

				.b-content {
					font-size: 24rpx;
					color: #a0a0a0;
				}
			}
		}

		.clock-btn-box {
			height: calc(100% - 180rpx);
		}

		.btn {
			width: 275rpx;
			height: 275rpx;
			border-radius: 50%;
			background-color: #3395f8;
			color: #fff;
			box-shadow: 0 2px 6px #83ecfc;
			margin-bottom: 30rpx;
			transition: all 0.3s;

			&:active {
				opacity: 0.3;
			}
		}

		.adress {
			color: #717171;
			font-size: 28rpx;
		}

		.reLocation {
			color: #3395f8;
			margin-left: 10rpx;
		}

		.status {
			border: 1px solid #19BE6B;
			font-size: 24rpx;
			color: #19BE6B;
			padding: 0 2px;
			border-radius: 2px;
			display: inline-block;
			transform: scale(0.8);
		}

		.wrong {
			border: 1px solid #ffb100;
			color: #ffb100;
		}
	}
</style>

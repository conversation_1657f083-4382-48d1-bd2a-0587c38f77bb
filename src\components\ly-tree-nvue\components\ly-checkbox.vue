<template>
	<text class="ly-check-icon" :class="[iconClass]" :style="{color: iconColor}"
	 @click.stop="handleClick">{{icons[iconClass]}}</text>
</template>

<script>
	import icons from './icons.js';
	
	// #ifdef APP-NVUE
	var domModule = weex.requireModule('dom');
	domModule.addRule('fontFace', {
		'fontFamily': "lyicons",
		'src': "url('data:font/truetype;charset=utf-8;base64,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')"
	});
	// #endif
	
	export default {
		props: {
			type: {
				type: String,
				validator(t) {
					return t === 'radio' || t === 'checkbox'
				}
			},
			checked: Boolean,
			disabled: Boolean,
			indeterminate: Boolean
		},
		
		data() {
			return {
				icons: icons
			}
		},
		
		computed: {
			iconClass() {
				if (this.type === 'checkbox') {
					if (this.indeterminate) return 'icon-indeterminate';
					
					if (!this.checked && !this.disabled) return 'icon-unchecked';
					
					if (this.checked) return 'icon-checked';
					
					if (this.disabled) return 'icon-check-disabled';
				} else {
					if (!this.checked) return 'icon-radio-unchecked';
					
					if (this.checked) return 'icon-radio-checked';
					
					if (this.disabled) return 'icon-radio-disabled';
				}
			},
			
			iconColor() {
				if (this.disabled) return '#d2d7e2';
				
				if (this.checked || this.indeterminate) return '#409EFF';
				
				return '#DCDFE6';
			}
		},

		methods: {
			handleClick() {
				this.$emit('check', this.checked);
			}
		}
	}
</script>

<style>
	/* #ifndef APP-NVUE */
	@font-face {
		font-family: "lyicons";
		src: url('data:font/truetype;charset=utf-8;base64,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') format('truetype');
	}
	/* #endif */
	
	.ly-check-icon {
		font-family: lyicons;
		font-size: 36rpx;
		font-style: normal;
		text-decoration: none;
		text-align: center;
		font-weight: 500;
		padding-right: 16rpx
	}
	
	.icon-radio-checked,
	.icon-radio-unchecked {
		font-size: 38rpx;
	}
</style>

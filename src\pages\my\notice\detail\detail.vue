<template>
	<view class="u-skeleton">
		<view class="container-top u-border-top">
			<view class="title">
				<text class="u-line-2 u-skeleton-rect">{{ detailData.noticeTitle }}</text>
			</view>
			<view class="b-title jg-icon">
				<text class="u-skeleton-rect">发布人员：</text>
				<text class="u-skeleton-rect">{{ detailData.userName }}</text>
			</view>
			<view class="b-title sz-icon">
				<text class="u-skeleton-rect">发布时间：</text>
				<text class="u-skeleton-rect">{{ detailData.updateTime ? detailData.updateTime : detailData.createTime }}</text>
			</view>
		</view>
		<u-gap bg-color="#F5F5F5" style="height: 10px;"></u-gap>
		<!-- 主体 -->
		<view class="container">
			<scroll-view class="u-skeleton-rect" :scroll-y="true" :show-scrollbar="true" style="height: calc(100vh - 155px);">
				<u-parse :html="detailData.noticeContent" :show-with-animation="true"></u-parse>
			</scroll-view>
		</view>
		<u-skeleton :loading="loading" :animation="true" bgColor="#FFF"></u-skeleton>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				detailId: null,
				detailData: {
					title: '占位数据',
					issueDept: '占位数据',
					issueTime: '2020-01-01',
					implementationTime: '2020-01-01',
					content: '占位数据'
				},
				loading: true
			}
		},
		methods: {
			fetchData() {
				this.$loading()
				this.$u.api.getNoticeDetail({}, this.detailId).then(res => {
					uni.hideLoading()
					this.loading = false
					if (res.data) {
						this.detailData = res.data
					}
				}).catch(() => {
					uni.hideLoading()
					this.loading = false
				})
			}
		},
		onLoad(params) {
			this.detailId = params.id
			this.fetchData()
		}
	}
</script>

<style lang="scss">
.container-top {
	background-color: #fff;
	padding-top: 15px;
	padding-bottom: 15px;
	.title {
		font-size: 18px;
		font-weight: 700;
		margin-bottom: 10px;
		padding: 0 15px;
	}
	.b-title {
		font-size: 12px;
		color: #808080;
		margin-bottom: 6px;
		padding: 0 15px 0 35px;
		&.jg-icon {
			background: url(../../../../static/img/type-icon.png) no-repeat 15px center / contain;
		}
		&.sz-icon {
			background: url(../../../../static/img/sz.png) no-repeat 15px center / contain;
		}
	}
}
.container {
	background-color: #FFFFFF;
	padding: 0 30rpx 15px;
	overflow-y: auto;
}
</style>

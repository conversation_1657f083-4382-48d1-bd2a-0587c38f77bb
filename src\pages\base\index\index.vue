<template>
	<view style="overflow: hidden;">
		<view class="banner">
			<text class="title">知识库</text>
			<u-image src="@/static/img/home_banner.png" width="750rpx" height="290rpx"></u-image>
		</view>
		<view class="container">
			<!-- 第一部分 -->
			<view class="container-item u-p-t-20">
				<text class="title">知识库</text>
				<view class="item-list">
					<view class="item" v-for="(item, index) in itemList" :key="index" @click="handleOpen(item.url)">
						<view class="item-inner" :style="{ backgroundColor: item.color }">
							<u-image :src="item.src" width="60rpx" height="60rpx"></u-image>
							<text class="name">{{ item.name }}</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 第二部分 -->
			<view class="container-item u-p-t-30">
				<text class="title">学习成长</text>
				<view class="item-list">
					<view class="lh-item" :style="{ backgroundColor: item.color }" v-for="(item, index) in thItemList" :key="index" @click="handleOpen(item.url)">
						<u-image class="img" :src="item.src" width="60rpx" height="60rpx"></u-image>
						<view class="text-content">
							<text class="name">{{ item.name }}</text>
							<text class="btn">进入操作></text>
						</view>
					</view>
				</view>
			</view>
			<!-- 第三部分 -->
			<view class="container-item u-p-t-30">
				<text class="title">典型案例</text>
				<view class="item-list">
					<scroll-view scroll-y="true" class="dh" @scrolltolower="scrolltolower">
						<view class="case-item u-p-l-30 u-p-r-30" v-for="item in dataList" :key="item.id" @click="$u.route({ url: 'pages/base/dxal/dxal', params: { id: item.id } })">
							<view class="u-p-t-30">
								<text class="u-line-2 u-font-b u-font-30">{{ item.title }}</text>
							</view>
							<view class="u-flex u-row-between u-p-t-15 u-p-b-30 u-border-bottom u-font-24" style="color: #AAAAAA;">
								<text>{{ item.typeName }}</text>
								<text>发布时间{{ item.createTime }}</text>
							</view>
						</view>
						<u-loadmore :status="status" class="u-m-t-20 u-m-b-20" />
					</scroll-view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				itemList: [
					{ key: 'xcfx', src: '../../../static/img/flfg.png', name: '法律法规', color: '#F1F4FF' , url:'pages/base/zsk/flfg/list/list' },
					{ key: 'jycx', src: '../../../static/img/gzzd.png', name: '规章制度', color: '#FFF2F1' , url:'pages/base/zsk/gzzd/list/list'  },
					{ key: 'jkzp', src: '../../../static/img/cjwd.png', name: '常见问答', color: '#EBF8F2' , url:'pages/base/study/cjwd/qaList/qaList'}
				],
				thItemList: [
					{ key: 'rwfb', src: '../../../static/img/rcdt.png', name: '日常答题', color: '#FFF4E2' , url: 'pages/base/study/rcdt/list/list'  },
					{ key: 'zfdc', src: '../../../static/img/czjl.png', name: '成长激励', color: '#EBF8F2', url: 'pages/base/study/czjl/list/list' },
				],
				status: 'loadmore',
				pageNum: 1,
				pageSize: 10,
				dataList: []
			}
		},
		methods: {
			handleOpen(url) {
				if (url) {
					this.$u.route({ url })
				}
			},
			scrolltolower() {
				console.log('12s')
			},
			fetchData() {
				this.status = 'loading'
				const { pageNum, pageSize } = this
				this.$u.api.getClassicCaseList({ pageNum, pageSize, orderByColumn: 'createTime', isAsc: 'desc' }).then(res => {
					console.log(res)
					if (pageNum == 1) {
						this.dataList = res.rows
						uni.stopPullDownRefresh()
					} else {
						this.dataList = this.dataList.concat(res.rows)
					}
					this.status = res.rows.length < pageSize ? 'nomore' : 'loading'
				}).catch(() => {
					this.status = 'loadmore'
					uni.stopPullDownRefresh()
				})
			}
		},
		onLoad() {
			this.fetchData()
		},
		onPullDownRefresh() {
			this.pageNum = 1
			this.fetchData()
		},
	}
</script>

<style lang="scss">
page {
	background: #fff;
}
.banner {
	position: absolute;
	.title {
		font-size: 40rpx;
		color: #fff;
		position: absolute;
		top: calc(23rpx + var(--status-bar-height));
		left: 30rpx;
		z-index: 10;
		font-weight: 700;
	}
}
.name {
	font-size: 26rpx;
	font-weight: 400;
}
.dh {
	height: calc(100vh - 800rpx - var(--status-bar-height));
	/* #ifdef H5 */
	height: calc(100vh - 910rpx);
	/* #endif */
}
.container {
	background-color: #fff;
	margin-top: calc(118rpx + var(--status-bar-height));
	position: relative;
	z-index: 15;
	border-radius: 36rpx 36rpx 0px 0px;
	padding-bottom: 30rpx;
	.container-item {
		padding: 0 30rpx;
		.title {
			height: 80rpx;
			font-size: 36rpx;
			color: #333333;
			font-weight: 700;
			display: flex;
			align-items: center;
			margin-bottom: 10rpx;
		}
		.item-list {
			display: flex;
			flex-wrap: wrap;
			margin: 0 -6rpx;
			.item {
				flex: 0 1 33.3%;
				&-inner {
					padding-left: 20rpx;
					height: 140rpx;
					display: flex;
					flex-direction: column;
					justify-content: center;
					margin: 0 6rpx 14rpx;
					border-radius: 10rpx;
					transition: opacity .3s;
					&:active {
						opacity: 0.3;
					}
					.name {
						color: #333;
						margin-top: 10rpx;
					}
				}
			}
		}
		.lh-item {
			height: 140rpx;
			flex: 1;
			border-radius: 10rpx;
			padding-left: 40rpx;
			margin: 0 6rpx 14rpx;
			display: flex;
			align-items: center;
			transition: opacity .3s;
			&:active {
				opacity: 0.3;
			}
			.img {
				flex-shrink: 0;
				margin-right: 20rpx;
			}
			.text-content {
				display: flex;
				flex-direction: column;
				.btn {
					font-size: 22rpx;
					color: #888888;
				}
			}
		}
	}
}
</style>

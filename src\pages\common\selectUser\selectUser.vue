<template>
	<view>
		<view class="content">
			<ly-tree
				ref="tree"
				node-key="id"
				:ready="ready"
				:props="props"
				:tree-data="treeData"
				:check-on-click-node="true"
				:show-radio="showRadio"
				:show-checkbox="!showRadio"
				:check-strictly="false"
				:default-checked-keys="defaultCheckedKeys"
				:default-expanded-keys="defaultExpandedKeys"
				:is-inject-parent-in-node="true"
			>
			</ly-tree>
		</view>

		<!-- 确认按钮 -->
		<view class="btns">
			<u-button type="primary" shape="circle" :custom-style="subStyle" class="btn u-m-t-24" @click="submit">确认</u-button>
		</view>
	</view>
</template>

<script>
import LyTree from '@/components/ly-tree/ly-tree.vue'
export default {
  components: {
    LyTree
  },
  data() {
    return {
      isJurisdiction: 1,
      typeList: '',
      subStyle: {
        height: '86rpx',
        backgroundColor: '#327BF0'
      },
      showRadio: true,
      treeData: [],
      type: '',
      ready: true,
      defaultCheckedKeys: [],
      defaultExpandedKeys: [],
      props: {
        disabled: (data) => {
          if (this.showRadio) {
            return data.type === 'd' || data.type === 'D'
          } else {
            return data.type === 'D'
          }
        }
      },
      name: '',
      from: ''
    }
  },

  onLoad(param = {}) {
    // @ showRadio   是否为单选，默认单选，多选传0
    // @ type        若上层页面有多个人员选择，用以区分不同人员选择
    this.showRadio = param.showRadio != 0
    this.type = param.type || ''
    this.name = param.name
    this.from = param.from || ''
    this.isJurisdiction = param.isJurisdiction == 0 ? param.isJurisdiction : 1
    this.typeList = param.typeList || ''

    if (param.defaultCheckedKeys) {
      this.defaultCheckedKeys = param.defaultCheckedKeys.split(',')
      if (this.name != 'anyou' && this.name != 'bumen') this.defaultCheckedKeys = this.defaultCheckedKeys.map(key => `${key}u`)
    }
    if (param.defaultExpandedKeys) {
      this.defaultExpandedKeys = param.defaultExpandedKeys.split(',')
      if (this.name != 'anyou' && this.name != 'bumen') this.defaultExpandedKeys = this.defaultExpandedKeys.map(key => `${key}u`)
    }
    this.fetchData(this.name)
  },
  methods: {
    treeMap(item) {
      if (!item) return false
			  const haveChildren = Array.isArray(item.children) && item.children.length > 0
			  const tree = {
        id: `${item.id}${item.type}`,
        label: item.label,
        parentId: item.parentId,
        type: item.type
			  }
			  if (haveChildren) tree.children = item.children.map(i => this.treeMap(i))
			  return tree
    },
    fetchData(name) {
      uni.showLoading({ title: '数据加载中...', mask: true })
      this.ready = false
      const params = {}

      // 暂时注释
      // if (this.type == 'summaryType') {
      // 	params.summaryType = 1
      // }

      if (this.isJurisdiction == 0) params.isJurisdiction = this.isJurisdiction
      if (this.typeList) params.typeList = this.typeList
      console.log('anyou', name)
      const api = name == 'anyou' ? this.$u.api.summaryList(params) : name == 'bumen' ? this.$u.api.deptTreeselect() : this.$u.api.deptUsertreeselect(params)
      api.then(res => {
        if (name != 'anyou' && name != 'bumen') {
          this.treeData = res.data.map(item => this.treeMap(item))
          this.treeData = this.treeData.filter(item => item.label === '站前区域综合管理中心')
          // if (this.from === 'xcfxrcxc' || this.from === 'rcxc') {
          //   this.treeData = this.treeData.filter(item => item.label === '站前区域综合管理中心')
          // }
        } else {
          this.treeData = res.data
        }
        this.ready = true
        if (this.$u.test.isEmpty(this.defaultExpandedKeys)) {
          this.defaultExpandedKeys = [this.treeData[0].id]
        }
        uni.stopPullDownRefresh()
        uni.hideLoading()
      }).catch(() => {
        this.ready = true
        uni.hideLoading()
        uni.stopPullDownRefresh()
      })
    },
    submit() {
      let checkeds = this.$refs.tree.getCheckedNodes()
      let parent = null
      if (checkeds[0]) parent = this.$refs.tree.getNode(checkeds[0].id).parent
      // 处理ID
      if (this.name != 'anyou' && this.name != 'bumen') {
        checkeds = checkeds.map(item => {
          item.id = parseInt(item.id)
          return item
        })
        if (parent && parent.data && parent.data.id) parent.data.id = parseInt(parent.data.id)
      }

      // console.log(checkeds)
      checkeds = checkeds.filter(item => item.type != 'd')
      const pages = getCurrentPages() // 获取所有页面栈实例列表
      const prevPage = pages[ pages.length - 2 ] // 上一页页面实例
      if (prevPage.$vm.setUserData) {
        console.log(checkeds, this.type, parent)
        prevPage.$vm.setUserData(checkeds, this.type, parent)
      }
      uni.navigateBack()
    }
  },
  onPullDownRefresh() {
    this.fetchData(this.name)
  }
}
</script>

<style>
	.checkbox-box {
		width: 100%;
	}
	.checkbox-item {
		padding: 24rpx;
		width: 100%;
	}
	.u-checkbox__label, .u-radio__label {
		width: 100%;
	}
	.content {
		position: absolute;
		bottom: 124rpx;
		top: 0;
		overflow-y: auto;
		width: 100%;
	}
	.btns {
		position: fixed;
		bottom: 0rpx;
		width: 100%;
		height: 124rpx;
		padding: 0 30rpx;
		z-index: 100;
	}
</style>

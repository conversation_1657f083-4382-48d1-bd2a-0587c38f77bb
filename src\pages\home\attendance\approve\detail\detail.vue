<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="150" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="请假人:" prop="createName" required>
					<u-input v-model="form.createName" type="popup" />
				</u-form-item>
				<u-form-item label="请假类型:" prop="type" required>
					<u-input v-model="form.type" placeholder="请选择请假类型" disabled type="select" />
					<!-- <u-action-sheet v-model="showLeaveTypeList" :list="leaveTypeList" @click="leaveTypeClick"></u-action-sheet> -->
				</u-form-item>
				<u-form-item label="标题:" prop="title" required>
					<u-input v-model="form.title" type="popup" />
				</u-form-item>
				<u-form-item label="原因:" prop="reason" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.reason" type="textarea" disabled placeholder="原因" />
				</u-form-item>
				<u-form-item label="开始时间:" prop="leaveStartTime" required>
					<u-input v-model="form.leaveStartTime" type="popup" disabled placeholder="请选择开始时间" />
					<u-icon name="calendar"  size="40"></u-icon>
					<!-- <u-picker v-model="showLeaveStartTime" mode="time" :default-time="form.leaveStartTime" :params="params"  @confirm="confirmLeaveStartTime"></u-picker> -->
				</u-form-item>
				<u-form-item label="结束时间:" prop="leaveEndTime" required>
					<u-input v-model="form.leaveEndTime" type="popup" disabled placeholder="请选择结束时间" />
					<u-icon name="calendar"  size="40"></u-icon>
					<!-- <u-picker v-model="showLeaveEndTime" mode="time" :default-time="form.leaveEndTime" :params="params"  @confirm="confirmLeaveEndTime"></u-picker> -->
				</u-form-item>
				<u-form-item label="请假天数:" prop="leaveDay" required>
					<u-input v-model="form.leaveDay" disabled type="number" />天
				</u-form-item>
			</view>
			
			<view v-for="(historyData, index) in fromData" :key="index">
				<!-- 间隔 -->
				<u-gap height="20" bg-color="#F5F5F5"></u-gap>
				<view class="p-lr-30">
					<h3 style="padding: 20rpx 0;">{{ historyData.taskNodeName }} :</h3>
					<u-form-item label="审批人:">
						<u-input v-model="historyData.createName" disabled />
					</u-form-item>
					<u-form-item label="审批时间:">
						<u-input v-model="historyData.createdDate" disabled />
					</u-form-item>
					<view v-for="(fistoryFormData, indexH) in historyData.formHistoryDataDTO" :key="indexH" label-width="80px">
						<u-form-item label="审批意见:" v-if="fistoryFormData.title == '审批意见'">
							<u-input v-model="fistoryFormData.value" disabled />
						</u-form-item>
						<u-form-item label="批注:" label-position="top" :border-bottom="false" v-else>
							<u-input v-model="fistoryFormData.value" type="textarea" disabled />
						</u-form-item>
					</view>
				</view>
			</view>
			
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<h3 style="padding: 20rpx 0;">{{ form.name }} :</h3>
				<u-form-item label="审批人:">
					<u-input v-model="form.approverName " disabled />
				</u-form-item>
				
				<view v-for="(domain, index) in form.formData" :key="index">
					<!-- <u-form-item :label="`${domain.controlLable}:`" label-position="top" :border-bottom="false"> -->
					<u-form-item label="审批意见:" v-if="'radio'==domain.controlType" required>
						<u-radio-group v-model="domain.controlValue">
							<u-radio 
								v-for="(defaults,indexd) in domain.controlDefault.split('--__--')"
								:key="indexd"
								:name="indexd + ''"
							>
								{{ defaults }}
							</u-radio>
						</u-radio-group>
					</u-form-item>
					
					<u-form-item label="批注:" v-if="'textarea'==domain.controlType" label-position="top" :border-bottom="false">
						<u-input v-model="domain.controlValue" type="textarea"  placeholder="请输入批注"></u-input>
					</u-form-item>
				</view>
				
			</view>
			
		</u-form>
		
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex">
			<u-button class="u-flex-1" type="primary" shape="circle" :loading="aloading" :custom-style="subStyle" @click="handleOver">提交</u-button>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id:'',
				value:'',
				leaveTypeList:[],
				showLeaveTypeList:false,
				aloading: false,
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				form: {},
				fromData: [],
				rules: {},
				showLeaveStartTime: false,
				showLeaveEndTime: false
			}
		},
		computed: {
		},		
		methods: {
			handleSubmit(status) {
				this.aloading = true
				this.$loading()
				this.$u.api.formDataSave(this.form.formData, this.id).then(res => {
						uni.hideLoading()
						uni.showToast({title: '操作成功'})
						this.aloading = false
						this.$implement()
				}).catch(() => {
					uni.hideLoading()
					this.aloading = false
				})
			},
			handleOver() {
				console.log(this.form.formData)
				this.$refs.uForm.validate(valid => {
					if (valid) {
						uni.showModal({
							title: '提示',
							content: '是否确认提交？',
							success: ({ confirm }) => {
								if (confirm) {
									this.handleSubmit()
								}
							}
						})
					}
				})
				
			}
		},
		async onLoad(params) {
			this.id = params.id
			this.$loading()
			const [standardRes, leavApproveRes, formDataRes] = await Promise.all([
				this.$u.api.getLeave({}, params.businessKey),
				this.$u.api.getLeavApprove({}, params.businessKey),
				this.$u.api.formDataShow({}, params.id)
			])
			// const standardRes = await this.$u.api.getLeave({}, params.id)			
			/* 数据请求完毕 */
			uni.hideLoading()
			if (standardRes.code == 200 && leavApproveRes.code == 200 && formDataRes.code == 200) {
				/* 表单数据 */
				this.form = { ...standardRes.data, name: params.name, approverName: this.vuex_nickName }
				this.fromData = leavApproveRes.data
				
				// 处理当前审批人员 审批数据
				let formData = []
				let datas = formDataRes.data
				console.log(datas)
				for (let i = 0; i < datas.length; i++) {
					let strings = datas[i].split('--__!!')
					console.log(strings)
					let controlValue = ''
					let controlDefault = null
					switch (strings[1]) {
						case 'radio':
							controlValue = '0'
							controlDefault = strings[4]
							break
							// default:
					}
					formData.push({
						controlId: strings[0],
						controlType: strings[1],
						controlLable: strings[2],
						controlIsParam: strings[3],
						controlValue: controlValue,
						controlDefault: controlDefault
					})
				}
				this.form.formData = formData
				console.log(this.form.formData)

			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.pos-r {
	height: 50rpx;
	line-height: 50rpx;
	font-size: 24rpx;
	background-color: #2979ff;
	border-radius: 10rpx;
	position: absolute;
	top: 30rpx;
	right: 0;
	color: #FFFFFF;
	padding: 0 15rpx;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
// .u-field{
// 	padding: 0;
// 	width: 690rpx;
// }
</style>

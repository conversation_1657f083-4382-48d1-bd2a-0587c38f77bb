<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="150" :label-style="labelStyle">
			<!-- 申诉人 -->
			<view class="p-lr-30">
			<u-form-item label="申诉人:" prop="appealUserName" required>
				<u-input v-model="form.appealUserName" type="popup" placeholder="申诉人" />
			</u-form-item>
			</view>
			<!-- 间隔 -->
			<!-- <u-gap height="20" bg-color="#F5F5F5"></u-gap> -->
			<view class="p-lr-30">
				<u-form-item label="申诉原因:" prop="reason" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.reason"  type="textarea" disabled placeholder="请输入申诉原因" />
				</u-form-item>
			</view>

			<!-- 申诉结果 -->
			<!-- 间隔 -->
			<u-gap height="5" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="申诉结果:" prop="result" label-position="top"  :border-bottom="false" required>
					<u-input v-model="form.result" :disabled="disable" type="textarea" placeholder="请输入申诉结果" />
				</u-form-item>
			</view>

			<u-gap height="40" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="督查人:" prop="userName" required>
					<u-input v-model="form.userName" type="popup" placeholder="督查人" />
				</u-form-item>
				<u-form-item label="事件描述:" prop="content" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.content" disabled type="textarea" placeholder="请输入事件描述" />
				</u-form-item>

				<view class="u-rela">
					<u-form-item label="考核条款:" prop="checkStandardName" label-position="top" required>
						<u-input v-model="form.checkStandardName" disabled type="textarea" placeholder="请选择考核条款" />
					</u-form-item>
					<!-- <view class="pos-r" @click="handleChooseStandar">选择项目</view> -->
				</view>
				<u-form-item label="考核标准:" prop="checkStandardContent">
					<u-input v-model="form.checkStandardContent" type="popup" placeholder="请选择考核标准" />
				</u-form-item>
				<u-form-item label="积分类型:" prop="type" required>
					<u-radio-group v-model="form.type" disabled>
						<u-radio name="1">扣分</u-radio>
						<u-radio name="0">加分</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="积分值:" prop="checkStandardScore" required>
					<u-input v-model="form.checkStandardScore" disabled type="number" placeholder="请输入积分值" />
				</u-form-item>
			</view>

			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="考核时间:" prop="happenTime" required>
					<u-input v-model="form.happenTime" type="popup" placeholder="请选择考核时间" @click="showHappenTime = true"  />
					<!-- <u-icon name="calendar"  @click="showHappenTime = true"  size="40"></u-icon>
					<u-picker v-model="showHappenTime" mode="time" :default-time="form.happenTime" :params="params"  @confirm="confirmHappenTime"></u-picker> -->
				</u-form-item>
				<u-form-item label="地址:" prop="address" required>
					<u-input v-model="form.address" type="popup" placeholder="请选择地址" />
					<!-- <u-button size="mini" type="primary" v-slot="right" @click="handleChooseAddress">选择地址</u-button> -->
				</u-form-item>
				<u-upload
					max-count="4"
					width="157"
					height="157"
					:auto-upload="false"
				  :size-type="['compressed']"
					:file-list="fileList"
					:deletable="false"
					:customBtn="true"
					name="files"
				></u-upload>
			</view>

			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<!-- form.status  1：新创建，2：推送班长，3：待反馈，9：已办结 -->
				<u-form-item label="班长:" prop="groupUserName" v-if="form.groupUserName">
					<u-input v-model="form.groupUserName" ref="monitor" type="popup" placeholder="班长" />
					<!-- <u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser({showRadio: 1, type: 'monitor'})">选择人员</u-button> -->
				</u-form-item>
				<u-form-item label="责任人:" prop="checkUserNames">
					<u-input v-model="form.checkUserNames" ref="responsible" type="popup" placeholder="责任人" />
					<!-- <u-button size="mini" type="primary"  v-if="form.status != 3" v-slot="right" @click="handleChooseHandleUser({showRadio: 0, type: 'responsible'})">选择人员</u-button> -->
				</u-form-item>
			</view>

			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="反馈内容:" prop="feedback" label-position="top" required  v-if="form.feedback">
					<u-input v-model="form.feedback"  type="textarea" disabled placeholder="请输入反馈内容" />
				</u-form-item>
				<u-upload
					max-count="4"
					width="157"
					height="157"
					:auto-upload="false"
				  :size-type="['compressed']"
					:file-list="fileList1"
					:deletable="false"
					:customBtn="true"
					name="files"
				></u-upload>
			</view>


		</u-form>

		<!-- 提交按钮 -->
			<view class="btn-box u-border-top u-flex" v-if="form.status == 1">
				<u-button class="u-flex-1 u-m-r-20" type="primary" shape="circle" :loading="aloading" :custom-style="subStyle" @click="handleAppeal(10)">驳回</u-button>
				<u-button class="u-flex-1" type="danger" shape="circle" :loading="aloading" :custom-style="subStyle1" @click="handleAppeal(9)">通过</u-button>
			</view>
			<!-- 提示 -->
			<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				disable:false,
				aloading: false,
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				subStyle1: {
					height: '86rpx',
					color:'#fff',
					backgroundColor: 'red'
				},
				form: {address:"浙江省金华市婺城区城北街道"},
				showHappenTime: false,
				fileList: [],
				fileList1: [],
				rules: {
					// reason: [{ required: true, message: '请填写申诉原因', trigger: ['change', 'blur'] }],
					result: [{ required: true, message: '请填写申诉结果', trigger: ['change', 'blur'] }]
				},
			}
		},
		methods: {
			handleAppeal(status){
				if(this.vuex_id != this.form.userId) return this.mToase('您不是督查人，没有操作权限！')
				const name = {9:'通过',10:'驳回'}
				uni.showModal({
					title: '提示',
					content: `是否${name[status]}此申诉？`,
					success: ({ confirm }) => {
						if (confirm) {
							this.$refs.uForm.validate(valid => {
								if (valid){
									this.aloading = true
									this.$loading()
									const params = { ...this.form, status }
									this.$u.api.updateAppeal(params).then(() => {
										uni.hideLoading()
										uni.showToast({title: '操作成功'})
										this.disable = true
										this.$implement()
									}).catch(() => {
										uni.hideLoading()
										this.aloading = false
									})
								}
							})
						}
					}
				})
			}
		},
		async onLoad(params) {
			if (params.id) {
				this.$loading()
				const [standardRes, appealRes,fileRes] = await Promise.all([
					this.$u.api.getStandard({}, params.checkRecordId),
					this.$u.api.getAppeal({}, params.id),
					this.$u.api.getFileList({ tableName: 'supervision_check_record', businessId: params.checkRecordId }),
				])

				/* 数据请求完毕 */
				uni.hideLoading()
				if (standardRes.code == 200 && fileRes.code == 200) {
					/* 表单数据 */
					this.form = {...standardRes.data,...appealRes.data}
					/* 文件数据 */
					fileRes.rows.forEach(item => {
						// return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
						// #ifdef H5
						const files = { url: `${item.filePath}?id=${item.fileId}` }
						// #endif
						// #ifndef H5
						const files = { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
						// #endif
						if(item.status == 1){
							this.fileList.push(files)
						}else{
							this.fileList1.push(files)
						}
					})


					// this.fileList = fileRes.rows.map(item => {
					// 	if(item.status == 1){
					// 		console.log(item)
					// 		// return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
					// 		// #ifdef H5
					// 		return { url: `${item.filePath}?id=${item.fileId}` }
					// 		// #endif
					// 		// #ifndef H5
					// 		return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
					// 		// #endif
					// 	}
					// })
					// this.fileList=this.fileList.filter(item=>item != undefined)

					// this.fileList1 = fileRes.rows.map(item => {
					// 	if(item.status == 3){
					// 		// return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
					// 		// #ifdef H5
					// 		return { url: `${item.filePath}?id=${item.fileId}` }
					// 		// #endif
					// 		// #ifndef H5
					// 		return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
					// 		// #endif
					// 	}
					// })
					// this.fileList1=this.fileList1.filter(item=>item != undefined)
				}
			} else {
				const timestamp = new Date().getTime()
				const happenTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = { type: '1', happenTime }
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.pos-r {
	height: 50rpx;
	line-height: 50rpx;
	font-size: 24rpx;
	background-color: #2979ff;
	border-radius: 10rpx;
	position: absolute;
	top: 30rpx;
	right: 0;
	color: #FFFFFF;
	padding: 0 15rpx;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

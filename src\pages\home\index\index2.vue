<template>
	<view style="overflow: hidden;">
		<view class="banner">
			<text class="title">首页</text>
			<u-image src="@/static/img/home_banner.png" width="750rpx" height="290rpx"></u-image>
		</view>
		<view class="container">
			<!-- 通知公告 -->
			<view  @click="handleOpenNotice">
				<u-notice-bar mode="horizontal" :more-icon="true" :list="noticeList"></u-notice-bar>
			</view>
			<!-- 无权限 -->
			<u-empty text="无权限" mode="permission" class="u-p-80" v-if="!this.appList.length"></u-empty>
			<!-- 第一块部分 -->
			<view class="container-item u-p-t-20" v-if="isShow('zhxzzf')">
				<text class="title">综合行政执法</text>
				<view class="item-list">
					<view class="item" v-for="(item, index) in itemList" :key="index2" @click="handleOpen(item.url)">
						<view class="item-inner" :style="{ backgroundColor: item.color }">
							<u-badge v-if="item.key === 'jkzp'" type="error" :offset="[-4,-4]" :count="msgCount.capture || 0">1</u-badge>
							<u-badge v-if="item.key === 'znzp'" type="error" :offset="[-4,-4]" :count="msgCount.autoCapture || 0"></u-badge>
							<u-image  :src="item.src" width="76rpx" height="76rpx"></u-image>
							<text class="name">{{ item.name }}</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 考勤打卡 -->
			<view class="clock" style="margin-top: 6rpx;"  v-if="isShow('kqdk')" @click="handleClickOpen">
				<u-image src="@/static/img/clock.jpg" width="750rpx" height="200rpx"></u-image>
			</view>
			<!-- 第二部分 -->
			<view class="container-item" v-if="isShow('kqgl')">
				<text class="title">考勤管理</text>
				<view class="item-list">
					<view class="item" v-for="(item, index) in attendanceItemList" :key="index2" @click="handleClickOpen(item)">
						<view class="item-inner" :style="{ backgroundColor: item.color }">
							<u-image  :src="item.src" width="76rpx" height="76rpx"></u-image>
							<text class="name">{{ item.name }}</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 第三部分 -->
			<view class="container-item" v-if="isShow('ygzf')">
				<text class="title">运管执法</text>
				<view class="item-list">
					<view class="ygzfItem" v-for="(item, index) in secItemList" :key="index2" @click="handleOpen(item.url)">
						<view class="ygzfItem-inner" :style="{ backgroundColor: item.color }">
							<u-image :src="item.src" width="46rpx" height="40rpx" style="margin-bottom: 10rpx;"></u-image>
							<text class="name">{{ item.name }}</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 第四部分 -->
			<view class="container-item u-p-t-20" v-if="isShow('jjzf')">
				<text class="title">交警执法</text>
				<view class="item-list">
					<view class="jjzfItem" v-for="(item, index) in jjzfItemList" :key="index2" @click="handleOpen(item.url)">
						<view class="jjzfItem-inner">
							<u-image :src="item.src" width="76rpx" height="76rpx"></u-image>
							<view class="jjzfItem-right">
								<text class="name">{{ item.name }}</text>
							<text class="enter">进入操作></text>
							</view>
						</view>
					</view>
				</view>
				<!-- <view style="box-shadow: 0px 20px 10px -10px rgba(55, 23, 122, 0.2);" v-if="isShow('dzzp')" @click="$u.route({url:'pages/home/<USER>/dzzp/list/list'})">
					<u-image src="@/static/img/dzzp.png" width="690rpx" height="120rpx"></u-image>
				</view> -->
			</view>
			<!-- 第五部分 -->
			<view class="container-item u-p-t-30" v-if="isShow('rcxcjg')">
				<text class="title">督查监管</text>
				<view class="item-list">
					<view class="f-item item-wrap" v-for="(item, index) in sItemList" :key="index2" @click="handleOpen(item.url)">
						<view class="f-item-img">
							<u-badge v-if="item.key === 'swyt'" type="error" :offset="[-4,-4]" :count="msgCount.four || 0"></u-badge>
							<u-image class="img" :src="item.src" width="96rpx" height="68rpx"></u-image>
						</view>
						<text class="name">{{ item.name }}</text>
					</view>
				</view>
			</view>
			<!-- 第六部分 柔性执法 -->
			<view class="clock" style="margin-top: 30rpx;"  v-if="isShow('rxzf')" @click="$u.route({ url: 'pages/home/<USER>/rxzf/list/list' })">
				<u-image src="@/static/img/rxzf.png" style="margin: 0 auto;"  width="690rpx" height="140rpx"></u-image>
			</view>
			<!-- 第七部分 -->
			<view class="container-item u-p-t-30" v-if="isShow('hngl')">
				<text class="title">黄牛管理</text>
				<view class="item-list">
					<view class="lh-item" :style="{ backgroundColor: item.color }" v-for="(item, index) in hnItemList" :key="index2" @click="handleOpen(item.url)">
						<u-badge v-if="item.key === 'swyt'" type="error" :offset="[-4,-4]" :count="msgCount.four || 0"></u-badge>
						<u-image class="img" :src="item.src" width="76rpx" height="76rpx"></u-image>
						<view class="text-content">
							<text class="name">{{ item.name }}</text>
							<text class="btn">进入操作></text>
						</view>
					</view>
				</view>
			</view>
			<!-- 第八部分 -->
			<view class="container-item u-p-t-30" v-if="isShow('lhzf')">
				<text class="title">联合执法</text>
				<view class="item-list">
					<view class="lh-item" :style="{ background: `url(${item.src}) no-repeat` }" v-for="(item, index) in thItemList" :key="index2" @click="handleOpen(item.url)">
						<!-- <u-image class="img" :src="item.src" width="60rpx" height="60rpx"></u-image> -->
						<view class="text-content">
							<text class="aName">{{ item.name }}</text>
							<text class="eName">{{ item.eName }}</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 第九部分 -->
			<view class="container-item u-p-t-20" v-if="isShow('ldzd')">
				<text class="title">领导终端</text>
				<view class="item-list">
					<view class="f-item" v-for="(item, index) in fItemList" :key="index2" @click="handleOpen(item.url)">
						<u-image class="img" :src="item.src" width="78rpx" height="78rpx"></u-image>
						<text class="name">{{ item.name }}</text>
					</view>
				</view>
			</view>
			<!-- 第六部分 -->
			<!-- <view class="container-item u-p-t-10">
				<text class="title">教科书式执法</text>
				<view class="item-list u-p-l-6 u-p-r-6">
					<view class="u-m-r-30">
						<u-image src="@/static/img/zsk.png" width="250rpx" height="295rpx"></u-image>
					</view>
					<view>
						<u-image src="@/static/img/dxal.png" width="410rpx" height="132rpx"></u-image>
						<u-image src="@/static/img/xxcz.png" width="410rpx" height="132rpx" style="margin-top: 31rpx;"></u-image>
					</view>
				</view>
			</view> -->
		</view>
	</view>
</template>

<script>
	// import { connectSocket } from '@/common/websocket.js'

	export default {
		data() {
			return {
				itemList: [
					{ key: 'xcfxrcxc', src: '../../../static/img/xcfxrcxc_icon.png', name: '日常巡查',  url:'pages/home/<USER>/xcfxrcxc/list/list' },
					{ key: 'xcfx', src: '../../../static/img/xcfx_icon.png', name: '店铺巡查', url:'pages/home/<USER>/xcfx/list/list' },
					{ key: 'jycx', src: '../../../static/img/jycx_icon.png', name: '简易案件', url:'pages/home/<USER>/jycx/list/list' },
					{ key: 'jkzp', src: '../../../static/img/jkzp_icon.png', name: '监控抓拍', url:'pages/home/<USER>/jkzp/list/list'},
					{ key: 'znzp', src: '../../../static/img/znzp_icon.png', name: '智能抓拍', url:'pages/home/<USER>/znzp/list/list'},
					// { key: 'jkm', src: '../../../static/img/fkm_icon.png', name: '缴款码',  url:'pages/home/<USER>/jkm/jkm' },
					{ key: 'dpxx', src: '../../../static/img/dpxx_icon.png', name: '店铺信息',  url:'pages/home/<USER>/dpxx/list/list'},
					{ key: 'ybaj', src: '../../../static/img/ybaj_icon.png', name: '一般案件',  url:'pages/home/<USER>/ybaj/list/list'},
					{ key: 'xkzl', src: '../../../static/img/xkzl_icon.png', name: '许可总览',  url:'pages/home/<USER>/xkzl/list/list'},
					// { key: 'sgcl', src: '../../../static/img/sgcl_icon.png', name: '宿管处理'},
					{ key: 'rxdwt', src: '../../../static/img/rxdwt_icon.png', name: '人行道违停',  url:'pages/home/<USER>/rxdwt/list/list'},
					{ key: 'wtcp', src: '../../../static/img/wtcp_icon.png', name: '违停抄牌',  url:'pages/home/<USER>/wtcp/list/list'},
				],
				attendanceItemList: [
					{ key: 'kqdk', src: '../../../static/img/kqdk_icon.png', name: '考勤打卡'},
					{ key: 'leave', src: '../../../static/img/leave_icon.png', name: '我的请假', url:'pages/home/<USER>/leave/list/list'},
					{ key: 'approve', src: '../../../static/img/approve_icon.png', name: '请假审批', url:'pages/home/<USER>/approve/list/list'},
					{ key: 'zbdwb', src: '../../../static/img/zbdwb_icon.png', name: '值班点位表', url:'pages/home/<USER>/zbdwb/list/list'},
					{ key: 'dbsq', src: '../../../static/img/dbsq_icon.png', name: '代班申请', url:'pages/home/<USER>/dbsq/list/list'},
				],
				secItemList: [
					{ key: 'rcxc', src: '../../../static/img/rcxn_icon.png', name: '日常巡查', color: '#FFF4E2', url: 'pages/home/<USER>/rcxc/list/list' },
					{ key: 'wkcz', src: '../../../static/img/wgcz_icon.png', name: '违规处置', color: '#FFEDEB', url: 'pages/home/<USER>/wgcz/list/list' },
					{ key: 'czcxx', src: '../../../static/img/cccjg_icon.png', name: '出租车监管', color: '#E5F5FE', url: 'pages/home/<USER>/taix/list/list' },
				],
				jjzfItemList: [
					{ key: 'dzzp', src: '../../../static/img/dzzp_icon.png', name: '电子抓拍', url: 'pages/home/<USER>/dzzp/list/list' },
					{ key: 'jjzfrcxc', src: '../../../static/img/jjzfrcxc_icon.png', name: '日常巡查'},
				],
				sItemList: [
					{ key: 'dckh', src: '../../../static/img/dckh_icon.png', name: '监督考核' , url: 'pages/home/<USER>/dckh/list/list' },
					{ key: 'gzdt', src: '../../../static/img/gzdt_icon.png', name: '工作动态',  url:'pages/home/<USER>/gzdt/list/list'},
					{ key: 'swyt', src: '../../../static/img/swyt_icon.png', name: '四位一体', url: 'pages/home/<USER>/swyt/list/list' },
					{ key: 'ajfb', src: '../../../static/img/ajfb_icon.png', name: '案卷分布' , url: 'pages/home/<USER>/ajfb/ajfb/ajfb' },
					{ key: 'dbsp', src: '../../../static/img/dbsp_icon.png', name: '代班审批' , url: 'pages/home/<USER>/dbsp/list/list' },
				],
				hnItemList: [
					{ key: 'hnjg', src: '../../../static/img/hnjg_icon.png', name: '黄牛信息' , color: '#FFF2F1', url: 'pages/home/<USER>/hnxx/list/list' },
					{ key: 'hncz', src: '../../../static/img/hncz_icon.png', name: '黄牛处置' ,  color: '#E5F5FE',url: 'pages/home/<USER>/hncz/list/list' }
				],
				thItemList: [
					{ key: 'rwfb', src: '../../../static/img/rwfb.png', name: '任务发布', eName:'Task release',  url: 'pages/home/<USER>/rwfb/list/list'  },
					{ key: 'zfdc', src: '../../../static/img/zfdc.png', name: '执法督查', eName:'law enforcement',  url: 'pages/home/<USER>/zfdc/list/list'},
				],
				fItemList: [
					{ key: 'rydw', src: '../../../static/img/rydw_icon.png', name: '人员定位' , url: 'pages/home/<USER>/rydw/rydw/rydw'},
					{ key: 'ldjb', src: '../../../static/img/ldjb_icon.png', name: '领导交办', url: 'pages/home/<USER>/ldjb/list/list' },
					{ key: 'ldsjc', src: '../../../static/img/ldsjc_icon.png', name: '领导数据舱' , url: 'pages/home/<USER>/ldsjc/list/list'},
					{ key: 'lddb', src: '../../../static/img/lddb_icon.png', name: '领导督办' , url: 'pages/home/<USER>/lddb/list/list'},
					{ key: 'ajcx', src: '../../../static/img/ajcx_icon.png', name: '案卷查询' , url: 'pages/home/<USER>/ajcx/list/list' },
				],

				userType: null,
				appList: [],
				msgCount: {},
				notice: {},
				noticeList: [],

			};
		},
		watch: {
			vuex_msg_count: {
				deep: true,
				handler(nVal) {
					this.msgCount = nVal
				}
			}
		},
		methods: {
			handleOpen(url) {
				if (url) {
					this.$u.route({ url })
				}
			},
			handleOpenNotice() {
				const timestamp = new Date().getTime()
				const readTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.$loading()
				this.$u.api.editNoticeDetail({ noticeDetailId: this.notice.noticeDetailId, isRead: 1, readTime }).then(() => {
					uni.hideLoading()
					this.$u.route({ url: 'pages/my/notice/detail/detail', params: { id: this.notice.noticeId } })
					this.notice = {}
					this.noticeList = []
				}).catch(() => {
					uni.hideLoading()
				})
			},
			// 考勤
			handleClickOpen(item){
				if(item.url){
					this.$u.route({ url:item.url })
				}else{
					if (this.userType == '0') {
						this.$u.route({ url: 'pages/home/<USER>/in/in', params: { userType: this.userType } })
					} else {
						this.$u.route({ url: 'pages/home/<USER>/out/out', params: { userType: this.userType } })
					}
				}
			},
			isShow(key) {
				return this.appList.includes(key)
			},
			getNotice() {
				this.$u.api.getNoticeDetailList({ userId: this.vuex_id, isRead: 0 }).then(noticeList => {
					/* 通知公告 */
					if (noticeList.rows && noticeList.rows[0]) {
						this.notice = noticeList.rows[0]
						this.noticeList = [noticeList.rows[0].noticeTitle]
					} else {
						this.notice = {}
						this.noticeList = []
					}
				})
			},
			setMarkCount() {
				uni.$on('changeMark', () => {
					setTimeout(() => {
						this.$u.api.getlistMark().then(resMsgCount => {
							this.msgCount = resMsgCount.data
							this.$u.vuex('vuex_msg_count', resMsgCount.data)
							/* if (resMsgCount.data.all) {
								uni.setTabBarBadge({ index: 0, text: `${resMsgCount.data.all}` })
							} else {
								uni.removeTabBarBadge({ index: 0 })
							} */
							if (resMsgCount.data.message) {
								uni.setTabBarBadge({ index: 2, text: `${resMsgCount.data.message}` })
							} else {
								uni.removeTabBarBadge({ index: 2 })
							}
						})
					}, 1500)
				})
			},
			fetchData() {
				Promise.all([
					this.$u.api.getInfo(),
					this.$u.api.getAPPList(),
					this.$u.api.getlistMark()
				]).then(resAry => {
					uni.stopPullDownRefresh()
					const [resUser, resAppList, resMsgCount] = resAry
					// 用户信息
					this.userType = resUser.user.type
					this.$u.vuex('vuex_id', resUser.user.userId)
					this.$u.vuex('vuex_postIds', resUser.user.postIds)
					this.$u.vuex('vuex_nickName', resUser.user.nickName)
					this.$u.vuex('vuex_phone', resUser.user.phonenumber)
					this.$u.vuex('vuex_postKeys', resUser.user.postKeys || '')
					this.$u.vuex('vuex_type', resUser.user.type || '')
					if (resUser.user.dept) {
						this.$u.vuex('vuex_deptId', resUser.user.dept.deptId)
						this.$u.vuex('vuex_deptName', resUser.user.dept.deptName)
					}
					/* 登录成功进入首页调用websocket */
					// connectSocket({ uId: `${resUser.user.userId}-app` })
					// appList
					this.appList = resAppList.data
					this.$u.vuex('vuex_appList', resAppList.data)
					this.itemList = this.itemList.filter(item => this.appList.includes(item.key))
					this.attendanceItemList = this.attendanceItemList .filter(item => this.appList.includes(item.key))
					this.secItemList = this.secItemList .filter(item => this.appList.includes(item.key))
					this.jjzfItemList = this.jjzfItemList .filter(item => this.appList.includes(item.key))
					this.sItemList = this.sItemList .filter(item => this.appList.includes(item.key))
					this.hnItemList = this.hnItemList .filter(item => this.appList.includes(item.key))
					this.thItemList = this.thItemList .filter(item => this.appList.includes(item.key))
					this.fItemList = this.fItemList .filter(item => this.appList.includes(item.key))

					// this.itemList = this.itemList.filter(item => this.appList.includes(item.key))
					// this.secItemList = this.secItemList.filter(item => this.appList.includes(item.key))
					// this.thItemList = this.thItemList.filter(item => this.appList.includes(item.key))
					// this.fItemList = this.fItemList.filter(item => this.appList.includes(item.key))
					// this.sItemList = this.sItemList.filter(item => this.appList.includes(item.key))
					// 消息
					this.msgCount = resMsgCount.data
					this.$u.vuex('vuex_msg_count', resMsgCount.data)
					/* if (resMsgCount.data.all) {
						uni.setTabBarBadge({ index: 0, text: `${resMsgCount.data.all}` })
					} else {
						uni.removeTabBarBadge({ index: 0 })
					} */
					if (resMsgCount.data.message) {
						uni.setTabBarBadge({ index: 2, text: `${resMsgCount.data.message}` })
					} else {
						uni.removeTabBarBadge({ index: 2 })
					}
					/* 获取通知公告 */
					this.getNotice()
				}).catch(() => {
					uni.stopPullDownRefresh()
				})
			},
			noticeMsg() {
				uni.$on('noticeMsg', data => {
					this.notice = data
					this.noticeList = [data.messageContent]
				})
			}
		},
		async onLoad() {
			await this.fetchData()
			this.setMarkCount()
			this.noticeMsg()
		},
		onPullDownRefresh() {
			this.fetchData()
		}
	}
</script>

<style lang="scss">
.banner {
	position: absolute;
	.title {
		font-size: 40rpx;
		color: #fff;
		position: absolute;
		top: calc(23rpx + var(--status-bar-height));
		left: 30rpx;
		z-index: 10;
		font-weight: 700;
	}
}
.name {
	font-size: 26rpx;
	font-weight: 400;
}
.container {
	background-color: #fff;
	// margin-top: -132rpx;
	margin-top: calc(118rpx + var(--status-bar-height));
	position: relative;
	z-index: 15;
	border-radius: 36rpx 36rpx 0px 0px;
	padding-bottom: 30rpx;
	overflow: hidden;
	.container-item {
		padding: 0 30rpx;
		.title {
			height: 80rpx;
			font-size: 36rpx;
			color: #333333;
			font-weight: 700;
			display: flex;
			align-items: center;
			margin-bottom: 10rpx;
		}
		.item-list {
			display: flex;
			flex-wrap: wrap;
			margin: 0 -6rpx;
			.item {
				flex: 0 1 25%;
				&-inner {
					display: flex;
					flex-direction: column;
					align-items: center;
					padding: 20rpx 0;
					margin: 0 6rpx 14rpx;
					border-radius: 10rpx;
					transition: opacity .3s;
					position: relative;
					&:active {
						opacity: 0.3;
					}
					.name {
						color: #333;
						margin-top: 20rpx;
					}
				}
			}
			// 运管执法
			.ygzfItem{
				flex: 0 1 33.33%;
				&-inner {
					width: 218rpx;
					height: 140rpx;
					padding: 32rpx 0 20rpx 20rpx;
					margin: 0 6rpx 14rpx;
					border-radius: 10rpx;
					transition: opacity .3s;
					position: relative;
					font-size: 26rpx;
					&:active {
						opacity: 0.3;
					}
					.name {
						color: #333;
						margin-top: 10rpx;
					}
				}
			}
			// 交警执法
			.jjzfItem{
				flex: 0 1 50%;
				&-inner {
					display: flex;
					width: 168px;
					height: 70px;
					background: #FFFFFF;
					box-shadow: 0px 0px 4px 0px rgba(30,68,130,0.16);
					border-radius: 7px;
					padding: 32rpx 0 32rpx 34rpx;
					font-size: 26rpx;
					color: #333;
					transition: opacity .3s;
					position: relative;
					&:active {
						opacity: 0.3;
					}
					.jjzfItem-right{
						display: flex;
						flex-direction: column;
						margin-left: 14rpx;
						.name {
							color: #333;
							margin-top: 10rpx;
						}
						.enter{
							font-size: 11px;
							color: #888;
						}
					}
				}
			}
			.lh-item {
				flex: 1;
				border-radius: 10rpx;
				padding: 37rpx 40rpx 38rpx;
				margin: 0 6rpx 14rpx;
				display: flex;
				transition: opacity .3s;
				&:active {
					opacity: 0.3;
				}
				.img {
					flex-shrink: 0;
					margin-right: 20rpx;
				}
				.text-content {
					display: flex;
					flex-direction: column;
					.btn {
						font-size: 22rpx;
						color: #888888;
					}
					.aName{
						font-size: 32rpx;
						font-weight: 500;
						color: #fff;
					}
					.eName{
						margin-top: 8rpx;
						color: #fff;
						opacity: 0.5;
						font-size: 20rpx;
					}
				}
			}
			.f-item {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;
				margin: 0 5rpx;
				padding: 35rpx 0 40rpx;
				transition: opacity .3s;
				&:active {
					opacity: 0.3;
				}
				.f-item-img {
					position: relative;
					width: 78rpx;
					height: 78rpx;
				}
				.img {
					margin-bottom: 12rpx;
					flex-shrink: 0;
				}
			}
			.item-wrap{
				flex:0 1 23%;
			}
		}
	}
}
</style>

<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="150" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="案件编号:" prop="ajbh" >
					<u-input v-model="form.ajbh" disabled type="text" placeholder="案件编号" />
				</u-form-item>
				<u-form-item label="抄告单号:" prop="billno" >
					<u-input v-model="form.billno" disabled type="text" placeholder="抄告单号" />
				</u-form-item>
				<u-form-item label="采集部门:" prop="cjbmmc" >
					<u-input v-model="form.cjbmmc" disabled type="text" placeholder="采集部门名称" />
				</u-form-item>
				<u-form-item label="采集人员:" prop="cjmj" >
					<u-input v-model="form.cjmj" disabled type="text" placeholder="采集人员" />
				</u-form-item>
				<u-form-item label="处理时间:" prop="clsj" >
					<u-input v-model="form.clsj" disabled type="text" placeholder="处理时间" />
				</u-form-item>
				<u-form-item label="处理标记:" prop="clbjName" >
					<u-input v-model="form.clbjName" disabled type="text" placeholder="处理标记" />
				</u-form-item>
			</view>

			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="当事人:" prop="dsr">
					<u-input v-model="form.dsr" disabled type="text" placeholder="当事人姓名" />
				</u-form-item>
				<u-form-item label="号牌号码:" prop="hphm">
					<u-input v-model="form.hphm" disabled type="text" placeholder="号牌号码" />
				</u-form-item>
				<u-form-item label="违法时间:" prop="wfsj">
					<u-input v-model="form.wfsj" disabled type="text" placeholder="违法时间" />
				</u-form-item>
				<u-form-item label="违法地址:" prop="wfdz">
					<u-input v-model="form.wfdz" type="text" disabled placeholder="违法地址" />
				</u-form-item>
				<u-form-item label="违法行为:" prop="wfxw">
					<u-input v-model="form.wfxw" type="popup" disabled placeholder="违法行为" @click="showHappenTime = true"  />
				</u-form-item>
				<u-form-item label="罚款金额:" prop="fkje" >
					<u-input v-model="form.fkje" type="text" disabled placeholder="罚款金额" />
				</u-form-item>
				<u-form-item label="缴款日期:" prop="jkrq">
					<u-input v-model="form.jkrq" disabled placeholder="缴款日期"   />
				</u-form-item>
				<u-form-item label="缴款标记:" prop="jkbjName">
					<u-input v-model="form.jkbjName" disabled placeholder="缴款标记" />
				</u-form-item>
				<u-form-item label="是否结案:" prop="sfjaName">
					<u-input v-model="form.sfjaName" disabled placeholder="是否结案" />
				</u-form-item>
				<u-upload
					max-count="4"
					width="157"
					height="157"
					:auto-upload="false"
				  :size-type="['compressed']"
					:file-list="fileList"
					:deletable="false"
					:customBtn="true"
					name="files"
				></u-upload>
			</view>


		</u-form>
		<!-- 提交按钮 -->
		<!-- <view class="btn-box u-border-top u-flex">
			<u-button class="u-flex-1 u-m-r-20" type="primary" shape="circle" :loading="aloading" :custom-style="subStyle" @click="handleSubmit(1)">暂存</u-button>
			<u-button class="u-flex-1" type="primary" shape="circle" :loading="aloading" :custom-style="subStyle" @click="handleOver">提交</u-button>
		</view> -->
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import gps from '@/common/gps.js'
	export default {
	 filters: {
			statusName(status) {
				const statusObj = { 1: '进行中', 9: '已完结'}
				if (statusObj) return statusObj[status]
			},
			// 处理标记
			clbjName(status) {
				const statusObj = { 0: '未处理', 1: '已处理'}
				if (statusObj) return statusObj[status]
			},
			// 缴款标记
			jkbjName(status) {
				const statusObj = { 0: '未缴款', 1: '已缴款'}
				if (statusObj) return statusObj[status]
			},
			// 是否结案
			sfjaName(status) {
				const statusObj = { 0: '未结案', 1: '已结案'}
				if (statusObj) return statusObj[status]
			}
		},
		data() {
			return {
				showCaseTypeList:false,
				showHandleTypeList:false,
				aloading: false,
				fileList: [],
				// 存储班长/责任人
				role:'',
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				form: {},
				happenData: {
					tableName: 'case_sidewalk',
					status: 1
				},
				rules: {

				},
				showHappenTime: false
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {

		},
		async onLoad(params) {
		  this.$loading()
			const [standardRes, fileRes] = await Promise.all([
				this.$u.api.getSidewalk({}, params.id),
				this.$u.api.getFileList({ tableName: 'case_sidewalk', businessId: params.id })
			])
			/* 数据请求完毕 */
			uni.hideLoading()
			if (standardRes.code == 200 && fileRes.code == 200) {
				/* 表单数据 */
				this.form = standardRes.data

				// 处理标记
				const clbjObj = { 0: '未处理', 1: '已处理'}
				// 缴款标记
				const jkbjObj = { 0: '未缴款', 1: '已缴款'}
				// 是否结案
				const sfjaObj = { 0: '未结案', 1: '已结案'}

				this.form = { ...this.form, clbjName: clbjObj[this.form.clbj], jkbjName: jkbjObj[this.form.jkbj], sfjaName: sfjaObj[this.form.sfja]}


				/* 文件数据 */
				this.fileList = fileRes.rows.map(item => {
          return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
				})
			}

		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.pos-r {
	height: 50rpx;
	line-height: 50rpx;
	font-size: 24rpx;
	background-color: #2979ff;
	border-radius: 10rpx;
	position: absolute;
	top: 30rpx;
	right: 0;
	color: #FFFFFF;
	padding: 0 15rpx;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}

// 试卷二维码
.exam_content{
	background: #fff;
	margin: 0 15rpx;
	border-radius: 10rpx;
	padding: 20rpx;
	.exam_info{
		display: flex;
		flex-direction: column;
		align-items: center;
		background: #F1F7FF;
		border-radius: 10rpx;
		padding: 30rpx;
		.exam_to_btn{
			display: flex;
			align-items: center;
			justify-content: center;
			color: #fff;
			font-size: 32rpx;
			font-weight: 500;
			width: 650rpx;
			height: 70rpx;
			border-radius: 10rpx;
			background: #4689F5;
			margin-bottom: 20rpx;
			// overflow: hidden;
		}
		.exam_name{
			font-size: 28rpx;
			margin-top: 15rpx;
			color: #808080;
		}
	}
}
</style>

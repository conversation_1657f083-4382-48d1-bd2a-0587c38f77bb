<template>
	<view>
		<top-supervise :caseId="form.transportId" caseType="transport" :status="form.status"/>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="当事人:" prop="party" required>
					<u-input v-model="form.party" type="text" placeholder="请输入当事人姓名" />
				</u-form-item>
				<!-- <u-form-item label="身份证:" prop="identityCard">
					<u-input v-model="form.identityCard" type="idcard" placeholder="请输入当事人身份证号"  />
				</u-form-item> -->
				<u-form-item label="联系电话:" prop="phone">
					<u-input v-model="form.phone" placeholder="请输入当事人联系电话" />
				</u-form-item>
				<u-form-item label="车辆车牌:" prop="carNo" required>
					<u-input v-model="form.carNo" type="text" placeholder="请输入车辆车牌" />
				</u-form-item>
				<u-form-item label="车辆品牌:" prop="models">
					<u-input v-model="form.models" type="text" placeholder="请输入车辆品牌" />
				</u-form-item>
				<u-form-item label="营运公司:" prop="operationCompany">
					<u-input v-model="form.operationCompany" type="text" placeholder="请输入营运公司" />
				</u-form-item>
				<u-form-item label="从业资格证号:" prop="certificateNo">
					<u-input v-model="form.certificateNo" type="text"  placeholder="请输入从业资格证号" />
				</u-form-item>
				<u-form-item label="道路运输证号:" :border-bottom="false" prop="transportCertificate">
					<u-input v-model="form.transportCertificate" type="text"  placeholder="请输入道路运输证号" />
				</u-form-item>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<!-- <u-form-item label="标题:" prop="title" required>
					<u-input v-model="form.title" placeholder="请输入标题" />
				</u-form-item> -->
				<u-form-item label="上报人员:" prop="userName" required>
					<u-input v-model="form.userName" type="text" placeholder="上报人员" />
				</u-form-item>
				<u-form-item label="辅助人员:" prop="userNames" >
					<u-input v-model="form.userNames" type="popup" placeholder="辅助人员"/>
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('userIds')">选择人员</u-button>
				</u-form-item>
				<u-form-item label="上报时间:" prop="inspectionTime" required>
					<u-input v-model="form.inspectionTime" type="text" placeholder="请选择上报时间" @click="showInspectionTime = true"/>
					<u-picker v-model="showInspectionTime" mode="time" :params="params" :default-time="form.inspectionTime" @confirm="handleItDateCon"></u-picker>
				</u-form-item>
				<u-form-item label="车辆类型:" prop="carTypeName" required>
					<u-input v-model="form.carTypeName" disabled placeholder="请选择车辆类型" /><!--type="select" :select-open="showCarType" @click="showCarType = true" -->
					<!-- <u-action-sheet v-model="showCarType" :list="transportCarType" @click="handleTypeClick"></u-action-sheet> -->
				</u-form-item>
				<u-form-item label="违规类型:" prop="inspectionTypeName" required>
					<span v-if="!inspectionTypeList.length" style="font-size: 30rpx; color: #c0c4cc">请选择违规类型</span>
					<u-checkbox-group wrap @change="handleInspectionTypeClick">
						<u-checkbox
							v-model="item.checked"
							v-for="(item, index) in inspectionTypeList" :key="index"
							:name="item.dictValue"
						>{{item.dictLabel}}</u-checkbox>
					</u-checkbox-group>
				</u-form-item>
				<!-- <u-form-item label="违规类型:" prop="inspectionTypeName" required>
					<u-input v-model="form.inspectionTypeName" type="select" :select-open="showInspectionType" placeholder="请选择违规类型" @click="handleOpenIsType" />
					<u-action-sheet v-model="showInspectionType" :list="inspectionTypeList" @click="handleInspectionTypeClick"></u-action-sheet>
				</u-form-item> -->
				<!-- <u-form-item v-if="isShowFTaxiType" label="网络预约出租汽车驾驶员证:" prop="onlineCarDriver" required>
					<u-radio-group v-model="form.onlineCarDriver">
						<u-radio name="1">有</u-radio>
						<u-radio name="0">无</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item v-if="isShowSTaxiType" label="网络预约出租汽车运输证类型:" prop="onlineCarTarnsport" required>
					<u-radio-group v-model="form.onlineCarTarnsport">
						<u-radio name="1">有</u-radio>
						<u-radio name="0">无</u-radio>
					</u-radio-group>
				</u-form-item> -->
				<u-form-item label="发生地址:" prop="address" required>
					<u-input v-model="form.address" type="text" placeholder="请选择地址" />
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseLocation">选择地址</u-button>
				</u-form-item>
				<u-form-item label="经纬度:" :border-bottom="false" prop="lnglat" required>
					<u-input v-model="form.lnglat" type="popup" disabled  placeholder="请选择地址" />
				</u-form-item>
			</view>

			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="违规内容:" prop="content" label-position="top" :border-bottom="false">
					<u-input v-model="form.content" type="textarea" maxlength="300" height="140" placeholder="请输入违规内容..." />
				</u-form-item>
				<u-upload
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
				  :size-type="['compressed']"
					:file-list="happenFile"
					:before-remove="handleRemove"
					@on-uploaded="handleAllUpload"
					@on-error="handleError"
				></u-upload>
			</view>
			<!-- 违规文件部分 -->
			<view v-for="(item,idx) in exFile" :key="idx">
				<u-gap height="20" bg-color="#F5F5F5"></u-gap>
				<view class="p-lr-30">
					<u-form-item :label="item.label" prop="content" label-position="top" :border-bottom="false">
						<u-upload
							:ref="`file${item.data.status}`"
							name="files"
							width="157"
							height="157"
							:header="header"
							:auto-upload="false"
							:action="action"
							:form-data="item.data"
						  :size-type="['compressed']"
							:file-list="item.fileList"
							:before-remove="handleRemove"
							@on-uploaded="handleAllUpload"
							@on-error="handleError"
						></u-upload>
					</u-form-item>
				</view>
			</view>
		</u-form>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex">
			<u-button class="u-flex-1 u-m-r-20" type="primary" shape="circle" :custom-style="subStyle" @click="handleEdit(2)">暂存</u-button>
			<u-button class="u-flex-1" type="primary" shape="circle" :custom-style="subStyle" @click="handleOver">办结</u-button>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import topSupervise from '@/components/top-supervise.vue'
	import gps from '@/common/gps.js'
	export default {
		components: {
			topSupervise
		},
		data() {
			return {
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				showCarType: false,
				transportCarType: [],
				showInspectionType: false,
				inspectionTypeList: [],
				showInspectionTime: false,
				isShowFTaxiType: false,
				isShowSTaxiType: false,
				form: {address:""},
				rules: {
					// title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
					party: [{ required: true, message: '请输入当事人姓名', trigger: 'blur' }],
					// identityCard: [
					// 	{ required: true, message: '请输入当事人身份证号', trigger: 'blur' },
					// 	{
					// 		validator: (rule, value, callback) => {
					// 			return this.$u.test.idCard(value);
					// 		},
					// 		message: '身份证号码不正确',
					// 		trigger: ['change','blur'],
					// 	}
					// ],
					// phone: [
					// 	{ required: true, message: '请输入当事人联系电话', trigger: 'blur' },
					// 	{
					// 		validator: (rule, value, callback) => {
					// 			return this.$u.test.mobile(value);
					// 		},
					// 		message: '手机号码不正确',
					// 		trigger: ['change','blur'],
					// 	}
					// ],
					carNo: [{ required: true, message: '请输入车辆车牌', trigger: 'blur' }],
					userName: [{ required: true, message: '违规人员', trigger: 'blur' }],
					inspectionTime: [{ required: true, message: '请选择违规时间', trigger: 'change' }],
					carTypeName: [{ required: true, message: '请选择车辆类型', trigger: 'change' }],
					inspectionTypeName: [{ required: true, message: '请选择违规类型', trigger: 'change' }],
					address: [{ required: true, message: '请选择地址', trigger: 'change' }],
					lnglat: [{ required: true, message: '请选择地址', trigger: 'change' }],
				},
				happenData: {
					tableName: 'case_transport',
					status: 1
				},
				happenFile: [],
				exFile: []
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			handleChooseHandleUser(type) {
				let params = {}
				if (this.form[type]) {
					params.defaultCheckedKeys = this.form[type]
					params.defaultExpandedKeys = this.form[type]
				}
				params.showRadio = 0
				params.type = type
				this.$u.route({ url: 'pages/common/selectUser/selectUser', params })
			},
			setUserData(checks,type,parent) {
				// 选择好人员后的回调
				let checkData = {}
				if(checks.length == 1){
					checkData = checks[0]
				}else{
					checks.map((v,i)=>{
						if(i==0) {
							checkData.label = v.label+''
							checkData.id = v.id+''
						}else{
							checkData.label += ',' + v.label+''
							checkData.id += ',' + v.id+''
						}
					})
				}
				if(type == 'userIds'){
					const deptId = parent.data.id, deptName = parent.data.label
					if (checkData) this.form = { ...this.form, userNames: checkData.label, userIds: checkData.id, deptId, deptName }
				}
			},
			async handleTypeClick(idx) {
				const { dictValue, text, remark } = this.transportCarType[idx]
				this.form = { ...this.form, carType: dictValue, carTypeName: text }
				if (dictValue != 4) {
					this.isShowFTaxiType = false
					this.isShowSTaxiType = false
				}
				this.$loading()
				await this.$u.api.getDicts({}, remark).then(res => {
					this.inspectionTypeList = res.data
					this.form = { ...this.form, inspectionType: '', inspectionTypeName: '' }
					uni.hideLoading()
				}).catch(() => {
					uni.hideLoading()
				})
			},
			handleOpenIsType() {
				if (!this.form.carTypeName) {
					this.mToase('请先选择车辆类型')
					return
				}
				this.showInspectionType = true
			},
			handleInspectionTypeClick(selectValue) {
				let inspectionTypeNameAry = this.inspectionTypeList.filter(item => selectValue.includes(item.dictValue)).map(item => item.dictLabel)
				this.form = { ...this.form, inspectionType: selectValue.join(','), inspectionTypeName: inspectionTypeNameAry.join(',') }
			},
			handleChooseLocation() {
				uni.chooseLocation({
					latitude: 29.110764,
					longitude: 119.635857,
					success: res => {
						const { address, longitude, latitude } = res
						const lnglat = gps.gcj_decrypt(parseFloat(latitude), parseFloat(longitude))
						this.form = { ...this.form, address: "浙江省金华市婺城区城北街道" + address, longitude: lnglat.lng, latitude: lnglat.lat, lnglat: `${lnglat.lng}, ${lnglat.lat}` }
					},
					fail: () => {
						uni.showToast({
							title: '地图打开失败',
							icon: 'none',
							position: 'bottom'
						})
					}
				})
			},
			handleItDateCon(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, happenDate: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleRemove(index, lists) {
				const fileInfo = lists[index]
				const fileId = fileInfo.url.split('?id=')[1]
				if (fileId) {
						return new Promise((resolve, reject) => {
							uni.showModal({
								title: '提示',
								content: '删除后将无法恢复，是否确认删除？',
								success: ({ confirm }) => {
									if (confirm) {
										this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
									} else {
										reject()
									}
								}
							})
						})
				} else {
					return true
				}
			},
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				const isPass = this.allFileUpload(this.form.transportId)
				if (!isFail && isPass) {
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					this.$implement()
				}
			},
			handleError() {
				// 文件上传失败，弹出提示是否重新上传
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					content: '图片上传失败，是否重新上传？',
					success: ({ confirm }) => {
						if (confirm) {
							this.allFileUpload(this.form.transportId)
						} else {
							this.$implement({ immediately: true })
						}
					}
				})
			},
			handleOver() {
				uni.showModal({
					title: '提示',
					content: '是否确认办结？',
					success: ({ confirm }) => {
						if (confirm) {
							this.handleEdit(9)
						}
					}
				})
			},
			allFileUpload(businessId) {
				// 先检测违规图片，没有需要上传文件后再检测其余类型文件
				let isPass = false // 为true的话则代表没有需要上传的文件了
				const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
				if (uploadFile) {
					this.happenData.businessId = businessId
					this.$loading('违规图片上传中')
					this.$refs.happenfile.upload()
				} else {
					// 循环所有额外文件上传对象，检测是否还有文件没有上传
					for(let i=0; i<this.exFile.length; i++) {
						let exfile = this.exFile[i]
						const exUploadFile = this.$refs[`file${exfile.data.status}`][0].lists.some(item => item.progress !== 100)
						if (exUploadFile) {
							// 存在为上传文件
							this.$loading(`${exfile.label}上传中`)
							this.exFile[i].data.businessId = businessId
							this.$refs[`file${exfile.data.status}`][0].upload()
							break
						} else if (i == this.exFile.length - 1) {
							isPass = true
						}
					}
				}
				return isPass
			},
			validateFn(isValid) {
				// 根据传入的状态来确定是否要进行验证，用以区别暂存和办结
				return new Promise((resolve, reject) => {
					if (isValid) {
						this.$refs.uForm.validate(valid => {
							if (valid) {
								/* // 图片验证，没有图片不通过验证
								if (!this.$refs.happenfile.lists.length) {
									this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
									reject()
								} else {
									for(let i=0; i<this.exFile.length; i++) {
										let exfile = this.exFile[i]
										if (!this.$refs[`file${exfile.data.status}`][0].lists.length) {
											this.$refs.uTips.show({ title: `请上传${this.exFile[i].label}`, type: 'error', duration: '2300' })
											reject()
											break
										} else if (i == this.exFile.length - 1) {
											resolve()
										}
									}
								} */
								resolve()
							} else {
								reject()
							}
						})
					} else {
						resolve()
					}
				})
			},
			handleEdit(status) {
				this.validateFn(status == 9).then(() => {
					const params = { ...this.form, status, type: 1 }
					// 开始上传
					this.$loading('数据上传中')
					this.$u.api.editTransport(params).then(res => {
						// 遍历列表，查询是否有未上传的图片
						const isPass = this.allFileUpload(this.form.transportId)
						if (isPass) {
							uni.showToast({title: '操作成功'})
							uni.hideLoading()
							this.$implement()
						}
					}).catch(() => {
						uni.hideLoading()
					})
				}).catch(() => {})
			},
			setFileType(type) {
				if (type == 4) {
					/* 网约车，修改上传文件类型 */
					this.exFile = [
						{ key: 2, label: '现场笔录', data: { tableName: 'case_transport', status: 2 }, fileList: [] },
						{ key: 3, label: '乘客询问笔录', data: { tableName: 'case_transport', status: 3 }, fileList: [] },
						{ key: 4, label: '驾驶员询问笔录', data: { tableName: 'case_transport', status: 4 }, fileList: [] },
						{ key: 5, label: '调查通知书', data: { tableName: 'case_transport', status: 5 }, fileList: [] },
						{ key: 8, label: '查封通知书', data: { tableName: 'case_transport', status: 8 }, fileList: [] },
						// { key: 7, label: '图片证据', data: { tableName: 'case_transport', status: 7 }, fileList: [] },
					]
				} else if (type == 5) {
					/* 黑车，修改上传文件类型 */
					this.exFile = [
						{ key: 2, label: '现场笔录', data: { tableName: 'case_transport', status: 2 }, fileList: [] },
						{ key: 3, label: '乘客询问笔录', data: { tableName: 'case_transport', status: 3 }, fileList: [] },
						{ key: 4, label: '驾驶员询问笔录', data: { tableName: 'case_transport', status: 4 }, fileList: [] },
						{ key: 5, label: '调查通知书', data: { tableName: 'case_transport', status: 5 }, fileList: [] },
						{ key: 9, label: '行政强制措施现场笔录', data: { tableName: 'case_transport', status: 9 }, fileList: [] },
						{ key: 10, label: '扣押决定书', data: { tableName: 'case_transport', status: 10 }, fileList: [] },
						// { key: 7, label: '图片证据', data: { tableName: 'case_transport', status: 7 }, fileList: [] },
					]
				} else {
					/* 其余为默认 */
					this.exFile = [
						{ key: 2, label: '现场笔录', data: { tableName: 'case_transport', status: 2 }, fileList: [] },
						{ key: 3, label: '乘客询问笔录', data: { tableName: 'case_transport', status: 3 }, fileList: [] },
						{ key: 4, label: '驾驶员询问笔录', data: { tableName: 'case_transport', status: 4 }, fileList: [] },
						{ key: 5, label: '调查通知书', data: { tableName: 'case_transport', status: 5 }, fileList: [] },
						// { key: 6, label: '先行登记保存证据通知书', data: { tableName: 'case_transport', status: 6 }, fileList: [] },
						// { key: 7, label: '图片证据', data: { tableName: 'case_transport', status: 7 }, fileList: [] },
					]
				}
			}
		},
		async onLoad(params) {
			// 获取车辆类型
			this.$loading('获取系统配置数据')
			await this.$u.api.getDicts({}, 'transport_car_type').then(res => {
				this.transportCarType = res.data.map(item => {
					item.text = item.dictLabel
					return item
				})
				uni.hideLoading()
			}).catch(() => {
				uni.hideLoading()
			})
			// 获取业务数据
			if (params.id) {
				this.$loading()
				Promise.all([
					this.$u.api.getTransport({}, params.id),
					this.$u.api.getFileList({ tableName: 'case_transport', businessId: params.id }),
					this.$u.api.getDicts({}, 'transport_car_type')
				]).then(resAry => {
					const formData = resAry[0].data
					const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
					this.form = { ...formData, lnglat }
					// 设置文件分类
					this.setFileType(this.form.carType)
					resAry[1].rows.map(item => {
						const obj = { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
						if (item.status == 1) {
							this.happenFile.push(obj)
						} else {
							this.exFile.forEach(file => {
								if (file.data.status == item.status) {
									file.fileList.push(obj)
								}
							})
						}
					})
					let remark = ''
					this.transportCarType = resAry[2].data.map(item => {
						item.text = item.dictLabel
						if (item.dictValue == resAry[0].data.carType) {
							remark = item.remark
						}
						return item
					})
					// 获取巡查类型
					if (remark) {
						this.$u.api.getDicts({}, remark).then(dictRes => {
							this.inspectionTypeList = dictRes.data.map(item => {
								item.checked = this.form.inspectionType.includes(item.dictValue)
								return item
							})
							uni.hideLoading()
						}).catch(() => {
							uni.hideLoading()
						})
					} else {
						uni.hideLoading()
					}
				}).catch(() => {
					uni.hideLoading()
				})
			} else {
				const timestamp = new Date().getTime()
				const inspectionTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = { inspectionTime, userName: this.vuex_nickName, userId: this.vuex_id }
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

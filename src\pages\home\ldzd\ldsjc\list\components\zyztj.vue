<template>
	<view class="containe">
		<view class="u-m-b-24">
			<view class="u-m-t-36 u-flex">
				<view style="width: 46px;">{{volData.sexCountData[0].name}}性</view>
				<u-line-progress active-color="#29E2FA" :show-percent = "false" :percent="volData.sexCountData[0].value"></u-line-progress>
				<view style="width: 46px;color: #4689F5;">{{volData.sexCountData[0].value}}%</view>
			</view>
			<view class="u-m-t-24  u-flex">
				<view style="width: 46px;">{{volData.sexCountData[1].name}}性</view>
				<u-line-progress active-color="#FF6189"  :show-percent = "false" :percent="volData.sexCountData[1].value"></u-line-progress>
				<view style="width: 46px;color: #FF6189;">{{volData.sexCountData[1].value}}%</view>
			</view>
		</view>
		<view class="">
			<ringUcharts :canvasId = "'zyzth'" :datas="volData.jobCountData"></ringUcharts>
		</view>
	</view>
</template>

<script>
	import ringUcharts from '@/components/ring-ucharts.vue'
	export default{
		name:'Zyzth',
		components:{ringUcharts},
		props:{
			volData:{
				type:Object
			}
		},
	}
</script>

<style lang="scss" scoped>
	.containe {
		width: 100%;
		height: 725rpx;
		background: rgba(255, 255, 255, 0.08);
		color: #fff;
		padding: 30rpx;
		font-size: 24rpx;
		border-radius: 14px;
		margin: 18rpx 0 20rpx;
	}
</style>

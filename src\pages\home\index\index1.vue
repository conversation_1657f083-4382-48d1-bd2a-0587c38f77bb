<template>
  <view style="overflow: hidden;display: flex;flex-direction: column;justify-content: flex-start;align-items: center  ">
    <view class="banner">
      <text class="title">首页</text>
      <u-image src="@/static/img/home_banner.png" width="750rpx" height="290rpx"></u-image>
    </view>
    <view class="container">
      <!-- 通知公告 -->
      <view  @click="handleOpenNotice">
        <u-notice-bar mode="horizontal" :more-icon="true" :list="noticeList"></u-notice-bar>
      </view>
      <!--驾驶舱-->
      <u-image class="container-jsc"
               src="../../../static/newImg/驾驶舱入口.png"
               @click="handleOpen('pages/home/<USER>/ldsjc/list/list')"
               width="690rpx"
               height="247.76rpx"></u-image>
      <!-- 无权限 -->
      <u-empty text="无权限" mode="permission" class="u-p-80" v-if="!this.appList.length"></u-empty>
      <!-- 应用列表 -->
      <view class="container-item u-p-t-20">
        <view class="item-list">
          <view class="item" v-for="(item, index) in appList" :key="index1" @click="handleOpen(item.url)">
            <view class="item-inner" :style="{ backgroundColor: item.color }">
              <!--四位一体徽标-->
              <u-badge v-if="item.key === 'swyt'" type="error" :offset="[-4,-4]" :count="msgCount.four || 0"></u-badge>
              <u-image  :src="item.src" width="76rpx" height="76rpx"></u-image>
              <text class="name">{{ item.name }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "index",
  data() {
    return {
      notice: {},
      noticeList: [],
      msgCount: {},
      appList: [

      ]
    }
  },
  computed: {},
  async onLoad() {
    await this.getInfo()
    await this.getMenuList()
    this.setMarkCount()
    this.noticeMsg()
  },
  methods: {
    getInfo() {
      if (this.Cookies.get('YGF-MOBILE-Token')) {
        this.$u.api.getInfo().then((res) => {
          const user = res.user
          //用户姓名
          this.$u.vuex('vuex_nickName', user.nickName)
          //用户联系方式
          this.$u.vuex('vuex_phone', user.phonenumber || '')
          //部门名称
          this.$u.vuex('vuex_deptName', user?.dept?.deptName || '')
          this.$u.vuex('vuex_postKeys', user.postKeys || '')
        })
      }
    },
    getMenuList() {
      const appList = [
        { key: 'ydd_xcfx', src: '../../../static/newImg/巡查发现.png', name: '巡查发现',  url:'pages/home/<USER>/xcfx/list/list' },
        { key: 'ydd_ajlr', src: '../../../static/newImg/案件录入.png', name: '案件录入', url:'pages/home/<USER>/jycx/list/list' },
        { key: 'ydd_wtcz', src: '../../../static/newImg/问题处置.png', name: '问题处置', url:'pages/home/<USER>/wtcz/list/list' },


        //店铺信息(已完成)
        { key: 'ydd_dpxx', src: '../../../static/newImg/店铺信息.png', name: '店铺信息', url:'pages/home/<USER>/dpxx/list/list'},
        //四位一体(已完成)
        { key: 'ydd_swyt', src: '../../../static/newImg/四位一体.png', name: '四位一体', url:'pages/home/<USER>/swyt/list/list'},
        //黄牛信息(已完成)
        { key: 'ydd_hnxx', src: '../../../static/newImg/黄牛信息.png', name: '黄牛信息',  url:'pages/home/<USER>/hnxx/list/list'},
        //黄牛处置(已完成)
        { key: 'ydd_hncz', src: '../../../static/newImg/黄牛处置.png', name: '黄牛处置',  url:'pages/home/<USER>/hncz/list/list'},
        //一般案件(已完成)
        { key: 'ydd_ybaj', src: '../../../static/img/一般案件.png', name: '一般案件',  url:'pages/home/<USER>/ybaj/list/list'},
        //违停抄牌(已完成)
        { key: 'ydd_wtcp', src: '../../../static/img/违停抄牌.png', name: '违停抄牌',  url:'pages/home/<USER>/wtcp/list/list'},
        //运管执法-日常巡查
        { key: 'ydd_rcxc', src: '../../../static/img/日常巡查.png', name: '日常巡查',  url: 'pages/home/<USER>/rcxc/list/list' },
        //督察监管-监督考核
        { key: 'ydd_jdkc', src: '../../../static/img/监督考核.png', name: '监督考核' , url: 'pages/home/<USER>/dckh/list/list' },


        { key: 'ydd_ssgl', src: '../../../static/newImg/申诉管理.png', name: '申诉管理',  url:'pages/home/<USER>/dckh/RepresentationsList/index'},
        { key: 'ydd_ldjb', src: '../../../static/newImg/领导交办.png', name: '领导交办',  url:'pages/home/<USER>/ldjb/list/list'},
      ]
      this.$u.api.getAPPList().then(res => {
        this.appList = appList.filter(item => {
          return res.data.includes(item.key)
        })
      })

    },
    handleOpen(url) {
      if (url) {
        this.$u.route({ url })
      }
    },
    setMarkCount() {
      uni.$on('changeMark', () => {
        setTimeout(() => {
          this.$u.api.getlistMark().then(resMsgCount => {
            this.msgCount = resMsgCount.data
            this.$u.vuex('vuex_msg_count', resMsgCount.data)
            /* if (resMsgCount.data.all) {
              uni.setTabBarBadge({ index: 0, text: `${resMsgCount.data.all}` })
            } else {
              uni.removeTabBarBadge({ index: 0 })
            } */
            if (resMsgCount.data.message) {
              uni.setTabBarBadge({ index: 2, text: `${resMsgCount.data.message}` })
            } else {
              uni.removeTabBarBadge({ index: 2 })
            }
          })
        }, 1500)
      })
    },
    noticeMsg() {
      uni.$on('noticeMsg', data => {
        this.notice = data
        this.noticeList = [data.messageContent]
      })
    },
    handleOpenNotice() {
      const timestamp = new Date().getTime()
      const readTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
      this.$loading()
      this.$u.api.editNoticeDetail({ noticeDetailId: this.notice.noticeDetailId, isRead: 1, readTime }).then(() => {
        uni.hideLoading()
        this.$u.route({ url: 'pages/my/notice/detail/detail', params: { id: this.notice.noticeId } })
        this.notice = {}
        this.noticeList = []
      }).catch(() => {
        uni.hideLoading()
      })
    },
  },
  watch: {
    vuex_msg_count: {
      deep: true,
      handler(nVal) {
        this.msgCount = nVal
      }
    }
  },
}
</script>

<style scoped lang="scss">
.banner {
  position: absolute;
  .title {
    font-size: 40rpx;
    color: #fff;
    position: absolute;
    top: calc(23rpx + var(--status-bar-height));
    left: 30rpx;
    z-index: 10;
    font-weight: 700;
  }
}
.container {
  // margin-top: -132rpx;
  position: relative;
  z-index: 15;
  margin: calc(118rpx + var(--status-bar-height)) 30rpx 30rpx 30rpx;
  overflow: hidden;
  .container-jsc {

  }
  .container-item {
    border-radius: 36rpx;
    background-color: #fff;
    margin: 30rpx 0 0 0;
    .item-list {
      display: flex;
      flex-wrap: wrap;
      margin: 0 -6rpx;
      .item {
        // 33%: 一行三个  25%: 一行四个
        flex: 0 1 33%;
        &-inner {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 20rpx 0;
          margin: 0 6rpx 14rpx;
          border-radius: 10rpx;
          transition: opacity .3s;
          position: relative;
          &:active {
            opacity: 0.3;
          }
          .name {
            color: #333;
            margin-top: 20rpx;
          }
        }
      }
      // 运管执法
      .ygzfItem{
        flex: 0 1 33.33%;
        &-inner {
          width: 218rpx;
          height: 140rpx;
          padding: 32rpx 0 20rpx 20rpx;
          margin: 0 6rpx 14rpx;
          border-radius: 10rpx;
          transition: opacity .3s;
          position: relative;
          font-size: 26rpx;
          &:active {
            opacity: 0.3;
          }
          .name {
            color: #333;
            margin-top: 10rpx;
          }
        }
      }
      // 交警执法
      .jjzfItem{
        flex: 0 1 50%;
        &-inner {
          display: flex;
          width: 168px;
          height: 70px;
          background: #FFFFFF;
          box-shadow: 0px 0px 4px 0px rgba(30,68,130,0.16);
          border-radius: 7px;
          padding: 32rpx 0 32rpx 34rpx;
          font-size: 26rpx;
          color: #333;
          transition: opacity .3s;
          position: relative;
          &:active {
            opacity: 0.3;
          }
          .jjzfItem-right{
            display: flex;
            flex-direction: column;
            margin-left: 14rpx;
            .name {
              color: #333;
              margin-top: 10rpx;
            }
            .enter{
              font-size: 11px;
              color: #888;
            }
          }
        }
      }
      .lh-item {
        flex: 1;
        border-radius: 10rpx;
        padding: 37rpx 40rpx 38rpx;
        margin: 0 6rpx 14rpx;
        display: flex;
        transition: opacity .3s;
        &:active {
          opacity: 0.3;
        }
        .img {
          flex-shrink: 0;
          margin-right: 20rpx;
        }
        .text-content {
          display: flex;
          flex-direction: column;
          .btn {
            font-size: 22rpx;
            color: #888888;
          }
          .aName{
            font-size: 32rpx;
            font-weight: 500;
            color: #fff;
          }
          .eName{
            margin-top: 8rpx;
            color: #fff;
            opacity: 0.5;
            font-size: 20rpx;
          }
        }
      }
      .f-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        margin: 0 5rpx;
        padding: 35rpx 0 40rpx;
        transition: opacity .3s;
        &:active {
          opacity: 0.3;
        }
        .f-item-img {
          position: relative;
          width: 78rpx;
          height: 78rpx;
        }
        .img {
          margin-bottom: 12rpx;
          flex-shrink: 0;
        }
      }
      .item-wrap{
        flex:0 1 23%;
      }
    }
  }
}
</style>

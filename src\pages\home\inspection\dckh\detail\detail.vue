<template>
	<view>
	<u-form :model="form" ref="uForm" class="container u-border-top" label-width="150" :label-style="labelStyle">
		<view class="p-lr-30">
			<u-form-item label="督查人:" prop="userName" required>
				<u-input v-model="form.userName" type="popup" placeholder="督查人" />
			</u-form-item>
			<u-form-item label="事件描述:" prop="content" label-position="top" :border-bottom="false" required>
				<u-input v-model="form.content" :disabled="disable" type="textarea" placeholder="请输入事件描述" />
			</u-form-item>

			<view class="u-rela">
				<u-form-item label="考核条款:" prop="checkStandardName" label-position="top" required>
					<u-input v-model="form.checkStandardName" :disabled="disable" type="textarea" placeholder="请选择考核条款" />
				</u-form-item>
				<view class="pos-r" @click="handleChooseStandar" v-if="form.status == 1">选择项目</view>
			</view>
			<u-form-item label="考核标准:" prop="checkStandardContent">
				<u-input v-model="form.checkStandardContent" type="popup" placeholder="请选择考核标准" />
			</u-form-item>
			<u-form-item label="积分类型:" prop="type" required>
				<u-radio-group v-model="form.type" disabled @change="typeRadioGroupChange">
					<u-radio name="1">扣分</u-radio>
					<u-radio name="0">加分</u-radio>
				</u-radio-group>
			</u-form-item>
			<u-form-item label="积分值:" prop="checkStandardScore" required>
				<u-input v-model="form.checkStandardScore" :disabled="disable" type="number" placeholder="请输入积分值" />
			</u-form-item>
		</view>

		<!-- 间隔 -->
		<u-gap height="20" bg-color="#F5F5F5"></u-gap>
		<view class="p-lr-30">
			<u-form-item label="考核时间:" prop="happenTime" required>
				<u-input v-model="form.happenTime" type="popup" placeholder="请选择考核时间" @click="showHappenTime = true"  />
				<!-- <u-icon name="calendar"  @click="showHappenTime = true"  size="40"></u-icon>
				<u-picker v-model="showHappenTime" mode="time" :default-time="form.happenTime" :params="params"  @confirm="confirmHappenTime"></u-picker> -->
			</u-form-item>
			<u-form-item label="地址:" prop="address" required>
				<u-input v-model="form.address" type="text" placeholder="请选择地址" />
				<u-button size="mini" type="primary" v-slot="right" @click="handleChooseAddress" v-if="form.status == 1">选择地址</u-button>
			</u-form-item>
			<u-upload
				max-count="4"
				width="157"
				height="157"
				:auto-upload="false"
			  :size-type="['compressed']"
				:file-list="fileList"
				:deletable="false"
				:customBtn="true"
				name="files"
			></u-upload>
		</view>

		<!-- 间隔 -->
		<u-gap height="20" bg-color="#F5F5F5"></u-gap>
		<view class="p-lr-30">
			<!-- form.status  1：新创建，2：推送班长，3：待反馈，9：已办结 -->
			<u-form-item label="班长:" prop="groupUserName" v-if="form.groupUserName">
				<u-input v-model="form.groupUserName" ref="monitor" type="popup" placeholder="班长" />
				<u-button size="mini" type="primary" v-slot="right" v-if="form.status == 1" @click="handleChooseHandleUser({showRadio: 1, type: 'monitor'})">选择人员</u-button>
			</u-form-item>
			<u-form-item label="责任人:" prop="checkUserNames">
				<u-input v-model="form.checkUserNames" ref="responsible" type="popup" placeholder="责任人" />
				<u-button size="mini" type="primary"  v-if="form.status != 3" v-slot="right" @click="handleChooseHandleUser({showRadio: 0, type: 'responsible'})">选择人员</u-button>
			</u-form-item>
		</view>

		<!-- 间隔 -->
		<u-gap height="20" bg-color="#F5F5F5"></u-gap>
		<view class="p-lr-30" v-if="form.status == 3">
			<u-form-item label="反馈内容:" prop="feedback" label-position="top">
				<u-input v-model="form.feedback" type="textarea" placeholder="请输入反馈内容" />
			</u-form-item>
			<u-upload
				ref="happenfile"
				name="files"
				max-count="4"
				width="157"
				height="157"
				:header="header"
				:auto-upload="false"
				:action="action"
				:form-data="happenData"
				:size-type="['compressed']"
				@on-uploaded="handleAllUpload"
				@on-error="handleError"
			></u-upload>
		</view>
	</u-form>
	<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex">
			<!-- <u-button class="u-flex-1 u-m-r-20" type="primary" shape="circle" :custom-style="subStyle" @click="handleSubmit(2)">暂存</u-button> -->
			<u-button class="u-flex-1" type="primary" shape="circle" :loading="aloading" v-if="form.status == 2" :custom-style="subStyle" @click="handleOver(3)">提交</u-button>
			<u-button class="u-flex-1" type="primary" shape="circle" :loading="aloading" v-if="form.status == 3" :custom-style="subStyle" @click="handleOver(9)">办结</u-button>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import gps from '@/common/gps.js'
	export default {
		data() {
			return {
				aloading: false,
				disable:true,
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				form: {address:"浙江省金华市婺城区城北街道"},
				rules: {
					checkUserNames: [{ required: true, message: '请选择督查人', trigger: 'change' }],
					type: [{ required: true, message: '积分类型', trigger: 'change' }],
					content: [{ required: true, message: '请输入事件描述', trigger: 'blur' }],
					checkStandardName: [{ required: true, message: '请选择考核条款', trigger: ['change', 'blur'] }],
					checkStandardScore: [{
							validator: (rule, value, callback) => {
								return !!value;
							},
							message: '请填写积分值',
							trigger: ['change','blur'],
						}],
					happenTime: [{ required: true, message: '请选择考核时间', trigger: 'change' }],
					address: [{ required: true, message: '请选择地址', trigger: 'change' }],
					groupUserName:[{
						validator: (rule, value, callback) => {
							return !!(value || this.form.checkUserNames);
						},
						message: '班长和责任人其中一个必填',
						trigger: ['change','blur'],
					}],
					checkUserNames:[{
						validator: (rule, value, callback) => {
							return !!value;
						},
						message: '请选择责任人',
						trigger: ['change','blur'],
					}]
				},
				showHappenTime: false,
				fileList: [],
				happenData: {
					tableName: 'supervision_check_record',
					status: 3
				}
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				if (!isFail) {
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					this.$implement()
				}
			},
			handleError() {
				// 文件上传失败，弹出提示是否重新上传
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					content: '图片上传失败，是否重新上传？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading('图片上传中')
							this.$refs.happenfile.reUpload()
						} else {
							this.$implement({ immediately: true })
						}
					}
				})
			},
			handleRemove(index, lists) {
				const fileInfo = lists[index]
				const fileId = fileInfo.url.split('?id=')[1]
				if (fileId) {
						return new Promise((resolve, reject) => {
							uni.showModal({
								title: '提示',
								content: '删除后将无法恢复，是否确认删除？',
								success: ({ confirm }) => {
									if (confirm) {
										this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
									} else {
										reject()
									}
								}
							})
						})
				} else {
					return true
				}
			},
			handleChooseStandar() {
				let params = { }
				if (this.form['checkStandardId']) {
					params.defaultCheckedKeys = this.form['checkStandardId']
					params.defaultExpandedKeys = this.form['checkStandardId']
				}
				this.$u.route({ url: 'pages/common/selectStandard/selectStandard', params })
			},
			setStandard(checks) {
				let names = [], ids = [], content = [], type = []
				if (checks && checks.length) checks.forEach(item => {
					names.push(item.title)
					content.push(item.content)
					ids.push(item.checkStandardId)
					type.push(item.type)
				})
				this.form = { ...this.form, checkStandardName: names.join(','), checkStandardContent: content.join(','), checkStandardId: ids.join(','), type: type.join(',') }
			},
			handleChooseAddress() {
				uni.chooseLocation({
					latitude: 29.110764,
					longitude: 119.635857,
					geocode: true,
					success: res => {
						const { address, longitude, latitude } = res
						const lnglat = gps.gcj_decrypt(parseFloat(latitude), parseFloat(longitude))
						this.form = { ...this.form, address, longitude: lnglat.lng, latitude: lnglat.lat }
					},
					fail: () => {
						uni.showToast({
							title: '地图打开失败',
							icon: 'none',
							position: 'bottom'
						})
					}
				})
			},
			// handleChooseHandleUser() {
			// 	let params = { showRadio: 0 }
			// 	if (this.form['checkUserIds']) {
			// 		params.defaultCheckedKeys = this.form['checkUserIds']
			// 		params.defaultExpandedKeys = this.form['checkUserIds']
			// 	}
			// 	this.$u.route({ url: 'pages/common/selectUser/selectUser', params })
			// },
			handleChooseHandleUser(obj) {
				let params = {...obj}
				this.role = params.type
				if (params.type == 'monitor' && this.form.groupUserId) {
					params.defaultCheckedKeys = this.form['groupUserId']
					params.defaultExpandedKeys = this.form['groupUserId']
				} else if (params.type == 'responsible' && this.form.checkUserIds) {
					params.defaultCheckedKeys = this.form['checkUserIds']
					params.defaultExpandedKeys = this.form['checkUserIds']
				}
				this.$u.route({ url: 'pages/common/selectUser/selectUser', params })
			},
			// setUserData(checks,type,parent) {
			// 	let names = [], ids = [], parents = {}
			// 	if (checks && checks.length) checks.forEach(item => {
			// 		names.push(item.label)
			// 		ids.push(item.id)
			// 		parents = parent
			// 	})
			// 	console.log(parents)
			// 	this.form = { ...this.form, checkUserNames: names.join(','), checkUserIds: ids.join(','), deptId: parents.data.id, deptName: parents.label }
			// },
			setUserData(checks,type,parent) {
				let names = [], ids = [], parents = {}
				if (checks && checks.length) checks.forEach(item => {
					names.push(item.label)
					ids.push(item.id)
					parents = parent
				})
				if (type == 'monitor') {
					this.form = { ...this.form, groupUserName: names.join(','), groupUserId: ids.join(',') }
				} else if (type == 'responsible') {
					this.form = { ...this.form, checkUserNames: names.join(','), checkUserIds: ids.join(','), deptId: parents.data.id, deptName: parents.label}
				}
			},
			typeRadioGroupChange() {
				this.form = { ...this.form, type: idx }
			},
			handleSubmit(status) {
				this.$loading()
				const params = { ...this.form, status }
				this.aloading = true
				this.$u.api.updateStandard(params).then(() => {
					/* 接口调用完毕 */
					if(this.$refs.happenfile){
						const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
						this.happenData.businessId = this.form.checkRecordId
						if (uploadFile) {
							this.$loading('图片上传中')
							this.$refs.happenfile.upload()
						} else {
							uni.hideLoading()
							uni.showToast({title: '操作成功'})
							this.$implement()
						}
					} else {
						uni.hideLoading()
						uni.showToast({title: '操作成功'})
						this.$implement()
					}
				}).catch(() => {
					uni.hideLoading()
					this.aloading = false
				})
			},
			handleOver(status) {
				if(status == 3){
					if(this.vuex_id != this.form.userId && this.vuex_id != this.form.groupUserId) return this.mToase('您不是督查人或班长，没有操作权限！')
				}else if(status == 9){
					if(!this.form.checkUserIds.split(',').includes(this.vuex_id.toString())) return this.mToase('您不是责任人，没有操作权限！')
				}
				uni.showModal({
					title: '提示',
					content: '是否确认提交？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$refs.uForm.validate(valid => {
								if (valid) this.handleSubmit(status)
							})
						}
					}
				})
			}
		},
		async onLoad(params) {
			if (params.id) {
				this.$loading()
				const [standardRes, fileRes] = await Promise.all([
					this.$u.api.getStandard({}, params.id),
					this.$u.api.getFileList({ tableName: 'supervision_check_record', businessId: params.id })
				])

				/* 数据请求完毕 */
				uni.hideLoading()
				if (standardRes.code == 200 && fileRes.code == 200) {
					/* 表单数据 */
					this.form = standardRes.data
					if(this.form.status == 1) this.disable = false
					/* 文件数据 */
					this.fileList = fileRes.rows.map(item => {
            return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
					})
				}
			} else {
				const timestamp = new Date().getTime()
				const happenTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = { type: '1', happenTime }
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.pos-r {
	height: 50rpx;
	line-height: 50rpx;
	font-size: 24rpx;
	background-color: #2979ff;
	border-radius: 10rpx;
	position: absolute;
	top: 30rpx;
	right: 0;
	color: #FFFFFF;
	padding: 0 15rpx;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
::v-deep .u-btn--primary{
	border-color:transparent;
}
</style>

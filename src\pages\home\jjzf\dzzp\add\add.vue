<template>
	<view>
		<top-supervise :caseId="form.trafficCaptureId" caseType="trafficCapture" :status="form.status"/>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<!-- <u-form-item label="案件名称:" prop="title" required>
					<u-input v-model="form.title" disabled placeholder="请输入案件名称" />
				</u-form-item> -->
				<u-form-item label="上报人:" prop="userName">
					<u-input v-model="form.userName" disabled placeholder="请输入上报人"  />
				</u-form-item>
				<!-- <u-form-item label="抄告单号:" prop="noticeNo"  required>
					<u-input v-model="form.noticeNo"  placeholder="请输入抄告单号"/>
				</u-form-item> -->
				<!-- <u-form-item label="号牌种类:" prop="carType"  required>
					<u-input v-model="form.carType"  placeholder="请输入号牌种类"/>
				</u-form-item> -->
				<u-form-item label="车牌号码:" prop="carNo"  required>
					<u-input v-model="form.carNo"  placeholder="请输入车牌号码"/>
				</u-form-item>
				<u-form-item label="违规时间:" prop="happenTime" required>
					<u-input v-model="form.happenTime" type="popup" placeholder="请选择违规时间" @click="showHappenTime = true"/>
					<u-picker v-model="showHappenTime" mode="time" :params="params" :default-time="form.happenTime" @confirm="handleItDateCon"></u-picker>
				</u-form-item>
				<!-- <u-form-item label="道路编码:" prop="roadCode" required>
					<u-input v-model="form.roadCode"  placeholder="请选择道路编码"  />
				</u-form-item> -->
				<!-- <u-form-item label="路口:" prop="intersection" required>
					<u-input v-model="form.intersection"  placeholder="请选择路口"  />
				</u-form-item> -->
				<!-- <u-form-item label="违规类型:" prop="type" required>
					<u-input v-model="form.typeName" type="select" :select-open="showTypeList" placeholder="请选择违规类型" @click="showTypeList = true" />
					<u-action-sheet v-model="showTypeList" :list="typeList" @click="handleCycleTypeClick"></u-action-sheet>
				</u-form-item> -->
				<u-form-item label="违规地点:" prop="caseAddress" required>
					<u-input v-model="form.caseAddress"  placeholder="请选择地址描述"  />
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseLocation">选择地址</u-button>
				</u-form-item>
				<!-- <u-form-item label="违法行为:" prop="behavior" required>
					<u-input v-model="form.behavior"  placeholder="请选择违法行为"  />
				</u-form-item> -->
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="违法行为:" prop="behavior" label-position="top" :border-bottom="false">
					<u-input v-model="form.behavior" type="textarea" maxlength="500" height="140" placeholder="请输入内容描述..."/>
				</u-form-item>
				<u-upload
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
					:size-type="['compressed']"
					:file-list="happenFile"
					:before-remove="handleRemove"
					@on-uploaded="handleAllUpload"
					@on-error="handleError"
				></u-upload>
			</view>
			<!-- 间隔 -->
			<!-- <u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="车身颜色:" prop="carColor" required>
					<u-input v-model="form.carColor" disabled placeholder="请输入车身颜色" />
				</u-form-item>
				<u-form-item label="车辆品牌:" prop="carBrand" required>
					<u-input v-model="form.carBrand"   placeholder="请输入车辆品牌"  />
				</u-form-item>
				<u-form-item label="所有人:" prop="carOwnner"  required>
					<u-input v-model="form.carOwnner"  placeholder="请输入所有人"/>
				</u-form-item>
				<u-form-item label="车辆车型:" prop="carModel"  required>
					<u-input v-model="form.carModel"  placeholder="请输入车辆车型"/>
				</u-form-item>
				<u-form-item label="使用性质:" prop="useProperties"  required>
					<u-input v-model="form.useProperties"  placeholder="请输入使用性质"/>
				</u-form-item>
				<u-form-item label="住所地址:" prop="ownnerAddress"  required>
					<u-input v-model="form.ownnerAddress"  placeholder="请输入住所地址"/>
				</u-form-item>
				<u-form-item label="邮政编码:" prop="postalcode"  required>
					<u-input v-model="form.postalcode"  placeholder="请输入邮政编码"/>
				</u-form-item>
				<u-form-item label="联系电话:" prop="phone"  required>
					<u-input v-model="form.phone"  placeholder="请输入联系电话"/>
				</u-form-item>
			</view> -->
		</u-form>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex">
			<u-button class="u-flex-1 u-m-r-20" type="primary" shape="circle" :custom-style="subStyle" @click="handleSubmit(2)">暂存</u-button>
			<u-button class="u-flex-1" type="primary" shape="circle" :custom-style="subStyle" @click="handleOver">办结</u-button>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import gps from '@/common/gps.js'
	import topSupervise from '@/components/top-supervise.vue'
	export default {
		components: {
			topSupervise
		},
		data() {
			return {
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				happenData: {
					tableName: 'case_traffic',
					status: 1
				},
				happenFile:[],
				form:{},
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				showHappenTime: false,
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				showTypeList: false,
				typeList: [],
				rules: {
					carNo: [{ required: true, message: '请填写车牌号码', trigger: 'blur' }],
					happenTime: [{ required: true, message: '请选择违规时间', trigger: 'change' }],
					caseAddress: [{ required: true, message: '请选择违规地点', trigger: ['blur','change'] }],
				},
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			handleChooseLocation() {
				uni.chooseLocation({
					latitude: 29.110764,
					longitude: 119.635857,
					success: res => {
						const { address, longitude, latitude } = res
						const lnglat = gps.gcj_decrypt(parseFloat(latitude), parseFloat(longitude))
						this.form = { ...this.form, caseAddress: "浙江省金华市婺城区城北街道" + address, longitude: lnglat.lng, latitude: lnglat.lat, lnglat: `${lnglat.lng}, ${lnglat.lat}` }
					},
					fail: () => {
						uni.showToast({
							title: '地图打开失败',
							icon: 'none',
							position: 'bottom'
						})
					}
				})
			},
			handleItDateCon(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, happenTime: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleCycleTypeClick(idx) {
				const { type, text } = this.typeList[idx]
				this.form = { ...this.form, type, typeName: text }
			},
			handleRemove(index, lists) {
				const fileInfo = lists[index]
				const fileId = fileInfo.url.split('?id=')[1]
				if (fileId) {
						return new Promise((resolve, reject) => {
							uni.showModal({
								title: '提示',
								content: '删除后将无法恢复，是否确认删除？',
								success: ({ confirm }) => {
									if (confirm) {
										this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
									} else {
										reject()
									}
								}
							})
						})
				} else {
					return true
				}
			},
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				if (!isFail) {
					uni.showToast({title: '操作成功', duration: 3000})
					uni.hideLoading()
					this.$implement()
				}
			},
			handleError() {
				// 文件上传失败，弹出提示是否重新上传
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					content: '图片上传失败，是否重新上传？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading('图片上传中')
							this.$refs.happenfile.reUpload()
						} else {
							this.$implement({ immediately: true })
						}
					}
				})
			},
			handleOver() {
				uni.showModal({
					title: '提示',
					content: '是否确认提交？',
					success: ({ confirm }) => {
						if (confirm) {
							this.handleSubmit(9)
						}
					}
				})
			},
			validateFn(isValid) {
				// 根据传入的状态来确定是否要进行验证，用以区别暂存和办结
				return new Promise((resolve, reject) => {
					if (isValid) {
						this.$refs.uForm.validate(valid => {
							if (valid) {
								resolve()
							} else {
								reject()
							}
						})
					} else {
						resolve()
					}
				})
			},
			handleSubmit(status) {
				this.validateFn(status == 9).then(() => {
					const params = { ...this.form, status }
					// 开始上传
					this.$loading('数据上传中')
					const submitFn = params.trafficCaptureId ? this.$u.api.editTraffic : this.$u.api.addTraffic

					submitFn(params).then(res => {
						// 遍历列表，查询是否有未上传的图片
						const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
						this.happenData.businessId = this.form.trafficCaptureId || res.data.trafficCaptureId
						if (uploadFile) {
							this.$loading('图片上传中')
							this.$refs.happenfile.upload()
						} else {
							uni.showToast({title: '操作成功', duration: 3000})
							uni.hideLoading()
							this.$implement()
						}
					}).catch(() => {
						uni.hideLoading()
					})
				}).catch(() => {})
			}
		},
		async onLoad(params){
			if (params.id) {
				this.$u.api.gettraffic({},params.id).then(res=>{
					this.form = {...res.data}
				})
				this.$u.api.getFileList({ tableName: 'case_traffic',  businessId: params.id }).then(res=>{
					this.happenFile = res.rows.map(item => {
						return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
					})
				})
			} else {
				const timestamp = new Date().getTime()
				const happenTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = { happenTime, userName: this.vuex_nickName, userId: this.vuex_id, caseAddress: '迪耳路金华站路段', longitude: 119.63496993582757, latitude: 29.112914350034448, }
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style>
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

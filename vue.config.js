console.log(process.env.UNI_PLATFORM, 'process.env.UNI_PLATFORM')
if (process.env.UNI_PLATFORM === 'h5') {
  // 由于这种方式的打包，会导致编译生成微信小程序（只验证了微信小程序）无法正常使用，所以必须分开
  let filePath = './static/js/'
  let Timestamp = new Date().getTime()
  module.exports = {
    transpileDependencies: ['uview-ui'],
    devServer: {
      proxy: {
        '/zqzfj': {
          secure: false,
          target: 'https://zqzf.xzzfj.jinhua.gov.cn/zqzfj', //正式环境
          changeOrigin: true,
          pathRewrite: {
            '^/zqzfj': '',
          },
        },
        '/prod-api': {
          secure: false,
          // target: 'http://172.16.10.97:9000/prod-api', //本地俊伟
          // target: 'http://172.16.10.106:9000/prod-api', //本地盛铭
          // target: 'http://172.16.10.118:9000/prod-api', //本地田亮亮
          target: 'http://10.45.13.116/prod-api', //正式
          changeOrigin: true,
          pathRewrite: {
            '^/prod-api': '',
          },
        }
      },
    },
    // ... webpack 相关配置
    filenameHashing: false,
    configureWebpack: {
      // webpack 配置 解决js缓存的问题，目前只适配H5端打包
      output: {
        // 输出重构  打包编译后的 文件目录 文件名称 【模块名称.时间戳】
        filename: `${filePath}[name]-${Timestamp}.js`,
        chunkFilename: `${filePath}[name]-${Timestamp}.js`,
      },
    },
  }
} else {
  // 其他打包需要的相关配置
  module.exports = {
    transpileDependencies: ['uview-ui'],
    devServer: {
      proxy: {
        '/zqzfj': {
          secure: false,
          target: 'https://zqzf.xzzfj.jinhua.gov.cn/zqzfj', //正式环境
          changeOrigin: true,
          pathRewrite: {
            '^/zqzfj': '',
          },
        }
      },
    },
  }
}


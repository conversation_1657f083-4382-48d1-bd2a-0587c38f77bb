<template>
	<view :style="isShow ? 'padding-bottom: 140rpx;' : ''">
		<view class="top u-flex u-flex-col u-col-top u-p-30 u-border-top">
			<text class="u-p-b-20 u-font-b">试卷类型：{{ typeName }}</text>
			<text>交卷时间：{{ updateTime }}</text>
		</view>
		<u-gap height="20" bg-color="#F5F5F5"></u-gap>
		<u-card title="回答正确率" margin="0rpx">
			<view class="charts" slot="body">
				<qiun-data-charts  type="arcbar" :chartData="chartData" :opts="opts" />
			</view>
		</u-card>
		<u-gap height="20" bg-color="#F5F5F5"></u-gap>
		<u-card title="答题卡" margin="0rpx">
			<view class="answer-list" slot="body">
				<view class="item" v-for="(item,idx) in answerList" :key="idx" @click="handleOpen(idx)">
					<text class="item-inner" :class="{ correct: item.status == 1, wrong: item.status == 2 }">{{idx + 1}}</text>
				</view>
			</view>
		</u-card>
		<view class="btn-box" v-if="isShow">
			<u-button type="primary" @click="handleOpenHis">查看解析</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				typeName: '',
				updateTime: '',
				questionList: [],
				answerList: [],
				correctRate: 0,
				isShow: false,
				examId: ''
			}
		},
		computed: {
			opts() {
				return {
					title: {
						name: `${this.correctRate * 100}%`
					},
					subtitle: {
						name: '正确率'
					}
				}
			},
			chartData() {
				return {
					"series": [
						{
							"name": "正确率",
							"data": this.correctRate,
							"color": "#2fc25b"
						}
					]
				}
			}
		},
		methods: {
			fetchData(examId) {
				this.$loading()
				this.$u.api.getQuestionInfo({ examId, userId: this.vuex_id }).then(res => {
					uni.hideLoading()
					const { correctCount, allCount } = res.finishSituation
					this.typeName = res.data.typeName
					this.updateTime = res.data.updateTime
					if (correctCount != undefined && allCount != undefined) {
						this.correctRate = (correctCount / allCount).toFixed(1)
					}
					this.answerList = res.questionList.map(item => {
						return {
							id: item.id,
							userAnswer: item.userAnswer,
							answerDate: item.answerDate,
							status: item.status,
							examId: res.data.id
						}
					})
				}).catch(() => {
					uni.hideLoading()
				})
			},
			handleOpen(idx) {
				if (this.isShow) {
					this.$u.route({ url: 'pages/base/study/rcdt/his/his', params: { id: this.examId, idx } })
				} else {
					this.$implement({ methods:'hadnleJump', immediately: true, data: idx })
				}
			},
			handleOpenHis() {
				this.handleOpen(0)
			}
		},
		onLoad(params) {
			this.examId = params.id
			this.isShow = params.isShow ? true : false
			this.fetchData(this.examId)
		},
		onBackPress(e) {
			if (this.isShow && e.from == 'backbutton') {
				this.$implement({ immediately: true })
				return true
			}
		}
	}
</script>

<style>
.top {
	background-color: #fff;
}
.charts {
	width: 750;
	background-color: #fff;
	height: 300rpx;
}
.item {
	width: 20%;
	display: inline-block;
	text-align: center;
	padding: 20rpx 0;
}
.item-inner {
	width: 60rpx;
	height: 60rpx;
	display: inline-block;
	border: 1px solid #999999;
	border-radius: 50%;
	line-height: 60rpx;
	text-align: center;
}
.correct {
	background-color: #00ca00;
	border-color: #00ca00;
	color: #fff;
}
.wrong {
	background-color: #ea0000;
	border-color: #ea0000;
	color: #fff;
}
.btn-box {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 140rpx;
	padding: 30rpx;
	background-color: #fff;
}
</style>

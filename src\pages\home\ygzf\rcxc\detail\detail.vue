<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="巡查人员:" prop="userName" required>
					<u-input v-model="form.userName" type="text" placeholder="巡查人员" />
				</u-form-item>
				<u-form-item label="辅助人员:" prop="userNames" >
					<u-input v-model="form.userNames" type="popup" placeholder="辅助人员"/>
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('userIds')">选择人员</u-button>
				</u-form-item>
				<u-form-item label="巡查时间:" prop="inspectionTime" required>
					<u-input v-model="form.inspectionTime" type="text" placeholder="请选择巡查时间" @click="showInspectionTime = true"/>
					<u-picker v-model="showInspectionTime" mode="time" :params="params" :default-time="form.inspectionTime" @confirm="handleItDateCon"></u-picker>
				</u-form-item>
				<u-form-item label="当事人:" prop="party" required>
					<u-input v-model="form.party" type="text" placeholder="请输入当事人姓名" />
				</u-form-item>
				<u-form-item label="车辆车牌:" prop="carNo" required>
					<u-input v-model="form.carNo" type="text" placeholder="请输入车辆车牌" />
				</u-form-item>
				<u-form-item label="车辆品牌:" prop="models">
					<u-input v-model="form.models" type="text" placeholder="请输入车辆品牌" />
				</u-form-item>
				<u-form-item label="车辆类型:" prop="carTypeName" required>
					<u-input v-model="form.carTypeName" type="select" :select-open="showCarType" placeholder="请选择车辆类型" @click="showCarType = true" />
					<u-action-sheet v-model="showCarType" :list="transportCarType" @click="handleTypeClick"></u-action-sheet>
				</u-form-item>
				<u-form-item label="营运公司:" prop="operationCompany">
					<u-input v-model="form.operationCompany" type="text" placeholder="请输入营运公司" />
				</u-form-item>
				<u-form-item label="发生地址:" prop="address" required>
					<u-input v-model="form.address" type="text" placeholder="请选择地址" />
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseLocation">选择地址</u-button>
				</u-form-item>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="是否有问题:" prop="isProblem" required>
					<u-radio-group v-model="form.isProblem">
						<u-radio name="1" >有</u-radio>
						<u-radio name="0" >无</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="问题类型:" prop="problemTypeName" v-if="form.isProblem == 1" required>
					<u-input v-model="form.problemTypeName" type="select" :select-open="showList" placeholder="请选择问题类型" @click="showList = true" />
					<u-action-sheet v-model="showList" :list="problemTypeList" @click="handlePoliceClick"></u-action-sheet>
				</u-form-item>
				<u-form-item label="检查概况:" prop="content" label-position="top" :border-bottom="false">
					<u-input v-model="form.content" type="textarea" maxlength="300" height="140" placeholder="请输入检查概况..." />
				</u-form-item>
				<u-upload
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
				  :size-type="['compressed']"
					:file-list="happenFile"
					:before-remove="handleRemove"
					@on-uploaded="handleAllUpload"
					@on-error="handleError"
				></u-upload>
			</view>
		</u-form>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex">
			<u-button class="u-flex-1 u-m-r-20" type="primary" shape="circle" :custom-style="subStyle" @click="handleEdit(2)">暂存</u-button>
			<u-button class="u-flex-1" type="primary" shape="circle" :custom-style="subStyle" @click="handleOver">办结</u-button>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import gps from '@/common/gps.js'
	export default {
		data() {
			return {
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				showCarType: false,
				transportCarType: [],
				showInspectionType: false,
				inspectionTypeList: [],
				showInspectionTime: false,
				isShowFTaxiType: false,
				isShowSTaxiType: false,
				form: {address:""},
				rules: {
					// title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
					party: [{ required: true, message: '请输入当事人姓名', trigger: 'blur' }],
					carNo: [{ required: true, message: '请输入车辆车牌', trigger: 'blur' }],
					userName: [{ required: true, message: '巡查人员', trigger: 'blur' }],
					inspectionTime: [{ required: true, message: '请选择巡查时间', trigger: 'change' }],
					carTypeName: [{ required: true, message: '请选择车辆类型', trigger: 'change' }],
					content: [{ required: true, message: '请输入检查概况', trigger: 'blur' }],
					address: [{ required: true, message: '请选择地址', trigger: 'change' }],
					isProblem: [{ required: true, message: '请选择是否有问题', trigger: 'change' }],
					problemTypeName: [{ required: true, message: '请选择问题类型', trigger: 'change' }],
				},
				happenData: {
					tableName: 'case_transport',
					status: 1
				},
				happenFile: [],
				showList: false,
				problemTypeList: []
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			handlePoliceClick(idx){
				const { type, text } = this.problemTypeList[idx]
				this.form = { ...this.form, problemType:type, problemTypeName: text }
			},
			handleChooseHandleUser(type) {
				let params = {}
				if (this.form[type]) {
					params.defaultCheckedKeys = this.form[type]
					params.defaultExpandedKeys = this.form[type]
				}
				params.showRadio = 0
				params.type = type
				this.$u.route({ url: 'pages/common/selectUser/selectUser', params })
			},
			setUserData(checks,type) {
				// 选择好人员后的回调
				let checkData = {}
				if(checks.length == 1){
					checkData = checks[0]
				}else{
					checks.map((v,i)=>{
						if(i==0) {
							checkData.label = v.label+''
							checkData.id = v.id+''
						}else{
							checkData.label += ',' + v.label+''
							checkData.id += ',' + v.id+''
						}
					})
				}
				if(type == 'userIds'){
					if (checkData) this.form = { ...this.form, userNames: checkData.label, userIds: checkData.id }
				}
			},
			async handleTypeClick(idx) {
				const { dictValue, text, remark } = this.transportCarType[idx]
				this.form = { ...this.form, carType: dictValue, carTypeName: text }
			},
			handleChooseLocation() {
				uni.chooseLocation({
					latitude: 29.110764,
					longitude: 119.635857,
					success: res => {
						const { address, longitude, latitude } = res
						const lnglat = gps.gcj_decrypt(parseFloat(latitude), parseFloat(longitude))
						this.form = { ...this.form, address: "浙江省金华市婺城区城北街道" + address, longitude: lnglat.lng, latitude: lnglat.lat, lnglat: `${lnglat.lng}, ${lnglat.lat}` }
					},
					fail: () => {
						uni.showToast({
							title: '地图打开失败',
							icon: 'none',
							position: 'bottom'
						})
					}
				})
			},
			handleItDateCon(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, happenDate: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleRemove(index, lists) {
				const fileInfo = lists[index]
				const fileId = fileInfo.url.split('?id=')[1]
				if (fileId) {
						return new Promise((resolve, reject) => {
							uni.showModal({
								title: '提示',
								content: '删除后将无法恢复，是否确认删除？',
								success: ({ confirm }) => {
									if (confirm) {
										this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
									} else {
										reject()
									}
								}
							})
						})
				} else {
					return true
				}
			},
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				if (!isFail) {
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					this.$implement()
				}
			},
			handleError() {
				// 文件上传失败，弹出提示是否重新上传
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					content: '图片上传失败，是否重新上传？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading('图片上传中')
							this.$refs.happenfile.reUpload()
						} else {
							this.$implement({ immediately: true })
						}
					}
				})
			},
			handleOver() {
				uni.showModal({
					title: '提示',
					content: '是否确认办结？',
					success: ({ confirm }) => {
						if (confirm) {
							this.handleEdit(9)
						}
					}
				})
			},
			validateFn(isValid) {
				// 根据传入的状态来确定是否要进行验证，用以区别暂存和办结
				return new Promise((resolve, reject) => {
					if (isValid) {
						this.$refs.uForm.validate(valid => {
							if (valid) {
								// 图片验证，没有图片不通过验证
								if (!this.$refs.happenfile.lists.length) {
									this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
									reject()
								} else {
									resolve()
								}
							} else {
								reject()
							}
						})
					} else {
						resolve()
					}
				})
			},
			handleEdit(status) {
				this.validateFn(status == 9).then(() => {
					const params = { ...this.form, status, type: 0 }
					// 开始上传
					this.$loading('数据上传中')
					this.$u.api.editTransport(params).then(res => {
						// 遍历列表，查询是否有未上传的图片
						const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
						this.happenData.businessId = this.form.transportId
						if (uploadFile) {
							this.$loading('图片上传中')
							this.$refs.happenfile.upload()
						} else {
							uni.showToast({title: '操作成功'})
							uni.hideLoading()
							this.$implement()
						}
					}).catch(() => {
						uni.hideLoading()
					})
				}).catch(() => {})
			}
		},
		onLoad(params) {
			// 获取业务数据
			if (params.id) {
				this.$loading()
				Promise.all([
					this.$u.api.getTransport({}, params.id),
					this.$u.api.getFileList({ tableName: 'case_transport', status: 1, businessId: params.id }),
					this.$u.api.getDicts({}, 'transport_car_type'),
					this.$u.api.getDicts({}, "transport_problem_type")
				]).then(resAry => {
					uni.hideLoading()
					const formData = resAry[0].data
					const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
					this.form = { ...formData, lnglat }
					this.happenFile = resAry[1].rows.map(item => {
						return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
					})
					this.transportCarType = resAry[2].data.map(item => {
						item.text = item.dictLabel
						return item
					})
					this.problemTypeList = resAry[3].data.map(item => ({text: item.dictLabel, type: item.dictValue}))
				}).catch(() => {
					uni.hideLoading()
				})
			} else {
				const timestamp = new Date().getTime()
				const inspectionTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = { inspectionTime, userName: this.vuex_nickName, userId: this.vuex_id }
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

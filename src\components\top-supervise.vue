<template>
	<view class="u-rela" v-if="status != 9">
		<u-alert-tips type="warning" :description="description" v-if="isShowdesc"></u-alert-tips>
		<view class="dc-btn" v-if="isShow">
			<u-button type="error" size="mini" :loading="loading" v-if="!isSupervise" @click="handleSuperrvise">督查</u-button>
			<u-button type="error" size="mini" :loading="loading" v-else @click="handleRemoveSuperrvise">取消督查</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		name:"top-supervise",
		props: {
			caseId: {
				type: Number,
				default: 0
			},
			caseType: {
				type: String,
				default: ''
			},
			status: {
				type: [Number, String],
				default: 0
			}
		},
		data() {
			return {
				loading: false,
				description: '正在查询中...',
				isSupervise: false,
				superviesId: 0
			};
		},
		computed: {
			isShow() {
				if (Array.isArray(this.vuex_appList) && this.vuex_appList.length && this.vuex_appList.includes('lddb')) {
					// 拥有督办权限可以督办警情
					return true
				} else {
					return false
				}
			},
			isShowdesc() {
				if (this.isShow) {
					// 领导
					return true
				} else {
					return this.isSupervise
				}
			}
		},
		watch: {
			caseId(nval) {
				if (nval) {
					this.fetchData()
				}
			}
		},
		methods: {
			fetchData() {
				this.loading = true
				this.$u.api.getUrgesList({ caseId: this.caseId, type: this.caseType }).then(res => {
					this.loading = false
					if (res.rows && res.rows.length) {
						// 案件正在被督察，组件可见
						this.isSupervise = true
						this.description = `领导 ${res.rows[0].userName} 正在督查该案件`
						this.superviesId = res.rows[0].urgeId
					} else {
						this.isSupervise = false
						// 案件没有被督察，拥有督查权限的人可见，其余人不可见
						this.description = '您可以点击右边按钮，督查该案件'
					}
				}).catch(() => {
					this.loading = false
					this.isSupervise = false
				})
			},
			handleSuperrvise() {
				this.loading = true
				const timestamp = new Date().getTime()
				this.$u.api.addUrges({
					caseId: this.caseId,
					type: this.caseType,
					userId: this.vuex_id,
					userName: this.vuex_nickName,
					urgeTime: this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				}).then(res => {
					this.loading = false
					this.description = `领导 ${res.data.userName} 正在督查该案件`
					this.isSupervise = true
					this.mToase('操作成功')
				}).catch(() => {
					this.loading = false
				})
			},
			handleRemoveSuperrvise() {
				this.loading = true
				this.$u.api.removeUrges({}, this.superviesId).then(() => {
					this.loading = false
					this.isSupervise = false
					this.description = '您可以点击右边按钮，督查该案件'
					this.mToase('操作成功')
				}).catch(() => {
					this.loading = false
				})
			}
		}
	}
</script>

<style scoped lang="scss">
.dc-btn {
	position: absolute;
	top: 10rpx;
	right: 10rpx;
}
</style>

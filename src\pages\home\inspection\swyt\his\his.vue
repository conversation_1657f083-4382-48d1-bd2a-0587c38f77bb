<template>
	<view>
		<top-supervise :caseId="form.fourId" caseType="four" :status="form.status" />
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<!-- <u-form-item label="标题:" prop="title">
					<u-input v-model="form.title" placeholder="请输入标题" disabled/>
				</u-form-item> -->
				<u-form-item label="类型:" prop="typeText">
					<u-input v-model="form.typeText" type="popup" placeholder="请选择类型"/>
				</u-form-item>
				<u-form-item label="发起人员:" prop="userName">
					<u-input v-model="form.userName" type="text" disabled placeholder="请选择发起人员"  />
				</u-form-item>
				<u-form-item label="联系电话:" prop="phone">
					<u-input v-model="form.phone" disabled placeholder="请输入发起人员联系电话" />
				</u-form-item>
				<u-form-item label="发生时间:" prop="happenDate">
					<u-input v-model="form.happenDate" type="popup" placeholder="请选择发生时间" />
				</u-form-item>
				<u-form-item label="发生地址:" prop="address">
					<u-input v-model="form.address" type="popup" placeholder="请选择地址" />
				</u-form-item>
				<u-form-item label="经纬度:" prop="lnglat" :border-bottom="false">
					<u-input v-model="form.lnglat" type="popup" disabled  placeholder="请选择地址"/>
				</u-form-item>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="内容描述:" prop="content" label-position="top" :border-bottom="false">
					<u-input v-model="form.content" type="textarea" maxlength="300" height="140" disabled placeholder="请输入内容描述..."/>
				</u-form-item>
				<u-upload
					ref="happenfile"
					max-count="4"
					width="157"
					height="157"
					:auto-upload="false"
				  :size-type="['compressed']"
					:file-list="happenFile"
					:deletable="false"
					:customBtn="true"
					name="files"
				></u-upload>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="处理人员:" prop="handleUserName">
					<u-input v-model="form.handleUserName" type="popup" placeholder="请选择处理人员" />
				</u-form-item>
				<u-form-item label="处理完成时间:" prop="handleDate">
					<u-input v-model="form.handleDate" type="popup" placeholder="请输入处理结果"/>
				</u-form-item>
				<u-form-item label="处理结果:" prop="handleContent" label-position="top" :border-bottom="false">
					<u-input v-model="form.handleContent" type="textarea" maxlength="300" height="140" disabled placeholder="请输入处理结果..."/>
				</u-form-item>
				<u-upload
					ref="handlefile"
					max-count="4"
					width="157"
					height="157"
					name="files"
					:auto-upload="false"
				  :size-type="['compressed']"
					:file-list="handleFile"
					:deletable="false"
					:customBtn="true"
				></u-upload>
			</view>
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30" v-if="form.rejectContent">
				<u-form-item label="驳回原因:" label-position="top" :border-bottom="false">
					<u-input v-model="form.rejectContent" type="textarea" maxlength="300" height="140" disabled/>
				</u-form-item>
			</view>
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
		</u-form>
		<!-- 撤回选项 -->
		<u-popup v-model="showPopup" mode="bottom" closeable>
			<u-form :model="form" ref="popForm" class="u-p-24" label-position="top" label-width="200" :label-style="labelStyle">
				<u-form-item label="撤回原因" :border-bottom="false">
					<u-input type="textarea" maxlength="300" height="140" v-model="form.rejectContent" />
				</u-form-item>
				<view class="u-flex u-row-between u-m-t-24">
					<u-button type="primary" shape="circle" @click="handleRevoke" class="btn u-flex-1 u-m-t-24">确定</u-button>
				</view>
			</u-form>
		</u-popup>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex u-row-between" v-if="showBtn">
			<u-button type="error" class="u-flex-1 u-m-r-30" shape="circle" :custom-style="revokeStyle" @click="showPopup = true">驳回</u-button>
			<u-button type="primary" class="u-flex-1" shape="circle" :custom-style="subStyle" @click="handleSubmit">办结</u-button>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import topSupervise from '@/components/top-supervise.vue'

	export default {
		components: {
			topSupervise
		},
		data() {
			return {
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				revokeStyle: {
					height: '86rpx',
					backgroundColor: '#fa3534'
				},
				typeList: [
          { type: 3, text: '市政道路' },
          { type: 4, text: '园林' },
          { type: 1, text: '绿化' },
          { type: 2, text: '环卫保洁' }
				],
				form: {address:""},
				happenFile: [],
				handleFile: [],
				showPopup: false,
				showBtn: false
			}
		},
		methods: {
			handleSubmit() {
				uni.showModal({
					title: '提示',
					content: '是否确认完成？',
					success: ({ confirm }) => {
						if (confirm) {
							const params = { ...this.form, status: 9 }
							this.$loading('数据上传中')
							this.$u.api.caseEdit(params).then(res => {
								uni.showToast({ title: '操作成功', mask: true })
								uni.hideLoading()
								this.$implement()
							}).catch(() => {
								uni.hideLoading()
							})
						}
					}
				})
			},
			handleRevoke() {
				const { fourId, rejectContent } = this.form
				if (!rejectContent || rejectContent.trim() === '') {
					uni.showToast({ title: '请输入驳回原因', icon: 'none', mask: true })
					return
				}
				uni.showModal({
					title: '提示',
					content: '是否确认驳回？',
					success: ({ confirm }) => {
						if (confirm) {
							const params = { fourId, rejectContent, status: 0 }
							this.$loading('数据上传中')
							this.$u.api.caseRevoke(params).then(res => {
								uni.showToast({ title: '操作成功', mask: true })
								uni.hideLoading()
								this.$implement()
							}).catch(() => {
								uni.hideLoading()
							})
						}
					}
				})
			}
		},
		onLoad(params) {
			if (params.id) {
				this.$loading()
				Promise.all([
					this.$u.api.getCaseDetail({}, params.id),
					this.$u.api.getFileList({ tableName: 'case_four_in_one', businessId: params.id })
				]).then(resAry => {
					const formData = resAry[0].data
					const typeText = this.typeList.find(item => item.type == formData.type)
					const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
					const timestamp = new Date().getTime()
					const handleDate = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
					if (!formData.handleDate) formData.handleDate = handleDate
					if (formData.status == 1 && formData.handleUserId == this.vuex_id) this.showBtn = true
					this.form = { ...formData, typeText: typeText ? typeText.text : '', lnglat }
					// 图片部分
					resAry[1].rows.forEach(item => {
						const url =  { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
						if (item.status == 1) {
							this.happenFile.push(url)
						} else {
							this.handleFile.push(url)
						}
					})
					uni.hideLoading()
				}).catch(() => {
					uni.hideLoading()
				})
			} else {
				const timestamp = new Date().getTime()
				const happenDate = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = { happenDate, userName: this.vuex_username, userId: this.vuex_id }
			}
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 115rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

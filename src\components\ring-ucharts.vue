<template>
	<view class="charts-box">
		<qiun-data-charts type="ring" :chartData="chartData" :opts="eopts" @cilck="top" :canvasId="canvasId" />
	</view>
</template>

<script>
	export default {
		name:"ping-ucharts",
		props:{
			canvasId:{
				type:String
			},
			datas:{
				type:Array
			}
		},
		watch:{
			datas(newVle,oldVal){
				console.log(newVle,oldVal);
				this.chartData = {series:[{data:newVle}]}
			}
		},
		data() {
			return {
				chartData: {
					series: [{
						data: this.datas
					},],
				},
				eopts: {
						"color": [
							"#1890FF",
							"#91CB74",
							"#FAC858",
							"#EE6666",
							"#73C0DE",
							"#3CA272",
							"#FC8452",
							"#9A60B4",
							"#ea7ccc"
						],
						"errorReload": true,
						"fontSize": 13,
						"fontColor": "#ffffff",
						"enableScroll": false,
						"touchMoveLimit": 60,
						"enableMarkLine": false,
						"dataLabel": true,
						"dataPointShape": true,
						"dataPointShapeType": "hollow",
						"tapLegend": true,
						"legend": {
							"show": true,
							"position": "bottom",
							"float": "left",
							"padding": 5,
							"margin": 5,
							"backgroundColor": "rgba(0,0,0,0)",
							"borderColor": "rgba(0,0,0,0)",
							"borderWidth": 0,
							"fontSize": 12,
							"fontColor": "#ffffff",
							"lineHeight": 24,
							"hiddenColor": "#CECECE",
							"itemGap": 20
						},
						"title": {
							"name": "",
							"fontSize": 15,
							"color": "#ffffff",
							"offsetX": 0,
							"offsetY": 0
						},
						"subtitle": {
							"name": "",
							"fontSize": 25,
							"color": "#7cb5ec",
							"offsetX": 0,
							"offsetY": 0
						},
						"extra": {
							"ring": {
								"ringWidth": 30,
								"centerColor": "#192250",
								"activeOpacity": 0.5,
								"activeRadius": 10,
								"offsetAngle": 0,
								"customRadius": 0,
								"labelWidth": 15,
								"border": true,
								"borderWidth": 4,
								"borderColor": "#192250",
								"linearType": "none"
							},
							"tooltip": {
								"showBox": true,
								"showArrow": false,
								"showCategory": false,
								"borderWidth": 0,
								"borderRadius": 0,
								"borderColor": "#000000",
								"borderOpacity": 0.7,
								"bgColor": "#000000",
								"bgOpacity": 0.7,
								"gridType": "solid",
								"dashLength": 4,
								"gridColor": "#CCCCCC",
								"fontColor": "#FFFFFF",
								"splitLine": true,
								"horizentalLine": false,
								"xAxisLabel": false,
								"yAxisLabel": false,
								"labelBgColor": "#FFFFFF",
								"labelBgOpacity": 0.7,
								"labelFontColor": "#666666"
							}
					},
				},
			}
		},
		methods:{
			top(e){
				console.log(e);
			}
		}
	}
</script>

<style>
	.charts-box {
		width: 100%;
		height: 547rpx;
	}
</style>

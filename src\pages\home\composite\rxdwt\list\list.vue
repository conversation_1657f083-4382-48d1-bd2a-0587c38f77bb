<template>
	<view>
		<view class="top-search u-flex u-col-center">
			<view class="top-search-left u-flex u-flex-1">
				<u-image src="@/static/img/top-search.png" width="35rpx" height="35rpx"></u-image>
				<u-input v-model="searchValue" type="text" class="u-flex-1 u-m-l-20"></u-input>
			</view>
			<view class="top-search-right">
				<view class="top-search-right-btn u-flex u-row-center u-col-center" @click="handleSearch">
					<u-image src="@/static/img/filter.png" width="28rpx" height="28rpx" class="img"></u-image>
					<text>筛选</text>
				</view>
			</view>
		</view>
		<!-- 列表 -->
		<view class="container">
			<view class="list">
				<view class="list-item u-flex u-col-top " v-for="(item, idx) in listData" :key="idx" @click="handleOpen(item)">
					<u-image  class="img" src="@/static/img/list-icon.png" width="60rpx" height="60rpx"></u-image>
					<view class="list-item-content u-m-l-24 u-flex u-flex-col u-flex-1 u-col-top">
						<text class="title u-line-1">{{ item.hphm }}</text>
						<text class="text u-line-1">采集人员: {{ item.cjmj }}</text>
						<text class="text u-line-1">罚款金额: {{ item.fkje }} 元</text>
						<text class="text u-line-1" >缴款标记: {{ item.jkbj | jkbjName}}</text>
						<text class="text u-line-1" >违法地址: {{ item.wfdz }}</text>
					</view>
					<view class="list-item-state u-flex u-col-center u-row-right">
						<view class="circle" :style="{ backgroundColor: circleColor[item.status] }"></view>
						<text>{{ item.status | statusName }}</text>
					</view>	
				</view>
				<u-loadmore :status="status" class="u-m-t-20" />
			</view>
		</view>
		<!-- 操作按钮 -->
		<!-- <view class="bottom-btn u-flex u-row-center u-col-center" @click="$u.route('pages/home/<USER>/rxzf/add/add' )">
			<u-image src="@/static/img/btn-add-icon.png" width="33rpx" height="33rpx"></u-image>
			<text class="text">新增执法</text>
		</view> -->
	</view>
</template>

<script>
	export default {
		filters: {
			statusName(status) {
				const statusObj = { 1:'进行中', 9:'已完结' }
				if (statusObj) return statusObj[status]
			},
			jkbjName(status){
				const statusObj = { 0:'未缴款', 1:'已缴款' }
				return statusObj[status]
			}
		},
		data() {
			return {
				list: [],
				pageNum: 1,
				pageSize: 10,
				searchValue: '',
				listData: [],
				status: 'loadmore',
				circleColor: {
					2: '#FAB71C',
					9: '#bdc3bf'
				}
			}
		},
		methods: {
			rePageData() {
				this.pageNum = 1
				this.fetchData()
			},
			fetchData() {
				const { pageNum, pageSize, searchValue } = this
				let params = { pageNum, pageSize }
				if (searchValue) params.searchValue = searchValue
				this.status = 'loading'
				this.$u.api.sidewalkList(params).then(res => {
					if (pageNum === 1) {
						this.listData = res.rows
						uni.stopPullDownRefresh()
					} else {
						this.listData = this.listData.concat(res.rows)
					}
					this.status = res.rows.length < 10 ? 'nomore' : 'loadmore'
				}).catch((err) => {
					this.status = 'loadmore'
					uni.stopPullDownRefresh()
				})
			},
			handleSearch() {
				this.pageNum = 1
				this.status= 'loadmore',
				this.fetchData()
			},
			handleOpen(item){
				this.$u.route({ url: 'pages/home/<USER>/rxdwt/detail/detail', params: { id: item.id } })
			}
		},
		onLoad() {
			this.fetchData()
		},
		onPullDownRefresh() {
			this.pageNum = 1
			this.fetchData()
		},
		onReachBottom() {
			if (this.status == 'loadmore') {
				this.pageNum++
				this.fetchData()
			}
		}
	}
</script>

<style lang="scss">
.top-search {
	width: 100%;
	height: 103rpx;
	background-color: #fff;
	padding: 0 30rpx;
	box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1);
	position: fixed;
	z-index: 10;
	top: 0;
	/* #ifdef H5 */
	top: 44px;
	/* #endif */
	&-left {
		height: 68rpx;
		background-color: #F5F5F5;
		border-radius: 68rpx;
		padding: 0 20rpx;
		margin-right: 20rpx;
	}
	&-right {
		&-btn {
			width: 136rpx;
			height: 68rpx;
			border-radius: 68rpx;
			background-color: #327BF0;
			color: #fff;
			font-size: 28rpx;
			transition: all 0.5s;
			.img {
				margin-right: 10rpx;
			}
			&:active {
				opacity: 0.3;
			}
		}
	}
}

// 主体
.container {
	padding-top: 103rpx;
	background-color: #f5f5f5;
	// min-height: 100vh;
	.list {
		padding-bottom: 209rpx;
		.list-item {
			margin: 20rpx 30rpx 0;
			background-color: #FFFFFF;
			border-radius: 12rpx;
			box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1);
			padding: 20rpx 20rpx 30rpx;
			.img {
				flex-shrink: 0;
				margin-right: 20rpx;
			}
			&-content {
				max-width: 500rpx;
				color: #808080;
				line-height: 32rpx;
				font-size: 24rpx;
				.title {
					width: 100%;
					line-height: 60rpx;
					font-weight: 700;
					font-size: 34rpx;
					color: #333333;
				}
				.text {
					width: 100%;
				}
			}
			&-state {
				width: 180rpx;
				flex-shrink: 0;
				font-size: 28rpx;
				font-weight: 700;
				line-height: 60rpx;
				margin-left: 20rpx;
				.circle {
					width: 16rpx;
					height: 16rpx;
					background-color: #EC5656;
					border-radius: 50%;
					margin-right: 10rpx;
				}
			}
		}
	}
}

// 按钮
.bottom-btn {
	width: 220rpx;
	height: 88rpx;
	position: fixed;
	bottom: 40rpx;
	left: 50%;
	margin-left: -110rpx;
	background-color: #327BF0;
	color: #FFFFFF;
	border-radius: 88rpx;
	box-shadow: 0px 0px 14px 0px rgba(82, 97, 121, 0.3);
	transition: all 0.5s;
	z-index: 10;
	&:active {
		background-color: #73a6f7;
	}
	.text {
		font-size: 28rpx;
		margin-left: 15rpx;
	}
}
</style>

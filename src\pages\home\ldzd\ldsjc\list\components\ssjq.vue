<template>
	<view class="">
		<view class="ssjq-list u-flex u-row-between" v-for="(item,index) in ssjqData" :key="index">
			<view class="ssjq-list-flex">
				<view class="u-font-32">{{item.title||""}}</view>
				<view class="">事件类型: {{item.typeName||""}}</view>
				<view class="">处理部门: {{item.deptName}}</view>
			</view>
			<view class="ssjq-list-flex  ssjq-list-right">
				<view class="ssjq-list-flex-state u-flex u-col-center">
					<view class="circle" :style="{ backgroundColor: (item.statusName == '处理中' ?'#FAB71C' :'#909399' )}"></view>
					<text>{{item.statusName}}</text>
				</view>
				<view class="">{{item.happenDate}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		name:"ssjq",
		props:{
			ssjqData:{
				type:Array
			}
		}
	}
</script>

<style lang="scss" scoped>
	.ssjq-list {
		width: 100%;
		height: 189rpx;
		background: rgba(255, 255, 255, 0.08);
		color: #fff;
		padding: 30rpx;
		font-size: 24rpx;
		border-radius: 14px;
		margin-bottom: 20rpx;
		&-right {
			align-items: flex-end;
		}
	
		&-flex {
			display: flex;
			height: 100%;
			justify-content: space-between;
			flex-direction: column;
			flex: 2;
			&-state {
				flex-shrink: 0;
				font-size: 28rpx;
				font-weight: 700;
				line-height: 60rpx;
				margin-left: 20rpx;
	
				.circle {
					width: 16rpx;
					height: 16rpx;
					background-color: #EC5656;
					border-radius: 50%;
					margin-right: 10rpx;
				}
			}
		}
	
	}
</style>

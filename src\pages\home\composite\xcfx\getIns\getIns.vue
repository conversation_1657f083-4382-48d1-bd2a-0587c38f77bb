<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<!-- <u-form-item label="巡查对象:" prop="obj" >
					<u-radio-group v-model="objValue" disabled>
						<u-radio
							v-for="(item, index) in objList" :key="index"
							:name="item.id"
							:disabled="item.disabled"
						>
							{{item.name}}
						</u-radio>
					</u-radio-group>
				</u-form-item> -->
				<!-- <u-form-item label="标题:" prop="inspectionName" required >
					<u-input v-model="form.inspectionName" disabled placeholder="请输入标题" />
				</u-form-item> -->
				<view>
					<u-form-item label="店铺名称:" prop="businessName">
						<u-input v-model="form.businessName" disabled placeholder="请扫码自动完成填写" />
					</u-form-item>
					<u-form-item label="营业执照号码:" prop="businessNo">
						<u-input v-model="form.businessNo" disabled placeholder="请扫码自动完成填写" />
					</u-form-item>
					<u-form-item label="店铺地址:" prop="shopAddress">
						<u-input v-model="form.shopAddress" disabled placeholder="店铺地址"/>
						<u-button size="mini" type="primary" v-slot="right" @click="openMap">导航</u-button>
					</u-form-item>
					<u-form-item label="任务下发时间:" prop="assignDate">
						<u-input v-model="form.assignDate" disabled type="text"  placeholder="任务下发时间"  />
					</u-form-item>
					<u-form-item label="巡查类别:" prop="shopTypeName">
						<u-input v-model="form.shopTypeName" type="popup" placeholder="请选择巡查类别" />
					</u-form-item>
				</view>
				<!-- <u-form-item label="当事人:" prop="contact">
					<u-input v-model="form.contact" disabled type="text"  placeholder="请选择当事人"  />
				</u-form-item>
				<u-form-item label="联系电话:" prop="phone">
					<u-input v-model="form.phone" disabled type="number" placeholder="请输入联系电话" />
				</u-form-item> -->
				<!-- <u-form-item label="性别:" prop="sex">
					<u-radio-group v-model="form.sex"  disabled>
						<u-radio
							v-for="(item, index) in sexList" :key="index"
							:name="item.id"
							:disabled="item.disabled"
						>
							{{item.name}}
						</u-radio>
					</u-radio-group>
				</u-form-item> -->
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="内容描述:" prop="inspectionContent" label-position="top" :border-bottom="false">
					<u-input v-model="form.inspectionContent" disabled type="textarea" maxlength="300" height="140" placeholder="请输入内容描述..."/>
				</u-form-item>
			</view>
		</u-form>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex u-row-between">
			<view class="u-flex-1">
				<u-button  type="primary" shape="circle" :custom-style="subStyle" @click="handleSubmit(2)">我来巡查</u-button>
			</view>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import Map from '@/common/openMap.js'
	import gps from '@/common/gps.js'

	export default {
		data() {
			return {
				objList:[{id:1,name:'店铺'},{id:2,name:'其他'}],
				sexList:[{id:1,name:'男'},{id:2,name:'女'}],
				isList:[{id:1,name:'是'},{id:0,name:'否'}],
				objValue:1,
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				showList: false,
				showTypeList: false,
				showunionList:false,
				showTemporaryList:false,
				typeList: [],
				categoryList:[],
				unionList:[],
				temporaryList:[],
				showHappenDate: false,
				showAssignDate:false,
				form: {},
				rules: {
					// inspectionName: [{ required: true, message: '请输入标题', trigger: 'blur' }],
					inspectionContent: [{ required: true, message: '请输入内容', trigger: 'blur' }],
					typeText: [{ required: true, message: '请选择类型', trigger: 'change' }],
					userName: [{ required: true, message: '请选择巡查人员', trigger: 'change' }],
					contact: [{ required: true, message: '请选择联系人', trigger: 'change' }],
					phone: [
						{ required: true, message: '请输入当事人联系电话', trigger: 'blur' },
						// {
						// 	validator: (rule, value, callback) => {
						// 		return this.$u.test.mobile(value);
						// 	},
						// 	message: '手机号码不正确',
						// 	trigger: ['change','blur'],
						// }
					],
					happenDate: [{ required: true, message: '请选择发生时间', trigger: 'change' }],
					address: [{ required: true, message: '请选择地址', trigger: 'change' }],
					lnglat: [{ required: true, message: '请选择地址', trigger: 'change' }],
					userNames: [{ required: true, message: '请选择处理人员', trigger: 'change' }],
					businessNo: [{ required: true, message: '请输入营业执照号码', trigger: 'change' }],
					businessName:[{ required: true, message: '请输入店铺名称', trigger: 'change' }],
				},
				happenData: {
					tableName: 'case_inspection',
					status: 1
				},
				happenFile: []
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			openMap() {
				if (this.form.shopLatitude && this.form.shopLongitude) {
					const lat = parseFloat(this.form.shopLatitude)
					const lng = parseFloat(this.form.shopLongitude)
					Map.openMap(lat, lng, this.form.businessName, 'wgs84')
				} else {
					this.mToase('未获取店铺经纬度')
				}
			},
			handleTypeClick(idx) {
				const { dictValue, dictLabel } = this.typeList[idx]
				this.form = { ...this.form, policeType:dictValue, policeTypeName: dictLabel }
			},
			handlePoliceClick(idx){
				const { dictValue, dictLabel } = this.categoryList[idx]
				this.form = { ...this.form, policeCategory:dictValue, policeCategoryName: dictLabel }
			},
			handleTemporaryClick(idx){
				const { dictValue, dictLabel } = this.temporaryList[idx]
				this.form = { ...this.form, temporaryId:dictValue, temporaryName: dictLabel }
			},
			handleUnionClick(idx){
				const { dictValue, dictLabel } = this.unionList[idx]
				this.form = { ...this.form, unionId:dictValue, unionName: dictLabel }
			},
			handleChooseLocation() {
				uni.chooseLocation({
					latitude: 29.110764,
					longitude: 119.635857,
					success: res => {
						const { address, longitude, latitude } = res
						const lnglat = gps.gcj_decrypt(parseFloat(latitude), parseFloat(longitude))
						this.form = { ...this.form, address: "浙江省金华市婺城区城北街道" + address, longitude: lnglat.lng, latitude: lnglat.lat, lnglat: `${lnglat.lng}, ${lnglat.lat}` }
					},
					fail: () => {
						uni.showToast({
							title: '地图打开失败',
							icon: 'none',
							position: 'bottom'
						})
					}
				})
			},
			handleHpDateCon(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, happenDate: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleAssignDate(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, assignDate: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleChooseHandleUser(type) {
				let params = {}
				if (this.form[type]) {
					params.defaultCheckedKeys = this.form[type]
					params.defaultExpandedKeys = this.form[type]
				}
				this.$u.route({ url: 'pages/common/selectUser/selectUser', params })
			},
			setUserData(checks) {
				// 选择好人员后的回调
				const checkData = checks[0]
				if (checkData) this.form = { ...this.form, userNames: checkData.label, userIds: checkData.id }
			},
			handleRemove(index, lists) {
				const fileInfo = lists[index]
				const fileId = fileInfo.url.split('?id=')[1]
				if (fileId) {
						return new Promise((resolve, reject) => {
							uni.showModal({
								title: '提示',
								content: '删除后将无法恢复，是否确认删除？',
								success: ({ confirm }) => {
									if (confirm) {
										this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
									} else {
										reject()
									}
								}
							})
						})
				} else {
					return true
				}
			},
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				if (!isFail) {
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					this.$implement()
				}
			},
			handleError() {
				// 文件上传失败，弹出提示是否重新上传
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					content: '图片上传失败，是否重新上传？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading('图片上传中')
							this.$refs.happenfile.reUpload()
						} else {
							this.$implement({ immediately: true })
						}
					}
				})
			},
			handleSubmit() {
				let params = { ...this.form, userId: this.vuex_id, userName: this.vuex_nickName, status: 2, deptName: this.vuex_deptName, deptId: this.vuex_deptId }
				// 开始上传
				this.$loading('数据上传中')
				this.$u.api.inspectionEdit(params).then(res => {
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					this.$implement()
				}).catch((err) => {
					uni.hideLoading()
				})
			},
			handleEdit(state) {
				const params = { ...this.form,status:state}
				this.$refs.uForm.validate(valid => {
					if (valid) {
						// 图片验证，没有图片不通过验证
						if (!this.$refs.happenfile.lists.length) {
							this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
							return
						}
						// 开始上传
						this.$loading('数据上传中')
						this.$u.api.inspectionEdit(params).then(res => {
							// 遍历列表，查询是否有未上传的图片
							const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
							this.happenData.businessId = this.form.inspectionId
							console.log(uploadFile);
							if (uploadFile) {
								this.$loading('图片上传中')
								this.$refs.happenfile.upload()
							} else {
								uni.showToast({title: '操作成功'})
								uni.hideLoading()
								this.$implement()
							}
							this.$implement()
						}).catch(() => {
							uni.hideLoading()
						})
					}
				});
			}
		},
		onLoad(params) {
			console.log(params);
			if (params.id) {
				this.$loading()
				Promise.all([
					this.$u.api.getInspection({}, params.id),
					this.$u.api.getFileList({ tableName: 'case_inspection', businessId: params.id }),
					this.$u.api.dictList({dictType:"case_alert_type"}),//警情类别
					this.$u.api.dictList({dictType:"case_call_type"})//警情类型
				]).then(resAry => {
					const formData = resAry[0].data
					this.typeList = resAry[2].rows
					this.categoryList= resAry[3].rows
					this.categoryList.forEach(v=>{
						if(v.dictLabel) v.text = v.dictLabel
						if(v.dictValue) v.type = v.dictValue
					})
					this.typeList.forEach(v=>{
						if(v.dictLabel) v.text = v.dictLabel
						if(v.dictValue) v.type = v.dictValue
					})
					console.log(formData);
					if(!formData.shopId) this.objValue = 2
					else this.objValue = 1
					let typeTexts = this.typeList.find(item => item.dictValue == formData.policeType)
					let policeCategory = this.categoryList.find(item => item.dictValue == formData.policeCategory)
					const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
					this.form = { ...formData, policeTypeName:typeTexts? typeTexts.text:'',	policeCategoryName:policeCategory?policeCategory.text:'', lnglat }
					this.happenFile = resAry[1].rows.map(item => {
						return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
					})
					uni.hideLoading()
				}).catch((err) => {
					console.log(err);
					uni.hideLoading()
					uni.showToast({title: '加载失败', icon: 'error'})
				})
			} else {
				const timestamp = new Date().getTime()
				const happenDate = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = {sex:1,isProblem:0,isTemporary:0,isUnion:0, happenDate, userName: this.vuex_username, userId: this.vuex_id }
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

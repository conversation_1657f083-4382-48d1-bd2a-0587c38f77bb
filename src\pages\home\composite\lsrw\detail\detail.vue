<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="230" :label-style="labelStyle">
			<view class="p-lr-30">
				<!-- <u-form-item label="编码:" prop="code" >
					<u-input v-model="form.code" placeholder="请输入编码" />
				</u-form-item> -->
				<u-form-item label="负责人:" prop="userName" :border-bottom="false" required>
					<u-input v-model="form.userName" type="popup" placeholder="请选择负责人"/>
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('userId','user')">选择人员</u-button>
				</u-form-item>
				<u-form-item label="执行部门名称:" prop="assignDeptNames" :border-bottom="false" required>
					<u-input v-model="form.assignDeptNames" type="popup" placeholder="请选择发起部门名称"/>
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('assignDeptIds','unit')">选择部门</u-button>
				</u-form-item>
				<u-form-item label="店铺:" prop="assignShopNames" required>
					<u-input v-model="form.assignShopNames" type="popup" placeholder="请选择分配店铺"/>
					<u-button size="mini" type="primary" v-slot="right" @click="show = true">选择店铺</u-button>
				</u-form-item>
				<u-form-item label="指派时间:" prop="assignDate" required>
					<u-input v-model="form.assignDate" type="popup" placeholder="请选择指派时间" @click="showAssignDate = true"  />
					<u-icon name="calendar"  @click="showAssignDate = true"  size="40"></u-icon>
					<u-picker v-model="showAssignDate" mode="time" :params="params"  @confirm="handCaseTime"></u-picker>
				</u-form-item>
				<u-form-item label="类型:" prop="type" required>
					<u-radio-group v-model="form.type" >
						<u-radio :name="1">日常劝说</u-radio>
						<u-radio :name="2">非法上下客</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="内容:" prop="content"  label-position="top" :border-bottom="false">
					<u-input v-model="form.content" text="textarea" maxlength="300" height="140" placeholder="请输入内容" />
				</u-form-item>
			</view>
		</u-form>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex u-row-between">
			<view class="u-flex-1 u-m-l-20">
				<u-button type="primary" shape="circle" :custom-style="subStyle" @click="handleSubmit(2)">提 交</u-button>
			</view>
			<view class="u-flex-1 u-m-l-20">
				<u-button type="primary"  shape="circle" :custom-style="subStyle" @click="handleSubmit(9)">办 结</u-button>
			</view>
		</view>
		<!-- 提示 -->
		<u-popup v-model="show" length="60%" mode="bottom">
			<view class="u-p-30 u-p-b-80">
				<u-checkbox-group @change="checkboxGroupChange">
					<u-checkbox 
						v-model="item.checked" 
						v-for="(item, index) in list" :key="index" 
						:name="item.shopId"
						style="padding: 20rpx;border: 1px #c8c9cc solid;border-radius: 24rpx;margin-bottom: 24rpx;"
					>
						<view class="u-m-l-20" style="width: 560rpx">
							<view class="" >
								<strong>店铺名称：{{item.shopName}}</strong>
							</view>
							<view class="" >
								地址：{{item.address}}
							</view>
						</view>
					</u-checkbox>
				</u-checkbox-group>
			</view>
			<u-button class="btn-box1" @click = "handCheckbox(true)">确 定</u-button>
		</u-popup>
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				labelStyle: {color: '#808080',fontSize: '30rpx'},
				subStyle: {height: '86rpx',backgroundColor: '#327BF0'},
				form:{type:1},
				showAssignDate:false,
				rules: {
					assignDate: [{ required: true, message: '请输入指派时间', trigger: 'change' }],
					userName: [{ required: true, message: '请选择负责人', trigger: 'change' }],
					assignDeptNames: [{ required: true, message: '请选择执行部门', trigger: 'change' }],
				},
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true},
				list: [],
				show: false,
				checkbox : ''
			}
		},
		async onLoad(params){
			await this.$u.api.shopList().then(res=>{
				this.list = res.rows
				this.list.forEach(v=>{v.text = v.shopName})
			})
			this.$u.api.getTemporary({},params.id).then(res=>{
				this.form = res.data
				if(this.form.assignShopIds) this.handCheckbox(false)
			})
		},
		methods: {
			handCheckbox(is){
				if (is) this.form = {...this.form , assignShopIds : this.checkbox}
				let arr = this.form.assignShopIds.split(',')
				let arr2 = []
				this.list.forEach((v,i)=>{
					arr.forEach(v2=>{
						if (v.shopId == v2) {
							arr2.push(v.shopName)
						}
					})
				})
				this.form = {...this.form , assignShopNames:arr2.join(',')}
				this.show = false
			},
			checkboxGroupChange(e){
				console.log(e);
				this.checkbox = e.join(',')
			},
			handleSubmit(state) {
				if (state == 1) return this.send(state)
				this.$refs.uForm.validate(valid => {
					if (valid) {
						this.send(state)
					}
				});
			},
			send(state){
				const params = { ...this.form ,status:state}
				// 开始上传
				this.$loading('数据上传中')
				this.$u.api.temporaryEdit(params).then(res => {
						uni.showToast({title: '操作成功'})
						uni.hideLoading()
						this.$implement()
				}).catch((err) => {
					console.log(err);
					uni.hideLoading()
				})
			},
			handCaseTime(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, assignDate: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleChooseHandleUser(type,tity) {
				let params = {}
				if (this.form[type]) {
					params.defaultCheckedKeys = this.form[type]
					params.defaultExpandedKeys = this.form[type]
				}
				if(tity == 'unit') params = {...params, name: 'bumen', showRadio: 0}
				params.type = type
				this.$u.route({ url: 'pages/common/selectUser/selectUser', params })
			},
			setUserData(checks,type) {
				// 选择好人员后的回调
				let label = [] , id = []
				checks.forEach((v,i)=>{
					label.push(v.label)
					id.push(v.id)
				})
				label = label.join(',')
				id = id.join(',')
				if(type == 'assignDeptIds'){
					if (checks) this.form = { ...this.form, assignDeptNames: label, assignDeptIds: id }
				}else {
					if (checks) this.form = { ...this.form, userName: label, userId: id }
				}
			},
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style scoped>
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box1 {
	width: 90%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	left: 5%;
	background-color: #2979ff;
	border: 0;
	color: #fff;
	z-index: 10;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

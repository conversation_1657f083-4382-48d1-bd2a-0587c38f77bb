<template>
	<view>
		<view class="top u-flex u-flex-col u-row-between u-border-top u-border-bottom">
			<u-avatar :src="userInfo.avatar || avatar" :size="120" class="u-m-t-52" @click="previewAvatarImage"></u-avatar>
			<u-button type="primary" shape="circle" :custom-style="customStyle" class="u-m-b-50" @click="chooseAvatar">
				<u-icon name="edit-pen-fill"></u-icon>
				<text class="u-m-l-10">修改头像</text>
			</u-button>
		</view>

		<view class="container">
			<u-form :model="userInfo" ref="uForm" label-width="200" :label-style="labelStyle">
				<u-form-item label="用户名称">
					<u-input v-model="userInfo.userName" disabled />
				</u-form-item>
				<u-form-item label="用户昵称">
					<u-input v-model="userInfo.nickName" disabled />
				</u-form-item>
				<u-form-item label="手机号码">
					<u-input v-model="userInfo.phonenumber" disabled placeholder="暂无手机号码"/>
				</u-form-item>
				<u-form-item label="用户邮箱">
					<u-input v-model="userInfo.email" disabled placeholder="暂无邮箱"/>
				</u-form-item>
				<u-form-item label="所属部门">
					<u-input v-model="userInfo.deptName" disabled placeholder="暂无所属部门"/>
				</u-form-item>
				<u-form-item label="所属角色" :border-bottom="false">
					<u-input v-model="userInfo.roleGroup" disabled placeholder="暂无所属角色"/>
				</u-form-item>
			</u-form>
		</view>
	</view>
</template>

<script>
	import avatar from '@/static/img/avatar.png'
	export default {
		data() {
			return {
				customStyle: {
					background: '#4689F5',
					width: '160rpx',
					height: '48rpx',
					fontSize: '24rpx'
				},
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				avatar: avatar,
				userInfo: {}
			}
		},
		methods: {
			fetchData() {
				this.$loading()
				this.$u.api.getUserProfile().then(res => {
					uni.hideLoading()
					if (res.data.avatar) res.data.avatar = `${this.vuex_ip}/zqzfj${res.data.avatar}`
					if (res.data.dept && res.data.dept.deptName) res.data.deptName = res.data.dept.deptName
					this.userInfo = res.data
				}).catch(() => {
					uni.hideLoading()
				})
			},
			previewAvatarImage() {
				uni.previewImage({
					current: 0,
					urls: [this.userInfo.avatar || this.avatar]
				})
			},
			chooseAvatar() {
				// 此为uView的跳转方法，详见"文档-JS"部分，也可以用uni的uni.navigateTo
				this.$u.route({
					// 关于此路径，请见下方"注意事项"
					url: '/uview-ui/components/u-avatar-cropper/u-avatar-cropper',
					// 内部已设置以下默认参数值，可不传这些参数
					params: {
						// 输出图片宽度，高等于宽，单位px
						destWidth: 300,
						// 裁剪框宽度，高等于宽，单位px
						rectWidth: 300,
						// 输出的图片类型，如果'png'类型发现裁剪的图片太大，改成"jpg"即可
						fileType: 'jpg',
					}
				})
			}
		},
		created() {
			uni.$on('uAvatarCropper', path => {
				// 可以在此上传到服务端
				this.$loading('上传图片中...')
				uni.uploadFile({
					url: `${this.vuex_ip}/zqzfj/system/user/profile/avatar`,
					header: {
						Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
					},
					filePath: path,
					name: 'avatarfile',
					complete: (res) => {
						console.log(res);
						uni.hideLoading()
						if (res.statusCode == 200) {
							try{
								const resData = JSON.parse(res.data)
								if (resData && resData.code == 200) {
									this.userInfo.avatar = path;
									uni.showToast({
										title: '操作成功'
									})
								} else {
									this.mToase('图片上传失败')
								}
							}catch(e){
								//TODO handle the exception
								this.mToase('图片上传失败')
							}
						} else {
							this.mToase('图片上传失败')
						}
					}
				});
			})
		},
		onLoad() {
			this.fetchData()
		}
	}
</script>

<style lang="scss">
.top {
	height: 300rpx;
	background: #fff;
}
.container {
	background-color: #FFFFFF;
	padding: 0 30rpx;
}
</style>

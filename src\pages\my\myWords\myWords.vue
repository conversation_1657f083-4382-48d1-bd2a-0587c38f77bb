<template>
	<view>
		<!-- 搜索 -->
		<view class="top-search u-flex u-col-center u-border-top ">
			<view class="top-search-left u-flex u-flex-1">
				<u-image src="@/static/img/top-search.png" width="35rpx" height="35rpx"></u-image>
				<u-input v-model="searchValue" type="text" class="u-flex-1 u-m-l-20"></u-input>
			</view>
			<view class="top-search-right">
				<view class="top-search-right-btn u-flex u-row-center u-col-center" @click="handleSearch">
					<u-image src="@/static/img/filter.png" width="28rpx" height="28rpx" class="img"></u-image>
					<text>筛选</text>
				</view>
			</view>
		</view>
		
		<view class="container">
			<u-cell-group>
				<u-cell-item :title="item.words" :arrow="false" v-for="(item,index) in dataList" :key="index" @click="handleCellClick(item.words)">
					<view slot="right-icon" style="width: 200rpx;">
						<u-button type="primary" size="mini" class="u-m-r-20" @click.stop="handleEdit(item)">编辑</u-button>
						<u-button type="primary" size="mini" @click.stop="handleDelete(item.id)">删除</u-button>
					</view>
				</u-cell-item>
			</u-cell-group>
		</view>
		<!-- 选项 -->
		<u-popup v-model="showPopup" mode="bottom" closeable>
			<u-form :model="form" ref="popForm" class="u-p-24" label-position="top" label-width="200" :label-style="labelStyle">
				<u-form-item label="惯用语" :border-bottom="false">
					<u-input type="textarea" maxlength="300" height="140" v-model="form.words" />
				</u-form-item>
				<view class="u-flex u-row-between u-m-t-24">
					<u-button type="primary" shape="circle" @click="handleSubmit" class="btn u-flex-1 u-m-t-24">确定</u-button>
				</view>
			</u-form>
		</u-popup>
		<!-- 操作按钮 -->
		<view class="bottom-btn u-flex u-row-center u-col-center" @click="handleAdd">
			<u-image src="@/static/img/btn-add-icon.png" width="33rpx" height="33rpx"></u-image>
			<text class="text">新增惯用语</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				searchValue: '',
				dataList:[],
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				form: {},
				showPopup: false
			}
		},
		watch: {
			searchValue(nVal) {
				if (nVal.trim() == '') {
					this.fetchData()
				}
			}
		},
		mounted() {
			this.fetchData()
		},
		methods: {
			fetchData() {
				this.$loading()
				let params = { userId: this.vuex_id }
				if (this.searchValue.trim() != '') params.searchValue = this.searchValue
				this.$u.api.getWordsList(params).then(res => {
					this.dataList = res.rows
					uni.hideLoading()
				}).catch(() => {
					uni.hideLoading()
				})
			},
			handleSearch() {
				this.fetchData()
			},
			handleSubmit() {
				this.$loading()
				const subFn = this.form.id ? this.$u.api.editWords : this.$u.api.addWords
				let params = { userId: this.vuex_id, words: this.form.words }
				if (this.form.id) params.id = this.form.id
				subFn(params).then(res => {
					this.showPopup = false
					this.fetchData()
					uni.hideLoading()
				}).catch(() => {
					uni.hideLoading()
				})
			},
			handleCellClick(words) {
				uni.setClipboardData({
					data: words,
					success: function () {
						this.mToase('复制成功')
					}
				});
			},
			handleEdit(item) {
				this.showPopup = true
				this.form = {...item}
			},
			handleDelete(id) {
				uni.showModal({
					title: '提示',
					content: '是否确认删除？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading()
							this.$u.api.removeWords({}, id).then(res => {
								this.fetchData()
								uni.hideLoading()
							}).catch(() => {
								uni.hideLoading()
							})
						}
					}
				})
			},
			handleAdd() {
				this.showPopup = true
				this.form = {}
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #fff;
}
.container {
	margin-top: 103rpx;
	padding-bottom: 30rpx;
}
// 按钮
.bottom-btn {
	width: 220rpx;
	height: 88rpx;
	position: fixed;
	bottom: 40rpx;
	left: 50%;
	margin-left: -110rpx;
	background-color: #327BF0;
	color: #FFFFFF;
	border-radius: 88rpx;
	box-shadow: 0px 0px 14px 0px rgba(82, 97, 121, 0.3);
	transition: all 0.5s;
	z-index: 10;
	&:active {
		background-color: #73a6f7;
	}
	.text {
		font-size: 28rpx;
		margin-left: 15rpx;
	}
}
.top-search {
	width: 100%;
	height: 103rpx;
	background-color: #fff;
	padding: 0 30rpx;
	// box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1);
	position: fixed;
	top: 0;
	/* #ifdef H5 */
	top: 44px;
	/* #endif */
	z-index: 10;
	&-left {
		height: 68rpx;
		background-color: #F5F5F5;
		border-radius: 68rpx;
		padding: 0 20rpx;
		margin-right: 20rpx;
	}
	&-right {
		&-btn {
			width: 136rpx;
			height: 68rpx;
			border-radius: 68rpx;
			background-color: #327BF0;
			color: #fff;
			font-size: 28rpx;
			transition: all 0.5s;
			.img {
				margin-right: 10rpx;
			}
			&:active {
				opacity: 0.3;
			}
		}
	}
}
</style>

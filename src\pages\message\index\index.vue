<template>
	<view>
		<view class="top u-border-bottom">
			<view class="status_bar"></view>
			<view class="content u-flex u-row-between">
				<text>消息</text>
				<view class="btn" @click="handleMsgAll"></view>
			</view>
		</view>
		<view class="msg-list">
			<view class="msg-list-item u-flex u-row-between u-col-top" v-for="(item, idx) in dataList" :key="`${item.messageId}-${idx}`">
				<u-image :src="iconList[item.type]" width="68rpx" height="68rpx" style="flex-shrink: 0;"></u-image>
				<view class="content">
					<view class="date">
						<view class="circle" v-if="!item.isRead"></view>
						<text class="u-m-l-15">{{ typeof item.createTime == 'number' ? $u.timeFormat(item.createTime, 'yyyy-mm-dd hh:MM:ss') : item.createTime }}</text>
					</view>
					<view class="msg" @click="handleOpen(item)">
						<view class="arrow"></view>
						<text class="msg-title">{{ item.messageTitle }}</text>
						<text>{{ item. messageContent }}</text>
					</view>
				</view>
			</view>
			<u-loadmore :status="status" class="u-m-t-30" />
		</view>
	</view>
</template>

<script>
	import swytIcon from '@/static/img/swyt1_icon.png'
	import jkzpIcon from '@/static/img/jkzp_icon.png'
	import wrfbIcon from '@/static/img/xcfx_icon.png'
	import ldjbIcon from '@/static/img/ldjb_icon.png'
	// import dpxcIcon from '@/static/img/xcfx_icon.png'
	import dcssIcon from '@/static/img/dckh1_icon.png'
	import dckhIcon from '@/static/img/dckh1_icon.png'
	import xfIcon from '@/static/img/swyt1_icon.png'
	
	export default {
		data() {
			return {
				pageNum: 1,
				pageSize: 10,
				dataList: [],
				status: 'loading',
				iconList: {
					1: swytIcon, // 四位一体
					2: jkzpIcon, // 监控抓拍
					3: wrfbIcon,  // 任务发布
					4: ldjbIcon,  // 领导交办
					// 5: dpxcIcon,  // 店铺巡查（临时任务）
					6: jkzpIcon,  // 智能抓拍
					7: dcssIcon,  // 督查申述
					8: dckhIcon,  // 督查考核
					9: xfIcon  // 信访
				}
			}
		},
		methods: {
			fetchData() {
				const { pageNum, pageSize } = this
				let params = { pageNum, pageSize, userId: this.vuex_id }
				this.status = 'loading'
				this.$u.api.getMsgList(params).then(res => {
					if (pageNum == 1) {
						this.dataList = res.rows
						uni.stopPullDownRefresh()
					} else {
						this.dataList = this.dataList.concat(res.rows)
					}
					// this.dataList.unshift({
					// 	caseId: 1205,
					// 	createTime: "2022-03-23 15:14:59",
					// 	isRead: 0,
					// 	messageContent: "编号：jkzp202203230360内容：出租车诱导",
					// 	messageId: 1488,
					// 	messageTitle: "新出租车诱导通知",
					// 	params: {},
					// 	type: "2",
					// 	userId: 11
					// })
					this.status = res.rows.length < 10 ? 'nomore' : 'loadmore'
				}).catch(err => {
					uni.stopPullDownRefresh()
					this.status = 'loadmore'
				})
			},
			handleMsgAll() {
				uni.showModal({
					title: '提示',
					content: '是否将所有消息更改为已读？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading()
							this.$u.api.editAllMsg({ userId: this.vuex_id }).then(() => {
								this.dataList = this.dataList.map(item => {
									item.isRead = 1
									return item
								})
								this.$u.vuex('vuex_msg_count.message', 0)
								uni.removeTabBarBadge({ index: 2 })
								uni.hideLoading()
							}).catch(() => {
								uni.hideLoading()
							})
						}
					}
				})
			},
			handleOpen(info) {
				this.$loading()
				this.$u.api.editMsg({ messageId: info.messageId, isRead: 1 }).then(() => {
					// 设置数据角标
					if (!info.isRead) {
						const message = this.vuex_msg_count.message - 1
						this.$u.vuex('vuex_msg_count.message', message)
						if (message > 0) {
							uni.setTabBarBadge({ index: 2, text: `${message}` })
						} else {
							uni.removeTabBarBadge({ index: 2 })
						}
						info.isRead = 1
					}
					if (info.type == 1) {
						// 四位一体
						this.openSwyt(info.caseId)
					} else if (info.type == 2) {
						/* 监控抓拍 */
						this.openJkzp(info)
					} else if (info.type == 3) {
						/* 任务发布 */
						this.$u.route({ url: 'pages/home/<USER>/zfdc/detail/detail', params: { id: info.caseId } })
					} else if (info.type == 4) {
						/* 领导交办 */
						this.$u.route({ url: 'pages/home/<USER>/ldjb/detail/detail', params: { id: info.caseId } })
					} 
					// else if (info.type == 5) {
					// 	/* 店铺巡查 */
					// 	// this.openDpxc(info)
					// 	this.$u.route({ url: 'pages/home/<USER>/xcfx/list/list', params: { id: info.caseId, current:1 } })
					// } 
					else if (info.type == 6) {
						/* 智能抓拍 */
						this.openZnzp(info)
					} else if (info.type == 7) {
						/* 督查申述 */
						this.openDcss(info)
					} else if (info.type == 8) {
						/* 督查考核 */
						this.openDckh(info)
					} 
					// else if (info.type == 9) {
					// 	/* 信访 */
					// 	this.mToase('111')
					// }
					 else {
						uni.hideLoading()
					}
				}).catch(() => {
					uni.hideLoading()
				})
			},
			openSwyt(id) {
				this.$u.api.getCaseDetail({}, id).then(res => {
					uni.hideLoading()
					if (res.data) {
						const { userId, handleUserId, status, fourId }  = res.data
						if (handleUserId == this.vuex_id && (status == 0 || status == 1)) {
							this.$u.route({ url: 'pages/home/<USER>/swyt/detail/detail', params: { id: fourId } })
						} else if (userId == this.vuex_id && status == 1) {
							this.$u.route({ url: 'pages/home/<USER>/swyt/add/add', params: { id: fourId } })
						} else {
							this.$u.route({ url: 'pages/home/<USER>/swyt/his/his', params: { id: fourId } })
						}
					} else {
						this.mToase('案件不存在或已被删除')
					}
				}).catch(() => {
					uni.hideLoading()
				})
			},
			openJkzp(item) {
				this.$u.api.getcapture({}, item.caseId).then(res => {
					uni.hideLoading()
					if (res.data) {
						const { status, squadronUserId } = res.data
						if (status == 3 || (status == 4 && squadronUserId == this.vuex_id)) {
							this.$u.route({ url: 'pages/home/<USER>/jkzp/add/add', params: { id: item.caseId } })
						} else if (status == 9) {
							this.$u.route({ url: 'pages/home/<USER>/jkzp/his/his', params: { id: item.caseId } })
						} else {
							this.$u.route({ url: 'pages/home/<USER>/jkzp/detail/detail', params: { id: item.caseId } })
						}
					} else {
						this.mToase('案件不存在或已被删除')
					}
				}).catch(() => {
					uni.hideLoading()
				})
			},
			openZnzp(item) {
				this.$u.api.getcapture({}, item.caseId).then(res => {
					uni.hideLoading()
					if (res.data) {
						const { status, squadronUserId } = res.data
						if (status == 3 || (status == 4 && squadronUserId == this.vuex_id)) {
							this.$u.route({ url: 'pages/home/<USER>/znzp/add/add', params: { id: item.caseId } })
						} else if (status == 9) {
							this.$u.route({ url: 'pages/home/<USER>/znzp/his/his', params: { id: item.caseId } })
						} else {
							this.$u.route({ url: 'pages/home/<USER>/znzp/detail/detail', params: { id: item.caseId } })
						}
					} else {
						this.mToase('案件不存在或已被删除')
					}
				}).catch(() => {
					uni.hideLoading()
				})
			},
			/* 店铺巡查 */
			/* openDpxc(item){
				this.$u.api.getInspection({}, item.caseId).then(res => {
					uni.hideLoading()
					if (res.data) {
						const { status, isTemporary } = res.data
						if (status == 9) {
							this.$u.route({ url: 'pages/home/<USER>/xcfx/his/his', params: { id: item.inspectionId } })
						} else if (status == 1 && isTemporary == 1) {
							this.$u.route({ url: 'pages/home/<USER>/xcfx/getIns/getIns', params: { id: item.inspectionId } })
						} else {
							this.$u.route({ url: 'pages/home/<USER>/xcfx/detail/detail', params: { id: item.inspectionId } })
						}
					} else {
						this.mToase('案件不存在或已被删除')
					}
				}).catch(() => {
					uni.hideLoading()
				})
			}, */
			/* 督查申述 */
			openDcss(item){
				console.log(item)
				this.$u.api.getAppeal({}, item.caseId).then(res=>{
					uni.hideLoading()
					this.$u.route({ url: 'pages/home/<USER>/dckh/appeal/appeal', params: { id: item.caseId,checkRecordId:res.data.checkRecordId } })
				}).catch(() => {
					uni.hideLoading()
				})
			},
			/* 督查考核 */
			openDckh(item){
				this.$u.api.getStandard({}, item.caseId).then(res => {
					uni.hideLoading()
					if (res.data) {
						const { status } = res.data
						if(status == 1){
							this.$u.route({ url: 'pages/home/<USER>/dckh/add/add', params: { id: item.caseId } })
						}else if (status != 9) {
							this.$u.route({ url: 'pages/home/<USER>/dckh/detail/detail', params: { id: item.caseId } })
						} else {
							this.$u.route({ url: 'pages/home/<USER>/dckh/his/his', params: { id: item.caseId } })
						}
					} else {
						this.mToase('案件不存在或已被删除')
					}
				}).catch(() => {
					uni.hideLoading()
				})
			},
			setNewMsg() {
				uni.$on('setMsg', (data) => {
					// console.log(data)
					// this.dataList.unshift(data)
					this.pageNum = 1
					this.fetchData()
				})
			}
		},
		onLoad() {
			this.fetchData()
			this.setNewMsg()
		},
		onPullDownRefresh() {
			this.pageNum = 1
			this.fetchData()
		},
		onReachBottom() {
			if (this.status == 'loadmore') {
				this.pageNum++
				this.fetchData()
			}
		}
	}
</script>

<style lang="scss">
.top {
	width: 100%;
	background-color: #FFFFFF;
	padding: 0 30rpx;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 10;
	.content {
		height: 98rpx;
		font-size: 40rpx;
		font-weight: 700;
		padding-top: 23rpx;
		padding-bottom: 25rpx;
		.btn {
			width: 48rpx;
			height: 48rpx;
			background: url(../../../static/img/clear-msg.png) no-repeat center center / contain;
		}
	}
}
.msg-list {
	padding-top: calc(98rpx + var(--status-bar-height));
	padding-bottom: 30rpx;
	.msg-list-item {
		padding: 30rpx 30rpx 0 30rpx;
		.content {
			margin-left: 10rpx;
			flex: 1;
			.date {
				color: #666666;
				font-size: 28rpx;
				line-height: 40rpx;
				margin-bottom: 10rpx;
				.circle {
					width: 12rpx;
					height: 12rpx;
					background-color: #E02020;
					border-radius: 50%;
					display: inline-block;
					vertical-align: middle;
					margin-left: 15rpx;
				}
			}
			.msg {
				font-size: 26rpx;
				color: #808080;
				background-color: #FFFFFF;
				border-radius: 12rpx;
				padding: 30rpx;
				margin-left: 20rpx;
				position: relative;
				transition: opacity 0.3s;
				.msg-title {
					color: #333333;
					font-size: 32rpx;
					font-weight: 700;
					display: block;
					margin-bottom: 18rpx;
				}
				.arrow {
					width: 0;
					height: 0;
					border: 20rpx solid transparent;
					border-top-color: #fff;
					position: absolute;
					top: 0;
					left: -20rpx;
				}
				&:active {
					opacity: 0.3;
				}
			}
		}
	}
}
</style>

<template>
	<view>
		<!-- <top-supervise :caseId="form.fourId" caseType="four" :status="form.status" /> -->
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<!-- <u-form-item label="标题:" prop="title" required>
					<u-input v-model="form.title" placeholder="请输入标题" />
				</u-form-item> -->
				<u-form-item label="类型:" prop="typeText" required>
					<u-input v-model="form.typeText" type="select" :select-open="showList" placeholder="请选择类型" @click="showList = true" />
					<u-action-sheet v-model="showList" :list="typeList" @click="handleTypeClick"></u-action-sheet>
				</u-form-item>
				<u-form-item label="发起人员:" prop="userName" required>
					<u-input v-model="form.userName" type="text" disabled placeholder="请选择发起人员"  />
				</u-form-item>
				<u-form-item label="联系电话:" prop="phone" required>
					<u-input v-model="form.phone" placeholder="请输入发起人员联系电话" />
				</u-form-item>
				<u-form-item label="发生时间:" prop="happenDate" required>
					<u-input v-model="form.happenDate" type="popup" placeholder="请选择发生时间" @click="showHappenDate = true"  />
					<u-picker v-model="showHappenDate" mode="time" :params="params" :default-time="form.happenDate" @confirm="handleHpDateCon"></u-picker>
				</u-form-item>
				<u-form-item label="发生地址:" prop="address" required>
					<u-input v-model="form.address" type="text" placeholder="请选择地址" />
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseLocation">选择地址</u-button>
				</u-form-item>
				<u-form-item label="经纬度:" prop="lnglat" :border-bottom="false" required>
					<u-input v-model="form.lnglat" type="popup" disabled  placeholder="请选择地址"/>
				</u-form-item>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="内容描述:" prop="content" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.content" type="textarea" maxlength="300" height="140" placeholder="请输入内容描述..."/>
				</u-form-item>
				<u-upload
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
					:size-type="['compressed']"
					:file-list="happenFile"
					:before-remove="handleRemove"
					@on-uploaded="handleAllUpload"
					@on-error="handleError"
				></u-upload>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="处理人员:" prop="handleUserName" :border-bottom="false" required>
					<u-input v-model="form.handleUserName" type="popup" placeholder="请选择处理人员"/>
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser">选择人员</u-button>
				</u-form-item>
			</view>
		</u-form>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top">
			<u-button v-if="!form.fourId" type="primary" shape="circle" :custom-style="subStyle" @click="handleSubmit">提交</u-button>
			<u-button v-else type="primary" shape="circle" :custom-style="subStyle" @click="handleEdit">提交</u-button>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	// import topSupervise from '@/components/top-supervise.vue'
	import gps from '@/common/gps.js'

	export default {
		// components: {
		// 	topSupervise
		// },
		data() {
			return {
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				showList: false,
				typeList: [
          { type: 3, text: '市政道路' },
          { type: 4, text: '园林' },
					{ type: 1, text: '绿化' },
					{ type: 2, text: '环卫保洁' }
				],
				showHappenDate: false,
				form: {address:""},
				rules: {
					// title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
					typeText: [{ required: true, message: '请选择类型', trigger: 'change' }],
					userName: [{ required: true, message: '请选择发起人员', trigger: 'change' }],
					// phone: [
					// 	{ required: true, message: '请输入发起人员联系电话', trigger: 'blur' },
					// 	{
					// 		validator: (rule, value, callback) => {
					// 			return this.$u.test.mobile(value);
					// 		},
					// 		message: '手机号码不正确',
					// 		trigger: ['change','blur'],
					// 	}
					// ],
					happenDate: [{ required: true, message: '请选择发生时间', trigger: 'change' }],
					address: [{ required: true, message: '请选择地址', trigger: 'change' }],
					lnglat: [{ required: true, message: '请选择地址', trigger: 'change' }],
					content: [{ required: true, message: '请输入内容描述', trigger: 'blur' }],
					handleUserName: [{ required: true, message: '请选择处理人员', trigger: 'change' }]
				},
				happenData: {
					tableName: 'case_four_in_one',
					status: 1
				},
				happenFile: []
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			handleTypeClick(idx) {
				const { type, text } = this.typeList[idx]
				this.form = { ...this.form, type, typeText: text }
			},
			handleChooseLocation() {
				uni.chooseLocation({
					latitude: 29.110764,
					longitude: 119.635857,
					success: res => {
						const { address, longitude, latitude } = res
						const lnglat = gps.gcj_decrypt(parseFloat(latitude), parseFloat(longitude))
						this.form = { ...this.form, address: "浙江省金华市婺城区城北街道" + address, longitude: lnglat.lng, latitude: lnglat.lat, lnglat: `${lnglat.lng}, ${lnglat.lat}` }
					},
					fail: () => {
						uni.showToast({
							title: '地图打开失败',
							icon: 'none',
							position: 'bottom'
						})
					}
				})
			},
			handleHpDateCon(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, happenDate: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleChooseHandleUser() {
				let params = {}
				if (this.form.handleUserId) {
					params.defaultCheckedKeys = this.form.handleUserId
					params.defaultExpandedKeys = this.form.handleUserId
				}
				this.$u.route({ url: 'pages/common/selectUser/selectUser', params })
			},
			setUserData(checks) {
				// 选择好人员后的回调
				const checkData = checks[0]
				if (checkData) this.form = { ...this.form, handleUserName: checkData.label, handleUserId: checkData.id }
			},
			handleRemove(index, lists) {
				const fileInfo = lists[index]
				const fileId = fileInfo.url.split('?id=')[1]
				if (fileId) {
						return new Promise((resolve, reject) => {
							uni.showModal({
								title: '提示',
								content: '删除后将无法恢复，是否确认删除？',
								success: ({ confirm }) => {
									if (confirm) {
										this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
									} else {
										reject()
									}
								}
							})
						})
				} else {
					return true
				}
			},
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				if (!isFail) {
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					this.$implement()
				}
			},
			handleError() {
				// 文件上传失败，弹出提示是否重新上传
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					content: '图片上传失败，是否重新上传？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading('图片上传中')
							this.$refs.happenfile.reUpload()
						} else {
							this.$implement({ immediately: true })
						}
					}
				})
			},
			handleSubmit() {
				const params = { ...this.form, status: 1 }
				this.$refs.uForm.validate(valid => {
					if (valid) {
						// 图片验证，没有图片不通过验证
						if (!this.$refs.happenfile.lists.length) {
							this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
							return
						}
						// 开始上传
						this.$loading('数据上传中')
						this.$u.api.caseAdd(params).then(res => {
							// 遍历列表，查询是否有未上传的图片
							const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
							this.happenData.businessId = res.data.fourId
							if (uploadFile) {
								this.$loading('图片上传中')
								this.$refs.happenfile.upload()
							} else {
								uni.showToast({title: '操作成功'})
								uni.hideLoading()
								this.$implement()
							}
						}).catch(() => {
							uni.hideLoading()
						})
					}
				});
			},
			handleEdit() {
				const params = { ...this.form, status: 1 }
				this.$refs.uForm.validate(valid => {
					if (valid) {
						// 图片验证，没有图片不通过验证
						if (!this.$refs.happenfile.lists.length) {
							this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
							return
						}
						// 开始上传
						this.$loading('数据上传中')
						this.$u.api.caseEdit(params).then(res => {
							// 遍历列表，查询是否有未上传的图片
							const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
							this.happenData.businessId = this.form.fourId
							if (uploadFile) {
								this.$loading('图片上传中')
								this.$refs.happenfile.upload()
							} else {
								uni.showToast({title: '操作成功'})
								uni.hideLoading()
								this.$implement()
							}
						}).catch(() => {
							uni.hideLoading()
						})
					}
				});
			}
		},
		onLoad(params) {
			if (params.id) {
				this.$loading()
				Promise.all([
					this.$u.api.getCaseDetail({}, params.id),
					this.$u.api.getFileList({ tableName: 'case_four_in_one', status: 1, businessId: params.id })
				]).then(resAry => {
					const formData = resAry[0].data
					const typeText = this.typeList.find(item => item.type == formData.type)
					const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
					this.form = { ...formData, typeText: typeText ? typeText.text : '', lnglat }
					this.happenFile = resAry[1].rows.map(item => {
						return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
					})
					uni.hideLoading()
				}).catch(() => {
					uni.hideLoading()
				})
			} else {
				const timestamp = new Date().getTime()
				const happenDate = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = { happenDate, userName: this.vuex_nickName, userId: this.vuex_id, phone: this.vuex_phone }
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

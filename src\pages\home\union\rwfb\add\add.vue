<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="任务名称:" prop="title" required>
					<u-input v-model="form.title" placeholder="请输入任务名称" />
				</u-form-item>
				<!-- <u-form-item label="联合执法类型:" prop="typeName" required>
					<u-input v-model="form.typeName" type="select" :select-open="showTypeList" placeholder="请选择联合执法类型" @click="showTypeList = true" />
					<u-action-sheet v-model="showTypeList" :list="typeList" @click="handleTypeClick"></u-action-sheet>
				</u-form-item> -->
				<!-- <u-form-item label="发起人员:" prop="launchName" required>
					<u-input v-model="form.launchName" type="popup" placeholder="请选择发起人员"/>
				</u-form-item> -->
				<!-- <u-form-item label="发起部门名称:" prop="launchDeptName" required>
					<u-input v-model="form.launchDeptName" type="popup" placeholder="请选择发起部门名称"/>
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('launchDeptId','unit')">选择部门</u-button>
				</u-form-item> -->
				<u-form-item label="负责人:" prop="userName" required>
					<u-input v-model="form.userName" type="popup" placeholder="请选择负责人"/>
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('userId','user')">选择人员</u-button>
				</u-form-item>
				<u-form-item label="执行部门:" prop="deptNames" required>
					<u-input v-model="form.deptNames" type="popup" placeholder="请选择发起部门与人员"/>
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseUser('userIds')">选择部门人员</u-button>
				</u-form-item>
				<u-form-item label="执行人员:" prop="userNames" required>
					<u-input v-model="form.userNames" type="popup" placeholder="请选择发起部门名称"/>
					<!-- <u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('deptIds','unit')">选择部门</u-button> -->
				</u-form-item>
				<u-form-item label="开始时间:" prop="startTime" required>
					<u-input v-model="form.startTime" type="popup" placeholder="请选择开始时间" @click="showStartTime = true"  />
					<u-icon name="calendar"  @click="showStartTime = true"  size="40"></u-icon>
					<u-picker v-model="showStartTime" mode="time" :params="params"  @confirm="handStartTime"></u-picker>
				</u-form-item>
				<u-form-item label="结束时间:" prop="endTime" border-bottom="false" required>
					<u-input v-model="form.endTime" type="popup" placeholder="请选择结束时间" @click="showEndTime = true"  />
					<u-icon name="calendar"  @click="showEndTime = true"  size="40"></u-icon>
					<u-picker v-model="showEndTime" mode="time" :params="params"  @confirm="handEndTime"></u-picker>
				</u-form-item>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="任务内容:" prop="content" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.content" type="textarea" maxlength="300" height="140" placeholder="请输入任务内容..."/>
				</u-form-item>
				<u-upload
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					upload-text="任务方案"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
					:size-type="['compressed']"
					:file-list="happenFile"
					:before-remove="handleRemove"
					@on-uploaded="handleAllUpload"
					@on-error="handleError"
				></u-upload>
			</view>
		</u-form>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex u-row-between">
			<view class="u-flex-1">
				<u-button type="primary" shape="circle" :custom-style="subStyle" @click="handleSubmit(1)">暂存</u-button>
			</view>
			<view class="u-flex-1 u-m-l-20">
				<u-button type="primary" shape="circle" :custom-style="subStyle" @click="handleSubmit(2)">发布</u-button>
			</view>
			<!-- <view class="u-flex-1 u-m-l-20">
				<u-button type="primary"  shape="circle" :custom-style="subStyle" @click="handleSubmit(9)">办结</u-button>
			</view> -->
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				form:{},
				showStartTime:false,
				showEndTime:false,
				showTypeList:false,
				typeList:[],
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				happenData: {
					tableName: 'case_union',
					status: 2
				},
				happenFile: [],
				rules: {
					title: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
					typeName: [{ required: true, message: '请选择类型', trigger: 'change' }],
					launchName: [{ required: true, message: '请选择发起人员', trigger: 'change' }],
					launchDeptName: [{ required: true, message: '请选择发起部门名称', trigger: 'change' }],
					userName: [{ required: true, message: '请选择发起人员', trigger: 'change' }],
					deptNames: [{ required: true, message: '请选择发起部门名称', trigger: 'change' }],
					startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
					endTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
					content: [{ required: true, message: '请输入任务内容', trigger: 'blur' }],
				},
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			handleSubmit(state) {
				if (state == 1) return this.send(state)
				this.$refs.uForm.validate(valid => {
					if (valid) {
						// 图片验证，没有图片不通过验证
						if (!this.$refs.happenfile.lists.length) {
							this.$refs.uTips.show({ title: '请上传任务方案图片', type: 'error', duration: '2300' })
							return
						}
						this.send(state)
					}
				});
			},
			send(state){
				const params = { ...this.form ,status:state}
				// 开始上传
				this.$loading('数据上传中')
				this.$u.api.unionAdd(params).then(res => {
					console.log(res);
					// 遍历列表，查询是否有未上传的图片
					const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
					this.happenData.businessId = res.data.unionId
					if (uploadFile) {
						this.$loading('图片上传中')
						this.$refs.happenfile.upload()
					} else {
						uni.showToast({title: '操作成功'})
						uni.hideLoading()
						this.$implement()
					}
				}).catch((err) => {
					console.log(err);
					uni.hideLoading()
				})
			},
			handleTypeClick(idx) {
				const { dictSort, dictLabel } = this.typeList[idx]
				this.form = { ...this.form, type:dictSort, typeName: dictLabel }
			},
			handStartTime(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, startTime: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handEndTime(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, endTime: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleChooseUser(type) {
				let params = {}
				if (this.form[type]) {
					params.defaultCheckedKeys = this.form[type]
					params.defaultExpandedKeys = this.form[type]
				}
				this.$u.route({ url: 'pages/common/selectDeptUser/selectDeptUser', params })
			},
			handleChooseHandleUser(type,tity) {
				let params = {}
				if (this.form[type]) {
					params.defaultCheckedKeys = this.form[type]
					params.defaultExpandedKeys = this.form[type]
				}
				if(tity == 'unit')	{
					params.name = 'bumen'
					params.showRadio = 0
				}

				params.type = type
				this.$u.route({ url: 'pages/common/selectUser/selectUser', params })
			},
			setDeptUserData(checkList, type) {
				let userNameList = [], userIdList = [], deptNameList = [], deptIdList = []
				checkList.forEach(item => {
					if (item.type === 'd') {
						deptNameList.push(item.label)
						deptIdList.push(item.id)
					} else if (item.type === 'u') {
						userNameList.push(item.label)
						userIdList.push(item.id)
					}
				})
				this.form = {
					...this.form,
					deptNames: deptNameList.join(','),
					userNames: userNameList.join(','),
					deptIds: deptIdList.join(','),
					userIds: userIdList.join(',')
				}
			},
			setUserData(checks,type) {
				// 选择好人员后的回调
				const checkData = checks[0]
				console.log(type);
				if(type == 'launchId'){
					if (checkData) this.form = { ...this.form, launchName: checkData.label, launchId: checkData.id }
				}else if(type == 'launchDeptId'){
					if (checkData) this.form = { ...this.form, launchDeptName: checkData.label, launchDeptId: checkData.id }
				}else if(type == 'deptIds'){
					let deptNames = '', deptIds = ''
					checks.forEach(item => {
						deptNames += `,${item.label}`
						deptIds += `,${item.id}`
					})
					this.form = { ...this.form, deptNames: deptNames.slice(1), deptIds: deptIds.slice(1) }
				}else {
					if (checkData) this.form = { ...this.form, userName: checkData.label, userId: checkData.id }
				}
			},
			handleRemove(index, lists) {
				const fileInfo = lists[index]
				const fileId = fileInfo.url.split('?id=')[1]
				if (fileId) {
						return new Promise((resolve, reject) => {
							uni.showModal({
								title: '提示',
								content: '删除后将无法恢复，是否确认删除？',
								success: ({ confirm }) => {
									if (confirm) {
										this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
									} else {
										reject()
									}
								}
							})
						})
				} else {
					return true
				}
			},
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				if (!isFail) {
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					this.$implement()
				}
			},
			handleError() {
				// 文件上传失败，弹出提示是否重新上传
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					content: '图片上传失败，是否重新上传？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading('图片上传中')
							this.$refs.happenfile.reUpload()
						} else {
							this.$implement({ immediately: true })
						}
					}
				})
			},

		},
		onLoad() {
			this.form = { launchName: this.vuex_nickName, launchId: this.vuex_id }
			this.$loading()
			this.$u.api.dictList({dictType:'case_union_type'}).then(res=>{
				this.typeList= res.rows
				this.typeList.forEach(v=>{
					if(v.dictLabel) v.text = v.dictLabel
					if(v.dictSort) v.type = v.dictSort
				})
				uni.hideLoading()
			}).catch(err=>{
				uni.hideLoading()
				uni.showToast({title: '加载失败', icon: 'error'})
			})
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}

</style>

<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="上报人:" prop="userName" required>
					<u-input v-model="form.userName" disabled type="text" placeholder="上报人" />
				</u-form-item>
				<u-form-item label="所属部门:" prop="deptName" required>
					<u-input v-model="form.deptName" disabled type="text" placeholder="所属部门" />
				</u-form-item>
				<u-form-item label="上报时间:" prop="happenTime" required>
					<u-input v-model="form.happenTime" type="popup" placeholder="请选择上报时间" @click="showHappenTime = true"/>
					<u-picker v-model="showHappenTime" mode="time" :params="params" :default-time="form.happenTime" @confirm="handleItDateCon"></u-picker>
				</u-form-item>
				<u-form-item label="上报地址:" prop="address" required>
					<u-input v-model="form.address" type="text" placeholder="请选择上报地址" />
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseLocation">选择地址</u-button>
				</u-form-item>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="上报内容:" prop="content" label-position="top" :border-bottom="false">
					<u-input v-model="form.content" type="textarea" maxlength="300" height="140" placeholder="请输入上报内容..." />
				</u-form-item>
				<u-upload
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
				  :size-type="['compressed']"
					:file-list="happenFile"
					:before-remove="handleRemove"
					@on-uploaded="handleAllUpload"
					@on-error="handleError"
				></u-upload>
			</view>
		</u-form>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex">
			<!-- <u-button class="u-flex-1 u-m-r-20" type="primary" shape="circle" :custom-style="subStyle" @click="handleSubmit(1)">暂存</u-button> -->
			<u-button class="u-flex-1" type="primary" :loading="aloading" shape="circle" :custom-style="subStyle" @click="handleOver">提 交</u-button>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import gps from '@/common/gps.js'
	export default {
		data() {
			return {
				aloading: false,
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				showCarType: false,
				// transportCarType: [],
				showDynamicType: false,
				DynamicTypeList: [],
				showHappenTime: false,
				isShowFTaxiType: false,
				isShowSTaxiType: false,
				form: {address:""},
				rules: {
					userName: [{ required: true, message: '上报人员', trigger: 'blur' }],
					deptName: [{ required: true, message: '所属部门', trigger: 'blur' }],
					happenTime: [{ required: true, message: '请选择上报时间', trigger: 'change' }],
					content: [{ required: true, message: '请输入上报内容', trigger: 'blur' }],
					address: [{ required: true, message: '请选择上报地址', trigger: 'change' }]
				},
				happenData: {
					tableName: 'case_work_dynamic',
					status: 1
				},
				happenFile: []
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			// async handleTypeClick(idx) {
			// 	const { dictValue, text, remark } = this.transportCarType[idx]
			// 	this.form = { ...this.form, carType: dictValue, carTypeName: text }
			// },
			handleChooseLocation() {
				uni.chooseLocation({
					latitude: 29.110764,
					longitude: 119.635857,
					success: res => {
						const { address, longitude, latitude } = res
						const lnglat = gps.gcj_decrypt(parseFloat(latitude), parseFloat(longitude))
						this.form = { ...this.form, address: "浙江省金华市婺城区城北街道" + address, longitude: lnglat.lng, latitude: lnglat.lat, lnglat: `${lnglat.lng}, ${lnglat.lat}` }
					},
					fail: () => {
						uni.showToast({
							title: '地图打开失败',
							icon: 'none',
							position: 'bottom'
						})
					}
				})
			},
			handleItDateCon(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, happenTime: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleRemove(index, lists) {
				const fileInfo = lists[index]
				const fileId = fileInfo.url.split('?id=')[1]
				if (fileId) {
						return new Promise((resolve, reject) => {
							uni.showModal({
								title: '提示',
								content: '删除后将无法恢复，是否确认删除？',
								success: ({ confirm }) => {
									if (confirm) {
										this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
									} else {
										reject()
									}
								}
							})
						})
				} else {
					return true
				}
			},
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				if (!isFail) {
					uni.showToast({title: '操作成功', duration: 3000})
					uni.hideLoading()
					this.$implement()
				}
			},
			handleError() {
				// 文件上传失败，弹出提示是否重新上传
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					content: '图片上传失败，是否重新上传？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading('图片上传中')
							this.$refs.happenfile.reUpload()
						} else {
							this.$implement({ immediately: true })
						}
					}
				})
			},
			handleOver() {
				uni.showModal({
					title: '提示',
					content: '是否确认提交？',
					success: ({ confirm }) => {
						if (confirm) {
							this.handleSubmit(9)
						}
					}
				})
			},
			validateFn(isValid) {
				// 根据传入的状态来确定是否要进行验证，用以区别暂存和办结
				return new Promise((resolve, reject) => {
					if (isValid) {
						this.$refs.uForm.validate(valid => {
							if (valid) {
								// 图片验证，没有图片不通过验证
								if (!this.$refs.happenfile.lists.length) {
									this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
									reject()
								} else {
									resolve()
								}
							} else {
								reject()
							}
						})
					} else {
						resolve()
					}
				})
			},
			handleSubmit(status) {
				this.validateFn(status == 9).then(() => {

					// const params = { ...this.form, status, type: 1,address:'金华火车站',longitude:'119.635918',latitude:'29.110464' }
					const params = { ...this.form, status, type: 1}
					// 开始上传
					this.aloading = true
					this.$loading('数据上传中')
					this.$u.api.addDynamic(params).then(res => {
						// 遍历列表，查询是否有未上传的图片
						const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
						this.happenData.businessId = res.data.id
						if (uploadFile) {
							this.$loading('图片上传中')
							this.$refs.happenfile.upload()
							// this.aloading = false
						} else {
							uni.showToast({title: '操作成功', duration: 3000})
							uni.hideLoading()
							this.$implement()
							// this.aloading = false
						}
					}).catch(() => {
						uni.hideLoading()
						this.aloading = false
					})
				}).catch(() => {})
			}
		},
		async onLoad(params) {
			this.$loading('加载中...')
			// 初始化数据
			const timestamp = new Date().getTime()
			const happenTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
			// this.form = { happenTime, userName: this.vuex_nickName, userId: this.vuex_id, deptName:this.vuex_deptName,address:'金华火车站',longitude:'119.635918',latitude:'29.110464'}
			this.form = { happenTime, userName: this.vuex_nickName, userId: this.vuex_id, deptName:this.vuex_deptName}
			uni.hideLoading()
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

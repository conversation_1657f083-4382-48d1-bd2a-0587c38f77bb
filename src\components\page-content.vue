<template>
  <!-- page-content -->
  <view class="page">
    <scroll-view scroll-y :style="{ height: height }" refresher-background="#f5f5f5" :refresher-enabled="refresher" :refresher-threshold="threshold" :refresher-triggered="triggered" @refresherrefresh="refreshing" :scroll-anchoring="anchoring" @scrolltolower="infinite">
      <slot></slot>
    </scroll-view>
  </view>

</template>

<script>
export default {
  name: "PageContent",
  props: {
    refresher: {
      type: Boolean,
      default: false,
    },
    threshold: {
      type: Number,
      default: 80,
    },
    anchoring: {
      type: Boolean,
      default: true,
    },
    infiniting: {
      type: Boolean,
      default: false,
    },
	height: {
		type: String,
		default: '150rpx'
	}
  },
  data() {
    return {
      isRefreshing: false, // 是否在刷新中
      isInfiniting: this.infiniting, // 是否在加载中
      infiniteDisabled: false, // 是否禁用上拉加载
      triggered: true,
    };
  },
  methods: {
    refreshing() {
      if (this.isRefreshing) return;
      this.isRefreshing = true
      this.triggered = true;
      this.isInfiniting = false; // 若下拉刷新和上拉加载同时存在，则每次刷新需要重置上拉加载状态
      this.$emit('refresh', { complete: this.refresherComplete});
    },
    refresherComplete() {
      this.triggered = false;
      this.isRefreshing = false;
      this.isInfiniting = true;
      this.infiniteDisabled = false;
    },
	setStatus(disabled) {
		this.infiniteDisabled = disabled;
	},
    infinite() {
      if(this.isInfiniting) {
        this.$emit('infinite', { setStatus: this.setStatus });
      }
    },
  }
}
</script>
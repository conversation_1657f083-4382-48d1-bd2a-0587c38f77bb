<template>
	<view>
		<map
			id="map"
			ref="map"
			class="map"
			:scale="scale"
			:longitude="longitude"
			:latitude="latitude"
			:markers="markers"
			:style='{ "width": "750rpx", "height": `${windowHeight}px` }'
			@markertap="handleMarkerTap"
		>
			<cover-view class="top">
				<cover-view class="top-item" :class="{ active: item.active }" v-for="(item,idx) in topList" :key="idx" @click="handleClick(idx, item.key)">
					<text class="item-font" style="margin-right: 10px;">{{ item.name }}</text>
					<text class="item-font">{{ item.num }}</text>
				</cover-view>
			</cover-view>
			
			<!-- <cover-view class="m-area-box" :style="{ height: `${move.height}rpx` }">
				<view class="line" @touchstart.stop="handleStart" @touchmove.stop="handleMove" @touchend.stop="handleEnd">
					<view class="line-inner"></view>
				</view>
				<cover-view class="search-box">
					<cover-view class="search">
						<icon type="search" size="17"/>
						<input class="serch-input" type="text" v-model="searchValue" :cursor-spacing="10" placeholder="搜索人员" @focus="isHide = false" @blur="isHide = true">
					</cover-view>`
					<button class="btn" :class="{ hide: isHide }" type="default" @click="handleSearch">
						<text class="btn-text">搜索</text>
					</button>
				</cover-view>
				<cover-view class="content">
					<text class="title">执法人员{{userInfo.nickName}}</text>
					<text class="online">{{userInfo.status == '1' ? '在线' : '离线'}}</text>
					<text class="text">岗位：{{userInfo.postName || '暂无'}}</text>
					<text class="text">手机号码：{{userInfo.phonenumber}}</text>
					<cover-view class="btns">
						<button class="c-btn phone" type="default" plain @click="handleCall(userInfo.phonenumber)">
							<text class="btn-text">拨打电话</text>
						</button>
						<button class="c-btn tra" type="default" plain>
							<text class="btn-text">查看轨迹</text>
						</button>
					</cover-view>
				</cover-view>
			</cover-view> -->
		</map>
	</view>
</template>

<script>
	import gps from '@/common/gps.js'
	
	export default {
		data() {
			return {
				mapContext: null,
				longitude: 119.635857,
				latitude: 29.110764,
				windowHeight: 0,
				scale: 18,
				topList: [
					{ key: 'all', name: '全部案件', num: 0, active: true },
					{ key: 'four', name: '四位一体', num: 0, active: false },
					{ key: 'capture', name: '监控抓拍', num: 0, active: false },
					{ key: 'inspection', name: '巡查发现', num: 0, active: false },
					{ key: 'punish', name: '简易案件', num: 0, active: false },
					{ key: 'tout', name: '黄牛处置', num: 0, active: false },
					{ key: 'trafficCapture', name: '电子抓拍', num: 0, active: false },
					{ key: 'transport', name: '违规处置', num: 0, active: false }
				],
				searchValue: '',
				isHide: true,
				move: {
					isMove: false,
					touchY: 0,
					height: 126
				},
				screenWidth: 0,
				markers: [],
				onlineMarker: [],
				offlineMarker: [],
				fourMarker: [],
				captureMarker: [],
				inspectionMarker: [],
				punishMarker: [],
				toutMarker: [],
				trafficCaptureMarker: [],
				transportMarker: [],
				userInfo: {}
			}
		},
		methods: {
			fetchData() {
				getApp().$loading()
				getApp().$u.api.getListAllCase().then(res => {
					const typeList = {
						four: '四位一体',
						capture: '监控抓拍',
						inspection: '巡查发现',
						punish: '简易案件',
						tout: '黄牛处置',
						trafficCapture: '电子抓拍',
						transport: '违规处置'
					} 
					if (res.data.event && res.data.event.length) {
						this.markers = res.data.event.map(item => {
							const lnglat = gps.gcj_encrypt(parseFloat(item.latitude), parseFloat(item.longitude))
							const marker = {
								id: `${item.id}${item.caseType}`,
								width: 60,
								height: 66,
								caseType: item.caseType,
								latitude: lnglat.latitude,
								longitude: lnglat.longitude,
								iconPath: '../../../../../static/img/map-icon-event.png',
								label: {
									x: -28,
									y: -5,
									content: typeList[item.caseType] || '',
									fontSize: 12,
									padding: 5,
									borderRadius: 18,
									bgColor: '#FAB71C',
									color: '#ffffff'
								}
							}
							// 各种类型点分散
							if (Array.isArray(this[`${item.caseType}Marker`])) {
								this[`${item.caseType}Marker`].push(marker)
							}
							return marker
						})
						let allNum = 0
						let topList = this.topList.map(menu => {
							if (menu.key != 'all') {
								menu.num = this[`${menu.key}Marker`].length
								allNum += this[`${menu.key}Marker`].length
							}
							return menu
						})
						topList[0].num = allNum
						this.topList = topList
						setTimeout(() => {
							this.includePointsFn()
						}, 300)
					}
					uni.hideLoading()
				}).catch(() => {
					uni.hideLoading()
				})
			},
			handleCall(phone) {
				if (phone) {
					if (getApp().$u.test.mobile(phone)) {
						console.log(phone)
						uni.makePhoneCall({
							phoneNumber: phone,
							fail() {
								getApp().mToase('拨打电话失败')
							}
						})
					} else {
						getApp().mToase('当前号码不是一个手机号码')
					}
				} else {
					getApp().mToase('手机号码不存在')
				}
			},
			handleSearch() {
				const searchValue = this.searchValue.trim()
				if (searchValue == '') return
				const allMarkers = [...this.onlineMarker, ...this.offlineMarker]
				const result = allMarkers.filter(item => {
					return item.label.content.includes(searchValue)
				})
				if (result.length) {
					this.markers = result
					this.includePointsFn()
					this.move.height = 126
				} else {
					getApp().mToase('未搜索到相关用户')
				}
			},
			handleMarkerTap(e) {
				const markerId = e.detail.markerId
				const typeList = {
					four: 'pages/home/<USER>/swyt/his/his',
					capture: 'pages/home/<USER>/jkzp/his/his',
					inspection: 'pages/home/<USER>/xcfx/his/his',
					punish: 'pages/home/<USER>/jycx/his/his',
					tout: 'pages/home/<USER>/hncz/his/his',
					trafficCapture: 'pages/home/<USER>/dzzp/his/his',
					transport: 'pages/home/<USER>/wgcz/his/his'
				}
				this.markers = this.markers.map(item => {
					if (item.id === markerId) {
						item.width = 70
						item.height = 77
						console.log(typeList[item.caseType],item.id)
						if (typeList[item.caseType] && item.id) {
							getApp().$u.route({ url: typeList[item.caseType], params: { id: parseInt(item.id) } })
						} else {
							getApp().mToase('信息出错，无法打开')
						}
					} else {
						item.width = 60
						item.height = 66
					}
					return item
				})
			},
			moveCenter() {
				uni.getLocation({
					type: 'gcj02',
					success: res => {
						this.longitude = res.longitude;
						this.latitude = res.latitude;
					}
				})
			},
			handleClick(idx, key) {
				this.topList = this.topList.map((item,index) => {
					if (idx === index) item.active = true
					else item.active = false
					return item
				})
				if (key == 'all') {
					this.markers = [
						...this.fourMarker,
						...this.captureMarker,
						...this.inspectionMarker,
						...this.punishMarker,
						...this.toutMarker,
						...this.trafficCaptureMarker,
						...this.transportMarker,
					]
				} else {
					this.markers = this[`${key}Marker`]
				}
				this.includePointsFn()
			},
			includePointsFn() {
				const points = this.markers.map(item => {
					return { latitude: item.latitude, longitude: item.longitude }
				})
				this.$refs.map.includePoints({ points, padding: [120] })
			},
			handleStart(e) {
				this.move.isMove = true
				this.move.touchY = e.touches[0].screenY
				this.$nextTick(() => {
					this.move.transition = false
				})
			},
			handleMove(e) {
				if (this.move.isMove) {
					const moveLength = e.touches[0].screenY - this.move.touchY
					if (this.move.height < 120 || this.move.height > 486) return
					let h = parseInt(750 * moveLength / this.screenWidth)
					this.move.height -= h
					this.move.touchY = e.touches[0].screenY
				}
			},
			handleEnd(e) {
				this.move.isMove = false
				if (this.move.height <= 300) {
					this.move.height = 126
				} else {
					this.move.height = 445
				}
			}
		},
		onLoad() {
			const systemInfo = uni.getSystemInfoSync()
			this.screenWidth = systemInfo.screenWidth
			this.fetchData()
		},
		onReady() {
			const res = uni.getSystemInfoSync();
			this.windowHeight = res.windowHeight;
			this.mapContext = uni.createMapContext('map', this)
		}
	}
</script>

<style lang="scss">
.top {
	width: 250rpx;
	position: absolute;
	top: 30rpx;
	right: 20rpx;
	background-color: #fff;
	border-radius: 14rpx;
	box-shadow: 0px 2px 16px 0px rgba(0, 0, 0, 0.15);
	display: flex;
	padding: 14rpx;
	/* #ifdef H5 */
	box-sizing: border-box;
	/* #endif */
	&-item {
		display: flex;
		flex-direction: row;
		width: 220rpx;
		height: 54rpx;
		border-radius: 10rpx;
		align-items: center;
		justify-content: center;
		&.active {
			background-color: #4689F5;
			.item-font {
				color: #fff;
			}
		}
		.item-font {
			font-size: 30rpx;
		}
	}
}
.m-area-box {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	// height: 480rpx;
	// transition: height 0.3s ease-in-out;
	border-top-left-radius: 28rpx;
	border-top-right-radius: 28rpx;
	box-shadow: 0px 0px 40px 0px rgba(0, 0, 0, 0.15);
	.line {
		width: 300rpx;
		height: 40rpx;
		position: absolute;
		top: 0;
		left: 225rpx;
		display: flex;
		justify-content: center;
		align-items: center; 
		.line-inner {
			width: 64rpx;
			height: 8rpx;
			background-color: #D6D6D6;
			border-radius: 4rpx;
		}
	}
	.search-box {
		width: 690rpx;
		margin-top: 40rpx;
		margin-left: 30rpx;
		display: flex;
		flex-direction: row;
		overflow: hidden;
		.search {
			flex: 1;
			padding: 0 22rpx;
			background-color: #F0F0F0;
			border-radius: 14rpx;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			height: 66rpx;
			align-items: center;
			.serch-input {
				flex: 1;
				font-size: 30rpx;
				padding-left: 20rpx;
			}
		}
		.btn {
			border-width: 0;
			padding: 0;
			margin-left: 20rpx;
			&.hide {
				margin-right: -120rpx;
			}
			.btn-text {
				color: #fff;
				font-size: 30rpx;
				background-color: #327BF0;
				width: 100rpx;
				height: 66rpx;
				line-height: 66rpx;
				text-align: center;
				border-radius: 14rpx;
			}
		}
	}
	.content {
		padding: 30rpx 0rpx 0;
		.title {
			font-size: 40rpx;
			color: #333333;
			font-weight: 700;
			margin-bottom: 10rpx;
			margin-left: 30rpx;
		}
		.online {
			width: 84rpx;
			height: 42rpx;
			background-color: #E5F0FF;
			color: #327BF0;
			font-size: 26rpx;
			border-radius: 8rpx;
			text-align: center;
			line-height: 42rpx;
			margin-bottom: 12rpx;
			margin-left: 30rpx;
		}
		.text {
			color: #7F7F7F;
			font-size: 26rpx;
			margin-bottom: 15rpx;
			margin-left: 30rpx;
		}
		.btns {
			box-shadow: 0px -1px 0px 0px #EBEBEB;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			padding: 23rpx 30rpx 23rpx;
			.c-btn {
				border-width: 0;
				padding: 0;
				transition: opacity 0.3s;
				.btn-text {
					width: 300rpx;
					height: 76rpx;
					line-height: 76rpx;
					text-align: center;
					border-radius: 76rpx;
					font-size: 30rpx;
					border-width: 2rpx;
				}
				&:active {
					opacity: 0.3;
				}
			}
			.phone {
				.btn-text {
					color: #327BF0;
					background-color: #fff;
					border-color: #327BF0;
					
				}
			}
			.tra {
				.btn-text {
					border-color: #327BF0;
					background-color: #327BF0;
					color: #fff;
				}
			}
		}
	}
}
</style>

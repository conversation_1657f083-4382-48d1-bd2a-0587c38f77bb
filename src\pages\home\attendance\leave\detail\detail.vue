<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="150" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="请假类型:" prop="type" required>
					<u-input v-model="form.type" placeholder="请选择请假类型" disabled type="select" />
					<!-- <u-action-sheet v-model="showLeaveTypeList" :list="leaveTypeList" @click="leaveTypeClick"></u-action-sheet> -->
				</u-form-item>
				<u-form-item label="标题:" prop="title" required>
					<u-input v-model="form.title" type="popup" />
				</u-form-item>
				<u-form-item label="原因:" prop="reason" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.reason" type="textarea" disabled placeholder="原因" />
				</u-form-item>
				<u-form-item label="开始时间:" prop="leaveStartTime" required>
					<u-input v-model="form.leaveStartTime" type="popup" disabled placeholder="请选择开始时间" />
					<u-icon name="calendar"  size="40"></u-icon>
					<!-- <u-picker v-model="showLeaveStartTime" mode="time" :default-time="form.leaveStartTime" :params="params"  @confirm="confirmLeaveStartTime"></u-picker> -->
				</u-form-item>
				<u-form-item label="结束时间:" prop="leaveEndTime" required>
					<u-input v-model="form.leaveEndTime" type="popup" disabled placeholder="请选择结束时间" />
					<u-icon name="calendar"  size="40"></u-icon>
					<!-- <u-picker v-model="showLeaveEndTime" mode="time" :default-time="form.leaveEndTime" :params="params"  @confirm="confirmLeaveEndTime"></u-picker> -->
				</u-form-item>
				<u-form-item label="请假天数:" prop="leaveDay" required>
					<u-input v-model="form.leaveDay" disabled type="number" />天
				</u-form-item>
			</view>
			
			<!-- 间隔 -->
			<view v-for="(historyData, index) in fromData" :key="index">
				<u-gap height="20" bg-color="#F5F5F5"></u-gap>
				<view class="p-lr-30">
					<h3 style="padding: 20rpx 0;">{{ historyData.taskNodeName }} :</h3>
					<u-form-item label="审批人:">
						<u-input v-model="historyData.createName" disabled />
					</u-form-item>
					<u-form-item label="审批时间:">
						<u-input v-model="historyData.createdDate" disabled />
					</u-form-item>
					<view v-for="(fistoryFormData, indexH) in historyData.formHistoryDataDTO" :key="indexH" label-width="80px">
						<u-form-item :label="fistoryFormData.title">
							<u-input v-model="fistoryFormData.value" disabled />
						</u-form-item>
					</view>
				</view>
			</view>
			
		</u-form>
		
		
		
		
		<!-- 提交按钮 -->
		<!-- <view class="btn-box u-border-top u-flex">
			<u-button class="u-flex-1" type="primary" shape="circle" :loading="aloading" :custom-style="subStyle" @click="handleOver">提交</u-button>
		</view> -->
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				leaveTypeList:[],
				showLeaveTypeList:false,
				aloading: false,
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				form: {},
				fromData: [],
				rules: {
					type: [
						{required: true, message: '请假类型不能为空', trigger: 'change'}
					],
					title: [
						{required: true, message: '标题不能为空', trigger: 'blur'}
					],
					reason: [
						{required: true, message: '原因不能为空', trigger: 'blur'}
					],
					leaveStartTime: [
						{required: true, message: '开始时间不能为空', trigger: 'blur'}
					],
					leaveEndTime: [
						{required: true, message: '结束时间不能为空', trigger: 'blur'}
					]
				},
				showLeaveStartTime: false,
				showLeaveEndTime: false
			}
		},
		computed: {
		},		
		methods: {
			// 处理类型
			leaveTypeClick(idx) {
				const { text } = this.leaveTypeList[idx]
				this.form = { ...this.form, type: text, title:`${this.vuex_nickName}的${text}申请`}
			},
			
	
			
			confirmLeaveStartTime(time){
				const { year, month, day, hour, minute, second} = time
				this.form = { ...this.form, leaveStartTime: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			confirmLeaveEndTime(time){
				const { year, month, day, hour, minute, second} = time
				this.form = { ...this.form, leaveEndTime: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleSubmit(status) {
				this.aloading = true
				this.$loading()
				this.$u.api.addLeave(this.form).then(res => {
						uni.hideLoading()
						uni.showToast({title: '操作成功'})
						this.aloading = false
						this.$implement()
				}).catch(() => {
					uni.hideLoading()
					this.aloading = false
				})
			},
			handleOver() {
				// 0:机关人员,1:网格人员,2:外部人员
				const type = this.vuex_type
				if (type == 0) {
					this.form = {...this.form, postCode: 'kz', workflowKey: 'leave-ky'}
				} else if (type == 1) {
					this.form = {...this.form, postCode: 'zz', workflowKey: 'leave-wg'}
				} else {
					return this.mToase('外部人员没有请假权限！')
				}
				
				if (new Date(this.form.leaveEndTime) - new Date(this.form.leaveStartTime) < 0) return this.mToase('请假开始时间不能晚于结束时间！')

				this.$refs.uForm.validate(valid => {
					if (valid) {
						uni.showModal({
							title: '提示',
							content: '是否确认提交？',
							success: ({ confirm }) => {
								if (confirm) {
									this.handleSubmit()
								}
							}
						})
					}
				})
				
			}
		},
		async onLoad(params) {
			// 数据字典数据
			// this.$u.api.dictList({dictType:"activiti_leave_type"}).then(res=>{
			// 	this.leaveTypeList = res.rows.map(item => {
			// 		return { text:item.dictLabel }
			// 	})
			// })
			
			this.$loading()
			const [standardRes, leavApproveRes] = await Promise.all([
				this.$u.api.getLeave({}, params.id),
				this.$u.api.getLeavApprove({}, params.id)
			])
			// const standardRes = await this.$u.api.getLeave({}, params.id)			
			/* 数据请求完毕 */
			uni.hideLoading()
			if (standardRes.code == 200 && leavApproveRes.code == 200) {
				/* 表单数据 */
				this.form = standardRes.data
				this.fromData = leavApproveRes.data

			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.pos-r {
	height: 50rpx;
	line-height: 50rpx;
	font-size: 24rpx;
	background-color: #2979ff;
	border-radius: 10rpx;
	position: absolute;
	top: 30rpx;
	right: 0;
	color: #FFFFFF;
	padding: 0 15rpx;
}
.container {
	// padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

<template>
	<view>
		<!-- 搜索 -->
		<view class="top-search u-flex u-col-center u-border-top ">
			<view class="top-search-left u-flex u-flex-1">
				<u-image src="@/static/img/top-search.png" width="35rpx" height="35rpx"></u-image>
				<u-input v-model="searchValue" type="text" class="u-flex-1 u-m-l-20"></u-input>
			</view>
			<view class="top-search-right">
				<view class="top-search-right-btn u-flex u-row-center u-col-center" @click="handleSearch">
					<u-image src="@/static/img/filter.png" width="28rpx" height="28rpx" class="img"></u-image>
					<text>筛选</text>
				</view>
			</view>
		</view>
		
		<view class="container">
			<view class="list">
				<view class="list-item u-flex u-col-top u-row-between" v-for="(item, idx) in dataList" :key="idx" @click="handleCellClick(item)">
					<u-image class="img" src="@/static/img/list-icon.png" width="60rpx" height="60rpx"></u-image>
					<view class="list-item-content u-flex u-flex-col u-flex-1 u-col-top">
						<text class="title u-line-1">{{ item.noticeTitle }}</text>
						<text class="text u-line-1">发布时间: {{ item.createTime }}</text>
					</view>
					<view class="list-item-state u-flex u-col-center u-row-right">
						<view class="circle" :style="{ backgroundColor: item.isRead == 1 ? '#bdc3bf' : '#EC5656' }"></view>
						<text>{{ item.isRead == 1 ? '已读' : '未读' }}</text>
					</view>
				</view>
				<u-loadmore :status="status" class="u-m-t-20" />
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				searchValue: '',
				pageNum: 1,
				pageSize: 10,
				dataList:[],
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				form: {},
				status: 'loadmore'
			}
		},
		watch: {
			searchValue(nVal) {
				if (nVal.trim() == '') {
					this.fetchData()
				}
			}
		},
		mounted() {
			this.fetchData()
		},
		methods: {
			fetchData() {
				const { pageNum, pageSize } = this
				let params = { userId: this.vuex_id, pageNum, pageSize }
				if (this.searchValue.trim() != '') params.searchValue = this.searchValue
				this.$u.api.getNoticeDetailList(params).then(res => {
					if (pageNum == 1) {
						this.dataList = res.rows
						uni.stopPullDownRefresh()
					} else {
						this.dataList = this.dataList.concat(res.rows)
					}
					this.status = res.rows.length < pageSize ? 'nomore' : 'loadmore'
				}).catch(() => {
					this.status = 'loadmore'
					uni.stopPullDownRefresh()
				})
			},
			handleSearch() {
				this.fetchData()
			},
			rePageData() {
				this.pageNum = 1
				this.fetchData()
			},
			handleCellClick(row) {
				if (row.isRead == 1) {
					this.$u.route({ url: 'pages/my/notice/detail/detail', params: { id: row.noticeId } })
					return
				}
				const timestamp = new Date().getTime()
				const readTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.$loading()
				this.$u.api.editNoticeDetail({ noticeDetailId: row.noticeDetailId, isRead: 1, readTime }).then(() => {
					uni.hideLoading()
					uni.$emit('hideNotice', { noticeId: row.noticeId })
					this.rePageData()
					this.$u.route({ url: 'pages/my/notice/detail/detail', params: { id: row.noticeId } })
				}).catch(() => {
					uni.hideLoading()
				})
			}
		},
		onPullDownRefresh() {
			this.pageNum = 1
			this.fetchData()
		}
	}
</script>

<style lang="scss">
.container {
	margin-top: 133rpx;
	padding-bottom: 30rpx;
	.list {
		padding-bottom: 209rpx;
		.list-item {
			margin: 20rpx 30rpx 0;
			background-color: #FFFFFF;
			border-radius: 12rpx;
			box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1);
			padding: 20rpx 20rpx 30rpx;
			.img {
				flex-shrink: 0;
				margin-right: 20rpx;
			}
			&-content {
				width: 360rpx;
				color: #808080;
				line-height: 32rpx;
				font-size: 24rpx;
				.title {
					width: 100%;
					line-height: 60rpx;
					font-weight: 700;
					font-size: 34rpx;
					color: #333333;
				}
				.text {
					width: 100%;
				}
			}
			&-state {
				width: 180rpx;
				flex-shrink: 0;
				font-size: 28rpx;
				font-weight: 700;
				line-height: 60rpx;
				margin-left: 20rpx;
				.circle {
					width: 16rpx;
					height: 16rpx;
					background-color: #EC5656;
					border-radius: 50%;
					margin-right: 10rpx;
				}
			}
		}
	}
}

.top-search {
	width: 100%;
	height: 103rpx;
	background-color: #fff;
	padding: 0 30rpx;
	// box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1);
	position: fixed;
	top: 0;
	/* #ifdef H5 */
	top: 44px;
	/* #endif */
	z-index: 10;
	&-left {
		height: 68rpx;
		background-color: #F5F5F5;
		border-radius: 68rpx;
		padding: 0 20rpx;
		margin-right: 20rpx;
	}
	&-right {
		&-btn {
			width: 136rpx;
			height: 68rpx;
			border-radius: 68rpx;
			background-color: #327BF0;
			color: #fff;
			font-size: 28rpx;
			transition: all 0.5s;
			.img {
				margin-right: 10rpx;
			}
			&:active {
				opacity: 0.3;
			}
		}
	}
}
</style>

<template>
  <view>
    <!-- 主体 -->
    <view class="tab u-border-top">
      <u-tabs-swiper
        ref="uTabs"
        :list="list"
        :current="current"
        :is-scroll="false"
        swiper-width="750"
        @change="tabsChange"
      ></u-tabs-swiper>
    </view>
    <view class="container">
      <swiper
        class="swiper"
        :current="swiperCurrent"
        @transition="transition"
        @animationfinish="animationfinish"
      >
        <!-- 我的 -->
        <swiper-item
          v-for="(scrollIItem, index) in listParams"
          :key="index"
          class="swiper-item"
        >
          <!-- 搜索 -->
          <view class="top-search u-flex u-col-center">
            <view class="top-search-left u-flex u-flex-1">
              <u-image
                src="@/static/img/top-search.png"
                width="35rpx"
                height="35rpx"
              ></u-image>
              <u-input
                v-model="scrollIItem.keyWords"
                type="text"
                class="u-flex-1 u-m-l-20"
              ></u-input>
            </view>
            <view class="top-search-right">
              <view
                class="top-search-right-btn u-flex u-row-center u-col-center"
                @click="handleSearch"
              >
                <u-image
                  src="@/static/img/filter.png"
                  width="28rpx"
                  height="28rpx"
                  class="img"
                ></u-image>
                <text>筛选</text>
              </view>
            </view>
          </view>
          <!-- 内容 -->
          <page-content
            refresher
            infiniting
            height="calc(100vh - 183rpx)"
            class="swiper-item-inner"
            @refresh="refresh"
            @infinite="infiniteScroll"
          >
            <view class="list">
              <view
                v-for="(item, idx) in scrollIItem.dataList"
                :key="idx"
                class="list-item u-flex u-col-top u-row-between"
                @click="handleOpen(item)"
              >
                <u-image
                  class="img"
                  src="@/static/img/list-icon.png"
                  width="60rpx"
                  height="60rpx"
                ></u-image>
                <view
                  class="list-item-content u-flex u-flex-col u-flex-1 u-col-top"
                >
                  <text class="title u-line-1">{{ item.content }}</text>
                  <text class="text u-line-1">交办人: {{ item.userName }}</text>
                  <text class="text u-line-1">
                    执行人: {{ item.workUserName }}
                  </text>
                  <text class="text u-line-1">
                    规定完成时间: {{ item.limitTime }}
                  </text>
                </view>
                <view class="list-item-state u-flex u-col-center u-row-right">
                  <view
                    class="circle"
                    :style="{ backgroundColor: circleColor[item.status] }"
                  ></view>
                  <text>{{ item.status | statusName }}</text>
                </view>
              </view>
              <u-loadmore
                v-if="scrollIItem.noPermission"
                :status="scrollIItem.status"
                class="u-m-t-20"
              />
              <u-empty
                v-if="!scrollIItem.noPermission"
                mode="permission"
                class="u-m-t-80"
              ></u-empty>
            </view>
          </page-content>
        </swiper-item>
      </swiper>
    </view>
    <!-- 新增按钮 -->
    <view
      class="bottom-btn u-flex u-row-center u-col-center"
      @click="$u.route('pages/home/<USER>/ldjb/add/add')"
    >
      <u-image
        src="@/static/img/btn-add-icon.png"
        width="33rpx"
        height="33rpx"
      ></u-image>
      <text class="text">新增</text>
    </view>
  </view>
</template>

<script>
import PageContent from '@/components/page-content.vue'
export default {
  components: {
    PageContent,
  },
  filters: {
    statusName(status) {
      const statusObj = { 0: '处理中', 2: '处理完成', 9: '已办结' }
      return statusObj[status]
    },
  },
  data() {
    return {
      list: [{ name: '我的' }, { name: '已办结' }],
      current: 0,
      swiperCurrent: 0,
      listParams: [
        {
          keyWords: '',
          pageNum: 1,
          pageSize: 10,
          dataList: [],
          status: 'loading',
          noPermission: true,
          isFirst: true,
        },
        {
          keyWords: '',
          pageNum: 1,
          pageSize: 10,
          dataList: [],
          status: 'loading',
          noPermission: true,
          isFirst: true,
        },
      ],
      sIsFirst: true,
      circleColor: {
        2: '#FAB71C',
        9: '#bdc3bf',
      },
    }
  },
  methods: {
    refresh({ complete }) {
      this.listParams[this.current].pageNum = 1
      this.fetchData(complete)
    },
    infiniteScroll({ setStatus }) {
      if (this.listParams[this.current].status === 'nomore') return
      this.listParams[this.current].pageNum++
      this.fetchData()
    },
    handleSearch() {
      this.listParams[this.current].pageNum = 1
      this.listParams = this.listParams.map((item) => {
        item.isFirst = true
        return item
      })
      this.fetchData()
    },
    fetchData(complete) {
      let { pageNum, pageSize, keyWords, status, dataList, noPermission } =
        this.listParams[this.current]
      const params = { pageNum, pageSize }
      if (keyWords) params.searchValue = keyWords
      if (this.current === 0) {
        params.userId = this.vuex_id
        params.workUserId = this.vuex_id
      }
      if (this.current === 1) params.status = 9 // 已办结状态
      this.listParams[this.current].status = 'loading'
      this.$u.api
        .getLeaderTaskList(params)
        .then((res) => {
          if (pageNum === 1) {
            dataList = res.rows
          } else {
            dataList = dataList.concat(res.rows)
          }
          this.listParams[this.current].status =
            res.rows.length < 10 ? 'nomore' : 'loadmore'
          if (complete) complete()
          this.listParams.splice(this.current, 1, {
            pageNum,
            pageSize,
            keyWords,
            status: this.listParams[this.current].status,
            dataList,
            noPermission,
            isFirst: false,
          })
        })
        .catch((err) => {
          this.listParams[this.current].status = 'loadmore'
          if (complete) complete()
          if (err.data.code === 403) noPermission = false
          this.listParams.splice(this.current, 1, {
            pageNum,
            pageSize,
            keyWords,
            status: 'loadmore',
            dataList,
            noPermission,
            isFirst: false,
          })
        })
    },
    rePageData() {
      this.listParams[this.current].pageNum = 1
      this.listParams = this.listParams.map((item) => {
        item.isFirst = true
        return item
      })
      this.fetchData()
    },
    handleOpen(item) {
      if (item.status == 1) {
        this.$u.route({
          url: 'pages/home/<USER>/ldjb/add/add',
          params: { id: item.id },
        })
      } else {
        this.$u.route({
          url: 'pages/home/<USER>/ldjb/detail/detail',
          params: { id: item.id },
        })
      }

      /* if (item.status != 9) {
					this.$u.route({ url: 'pages/home/<USER>/ldjb/detail/detail', params: { id: item.id } })
				} else {
					this.$u.route({ url: 'pages/home/<USER>/ldjb/his/his', params: { id: item.id } })
				} */
    },
    tabsChange(index) {
      this.swiperCurrent = index
    },
    transition(e) {
      const dx = e.detail.dx
      this.$refs.uTabs.setDx(dx)
    },
    animationfinish(e) {
      const current = e.detail.current
      this.$refs.uTabs.setFinishCurrent(current)
      this.swiperCurrent = current
      this.current = current
      if (this.listParams[current].isFirst) {
        // 第一次切换到第二页加载数据
        this.fetchData()
      }
    },
  },
  onLoad() {
    this.fetchData()
  },
}
</script>

<style lang="scss">
.top-search {
  width: 100%;
  height: 103rpx;
  background-color: #fff;
  padding: 0 30rpx;
  box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1);
  position: relative;
  z-index: 10;
  &-left {
    height: 68rpx;
    background-color: #f5f5f5;
    border-radius: 68rpx;
    padding: 0 20rpx;
    margin-right: 20rpx;
  }
  &-right {
    &-btn {
      width: 136rpx;
      height: 68rpx;
      border-radius: 68rpx;
      background-color: #327bf0;
      color: #fff;
      font-size: 28rpx;
      transition: all 0.5s;
      .img {
        margin-right: 10rpx;
      }
      &:active {
        opacity: 0.3;
      }
    }
  }
}

.tab {
}

// 主体
.container {
  height: calc(100vh - 80rpx);
  background-color: #f5f5f5;
  // min-height: 100vh;
  .swiper {
    height: 100%;
    .swiper-item-inner {
      width: 100%;
      height: calc(100vh - 183rpx);
    }
  }
  .list {
    padding-bottom: 209rpx;
    .list-item {
      margin: 20rpx 30rpx 0;
      background-color: #ffffff;
      border-radius: 12rpx;
      box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1);
      padding: 20rpx 20rpx 30rpx;
      .img {
        flex-shrink: 0;
        margin-right: 20rpx;
      }
      &-content {
        width: 360rpx;
        color: #808080;
        line-height: 32rpx;
        font-size: 24rpx;
        .title {
          width: 100%;
          line-height: 60rpx;
          font-weight: 700;
          font-size: 34rpx;
          color: #333333;
        }
        .text {
          width: 100%;
        }
      }
      &-state {
        width: 180rpx;
        flex-shrink: 0;
        font-size: 28rpx;
        font-weight: 700;
        line-height: 60rpx;
        margin-left: 20rpx;
        .circle {
          width: 16rpx;
          height: 16rpx;
          background-color: #ec5656;
          border-radius: 50%;
          margin-right: 10rpx;
        }
      }
    }
  }
}

// 按钮
.bottom-btn {
  width: 220rpx;
  height: 88rpx;
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  margin-left: -110rpx;
  background-color: #327bf0;
  color: #ffffff;
  border-radius: 88rpx;
  box-shadow: 0px 0px 14px 0px rgba(82, 97, 121, 0.3);
  transition: all 0.5s;
  z-index: 10;
  &:active {
    background-color: #73a6f7;
  }
  .text {
    font-size: 28rpx;
    margin-left: 15rpx;
  }
}
</style>

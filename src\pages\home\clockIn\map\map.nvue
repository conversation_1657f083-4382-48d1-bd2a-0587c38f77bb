<template>
	<view>
		<map
			id="map"
			ref="map"
			class="map"
			:scale="scale"
			:longitude="longitude"
			:latitude="latitude"
			:polygons="polygons"
			:show-location="true"
			:include-points="includePoints"
			:style='{ "width": "750rpx", "height": `${windowHeight}px` }'
		></map>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				mapContext: null,
				longitude: 119.635857,
				latitude: 29.110764,
				windowHeight: 0,
				scale: 18,
				includePoints: [],
				polygons: []
			}
		},
		methods: {
			moveCenter() {
				uni.getLocation({
					type: 'gcj02',
					success: res => {
						this.longitude = res.longitude;
						this.latitude = res.latitude;
					}
				})
			},
		},
		onLoad(params) {
			if (params.area) {
				// 遍历返回组好的打卡区域
				const polygons = params.area.split('/').map(poy => {
					return poy.split(';').map(lnglat => {
						const ll = lnglat.split(',')
						return { longitude: ll[0], latitude: ll[1] }
					})
				})
				this.polygons = polygons.map(points => {
					return {
						points,
						strokeWidth: 2,
						strokeColor: '#04a9e7',
						fillColor: '#04a9e74D'
					}
				})
			}
			this.moveCenter();
		},
		onReady() {
			const res = uni.getSystemInfoSync();
			this.windowHeight = res.windowHeight;
			this.mapContext = uni.createMapContext('map', this)
		}
	}
</script>

<style>

</style>

<template>
	<view>
		<top-supervise :caseId="form.trafficCaptureId" caseType="trafficCapture" :status="form.status"/>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<!-- <u-form-item label="案件名称:" prop="title" required>
					<u-input v-model="form.title" disabled placeholder="请输入案件名称" />
				</u-form-item> -->
				<u-form-item label="上报人:" prop="userName">
					<u-input v-model="form.userName"   placeholder="请输入上报人"  />
				</u-form-item>
				<!-- <u-form-item label="抄告单号:" prop="noticeNo"  required>
					<u-input v-model="form.noticeNo"  placeholder="请输入抄告单号"/>
				</u-form-item> -->
				<!-- <u-form-item label="号牌种类:" prop="carType"  required>
					<u-input v-model="form.carType"  placeholder="请输入号牌种类"/>
				</u-form-item> -->
				<u-form-item label="车牌号码:" prop="carNo"  required>
					<u-input v-model="form.carNo"  placeholder="请输入车牌号码"/>
				</u-form-item>
				<u-form-item label="违规时间:" prop="happenTime" required>
					<u-input v-model="form.happenTime"  placeholder="请选择违规时间" />
				</u-form-item>
				<!-- <u-form-item label="道路编码:" prop="roadCode" required>
					<u-input v-model="form.roadCode"  placeholder="请选择道路编码"  />
				</u-form-item> -->
				<!-- <u-form-item label="路口:" prop="intersection" required>
					<u-input v-model="form.intersection"  placeholder="请选择路口"  />
				</u-form-item> -->
				<!-- <u-form-item label="违规类型:" prop="type" required>
					<u-input v-model="form.type"  placeholder="请选择违规类型"  />
				</u-form-item> -->
				<u-form-item label="违规地点:" prop="caseAddress" required>
					<u-input v-model="form.caseAddress"  placeholder="请选择地址描述"  />
				</u-form-item>
				<!-- <u-form-item label="违法行为:" prop="behavior" required>
					<u-input v-model="form.behavior"  placeholder="请选择违法行为"  />
				</u-form-item> -->
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="违法行为:" prop="behavior" label-position="top" :border-bottom="false">
					<u-input v-model="form.behavior" disabled type="textarea" maxlength="500" height="140" placeholder="请输入内容描述..."/>
				</u-form-item>
				<u-upload
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:auto-upload="false"
					:size-type="['compressed']"
					:file-list="happenFile"
					:deletable="false"
					:customBtn="true"
				></u-upload>
			</view>
			<!-- 间隔 -->
			<!-- <u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="车身颜色:" prop="carColor" required>
					<u-input v-model="form.carColor" disabled placeholder="请输入车身颜色" />
				</u-form-item>
				<u-form-item label="车辆品牌:" prop="carBrand" required>
					<u-input v-model="form.carBrand"   placeholder="请输入车辆品牌"  />
				</u-form-item>
				<u-form-item label="所有人:" prop="carOwnner"  required>
					<u-input v-model="form.carOwnner"  placeholder="请输入所有人"/>
				</u-form-item>
				<u-form-item label="车辆车型:" prop="carModel"  required>
					<u-input v-model="form.carModel"  placeholder="请输入车辆车型"/>
				</u-form-item>
				<u-form-item label="使用性质:" prop="useProperties"  required>
					<u-input v-model="form.useProperties"  placeholder="请输入使用性质"/>
				</u-form-item>
				<u-form-item label="住所地址:" prop="ownnerAddress"  required>
					<u-input v-model="form.ownnerAddress"  placeholder="请输入住所地址"/>
				</u-form-item>
				<u-form-item label="邮政编码:" prop="postalcode"  required>
					<u-input v-model="form.postalcode"  placeholder="请输入邮政编码"/>
				</u-form-item>
				<u-form-item label="联系电话:" prop="phone"  required>
					<u-input v-model="form.phone"  placeholder="请输入联系电话"/>
				</u-form-item>
			</view> -->
		</u-form>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import topSupervise from '@/components/top-supervise.vue'
	export default {
		components: {
			topSupervise
		},
		data() {
			return {
				happenFile:[],
				form:{},
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
			}
		},
		methods: {

		},
		onLoad(params){
			this.$u.api.gettraffic({},params.id).then(res=>{
				this.form = {...res.data}
			})
			this.$u.api.getFileList({ tableName: 'case_traffic',  businessId: params.id }).then(res=>{
				this.happenFile = res.rows.map(item => {
						return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
					})
			})
		}
	}
</script>

<style>
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
</style>

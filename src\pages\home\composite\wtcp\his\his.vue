<template>
  <view>
    <top-supervise
      :case-id="form.inspectionId"
      case-type="inspection"
      :status="form.status"
    />
    <u-form
      ref="uForm"
      :model="form"
      class="container u-border-top"
      label-width="200"
      :label-style="labelStyle"
    >
      <view class="p-lr-30">
        <!-- <u-form-item label="巡查对象:" prop="obj" >
					<u-radio-group v-model="objValue">
						<u-radio
							v-for="(item, index) in objList" :key="index"
							:name="item.id"
							disabled
						>
							{{item.name}}
						</u-radio>
					</u-radio-group>
				</u-form-item> -->
        <!-- <u-form-item label="标题:" prop="inspectionName" required>
					<u-input v-model="form.inspectionName" placeholder="请输入标题" />
				</u-form-item> -->
        <!-- <view v-if="objValue == 1">
					<u-form-item label="店铺名称:" prop="businessName" required>
						<u-input v-model="form.businessName" disabled placeholder="请扫码自动完成填写" />
					</u-form-item>
					<u-form-item label="营业执照号码:" prop="businessNo" required>
						<u-input v-model="form.businessNo" disabled placeholder="请扫码自动完成填写" />
					</u-form-item>
				</view> -->
        <u-form-item
          v-if="!isTemporary"
          label="车牌号:"
          prop="contact"
          required
        >
          <u-input
            v-model="form.contact"
            type="text"
            disabled
            placeholder="请选择车牌号"
          />
        </u-form-item>
        <u-form-item v-if="!isTemporary" label="联系电话:" prop="phone">
          <u-input v-model="form.phone" disabled placeholder="请输入联系电话" />
        </u-form-item>
        <u-form-item v-if="!isTemporary" label="性别:" prop="sex" required>
          <u-radio-group v-model="form.sex">
            <u-radio
              v-for="(item, index) in sexList"
              :key="index"
              :name="item.id"
              disabled
            >
              {{ item.name }}
            </u-radio>
          </u-radio-group>
        </u-form-item>

        <!-- <view v-if="!isTemporary"> -->
        <view>
          <u-form-item label="发生时间:" prop="happenDate" required>
            <u-input
              v-model="form.happenDate"
              type="popup"
              placeholder="请选择发生时间"
            />
          </u-form-item>
          <u-form-item label="发生地址:" prop="address" required>
            <u-input
              v-model="form.address"
              type="popup"
              placeholder="请选择地址"
            />
          </u-form-item>
        </view>
        <!-- <view v-else>
					<u-form-item label="任务下发时间:">
						<u-input v-model="form.assignDate" type="popup" placeholder="任务下发时间"  />
					</u-form-item>
					<u-form-item label="店铺地址:" >
						<u-input v-model="form.shopAddress" type="popup" placeholder="店铺地址" />
						<u-button size="mini" type="primary" v-slot="right" @click="openMap">导航</u-button>
					</u-form-item>
				</view> -->
        <!-- <u-form-item label="经纬度:" prop="lnglat" :border-bottom="false" required>
					<u-input v-model="form.lnglat" type="popup" disabled  placeholder="请选择地址"/>
				</u-form-item> -->
      </view>
      <!-- 间隔 -->
      <u-gap height="20" bg-color="#F5F5F5"></u-gap>
      <view class="p-lr-30">
        <u-form-item label="巡查人员:" prop="userName" required>
          <u-input
            v-model="form.userName"
            type="popup"
            placeholder="请选择巡查人员"
          />
          <!-- <u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('userId')">选择人员</u-button> -->
        </u-form-item>
        <u-form-item label="辅助人员:" prop="userNames">
          <u-input
            v-model="form.userNames"
            type="popup"
            placeholder="请选择辅助人员"
          />
        </u-form-item>
        <!-- <u-form-item label="是否有问题:" prop="isProblem" :border-bottom="form.isProblem == 1">
					<u-radio-group v-model="form.isProblem" disabled >
						<u-radio name="1" >有</u-radio>
						<u-radio name="0" >无</u-radio>
					</u-radio-group>
				</u-form-item> -->
      </view>
      <view>
        <view class="p-lr-30">
          <!-- <u-form-item label="警情类型:" prop="policeCategoryName" required>
						<u-input v-model="form.policeCategoryName" type="popup" :select-open="showList" placeholder="请选择警情类型" />
					</u-form-item> -->
          <u-form-item label="警情类别:" prop="policeTypeName" required>
            <u-input
              v-model="form.policeTypeName"
              type="popup"
              :select-open="showTypeList"
              placeholder="请选择警情类别"
            />
          </u-form-item>
          <u-form-item
            label="案由名称:"
            prop="summaryName"
            required
            :border-bottom="false"
          >
            <u-input
              v-model="form.summaryName"
              type="popup"
              placeholder="请选择案由"
            />
          </u-form-item>
        </view>
        <u-gap height="20" bg-color="#F5F5F5"></u-gap>
        <view class="p-lr-30">
          <!-- <u-form-item label="处理类型:" prop="handleTypeName" required>
						<u-input v-model="form.handleTypeName" type="popup" :select-open="showhandleTypeList" placeholder="请选择警情类别" />
					</u-form-item> -->
          <u-form-item
            label="警情内容:"
            prop="policeContent"
            label-position="top"
            :border-bottom="false"
            required
          >
            <u-input
              v-model="form.policeContent"
              type="textarea"
              disabled
              maxlength="300"
              height="140"
              placeholder="请输入警情内容..."
            />
          </u-form-item>
        </view>
      </view>
      <!-- <u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="是否店铺巡查:" prop="isTemporary" :border-bottom="form.isTemporary == 1">
					<u-radio-group v-model="form.isTemporary"  >
						<u-radio name="1" >是</u-radio>
						<u-radio name="0" >否</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="店铺巡查:" prop="temporary" :border-bottom="false" required v-if="form.isTemporary == 1">
					<u-input v-model="form.temporaryName" type="popup" :select-open="showTemporaryList" placeholder="请选择店铺巡查" @click="showTemporaryList = true" />
					<u-action-sheet v-model="showTemporaryList" :list="temporaryList" @click="handleTemporaryClick"></u-action-sheet>
				</u-form-item>
			</view> -->
      <!-- <u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="是否联合执法:" prop="isUnion" :border-bottom="form.isUnion == 1">
					<u-radio-group v-model="form.isUnion" disabled >
						<u-radio name="1" >是</u-radio>
						<u-radio name="0" >否</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="联合执法:" prop="unionName" required :border-bottom="false" v-if="form.isUnion == 1">
					<u-input v-model="form.unionName" type="popup" :select-open="showunionList" placeholder="请选择联合执法" />
				</u-form-item>
			</view> -->
      <!-- 间隔 -->
      <u-gap height="20" bg-color="#F5F5F5"></u-gap>
      <view class="p-lr-30">
        <!-- <u-form-item label="内容描述:" prop="inspectionContent" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.inspectionContent" type="textarea" maxlength="300" height="140" placeholder="请输入内容描述..."/>
				</u-form-item> -->
        <u-form-item
          label="现场情况（需要有车辆照片，执法队员照片，抄牌单据照片）:"
          label-position="top"
        >
          <u-upload
            ref="happenfile"
            name="files"
            max-count="4"
            width="157"
            height="157"
            :header="header"
            :auto-upload="false"
            :action="action"
            :form-data="happenData"
            :size-type="['compressed']"
            :file-list="happenFile"
            :custom-btn="true"
            :deletable="false"
          ></u-upload>
        </u-form-item>
      </view>

      <!-- 处理结果 -->
      <!-- 间隔 -->
      <u-gap height="20" bg-color="#F5F5F5"></u-gap>
      <view class="p-lr-30">
        <u-form-item
          label="处理结果:"
          prop="inspectionContent"
          label-position="top"
          :border-bottom="false"
          required
        >
          <u-input
            v-model="form.inspectionContent"
            type="textarea"
            disabled
            maxlength="300"
            height="140"
            placeholder="请输入处理结果..."
          />
        </u-form-item>
        <u-upload
          ref="resfile"
          name="files"
          max-count="4"
          width="157"
          height="157"
          :header="header"
          :auto-upload="false"
          :action="action"
          :form-data="happenData"
          :size-type="['compressed']"
          :file-list="resFile"
          :custom-btn="true"
          :deletable="false"
        ></u-upload>
      </view>
    </u-form>
    <!-- 提示 -->
    <u-top-tips ref="uTips"></u-top-tips>
  </view>
</template>

<script>
import topSupervise from '@/components/top-supervise.vue'
import Map from '@/common/openMap.js'
import gps from '@/common/gps.js'

export default {
  components: {
    topSupervise,
  },
  data() {
    return {
      popupUnionShow: false,
      unionLists: [],
      objList: [
        { id: 1, name: '店铺' },
        { id: 2, name: '其他' },
      ],
      sexList: [
        { id: 1, name: '男' },
        { id: 2, name: '女' },
      ],
      isList: [
        { id: 1, name: '是' },
        { id: 0, name: '否' },
      ],
      objValue: 1,
      params: {
        year: true,
        month: true,
        day: true,
        hour: true,
        minute: true,
        second: true,
      },
      labelStyle: {
        color: '#808080',
        fontSize: '30rpx',
      },
      subStyle: {
        height: '86rpx',
        backgroundColor: '#327BF0',
      },
      showList: false,
      showTypeList: false,
      showunionList: false,
      showTemporaryList: false,
      typeList: [],
      categoryList: [],
      unionList: [],
      temporaryList: [],
      showHappenDate: false,
      showAssignDate: false,
      form: { address: '浙江省金华市婺城区城北街道' },
      happenData: {
        tableName: 'case_inspection',
        status: 2,
      },
      happenFile: [],
      isTemporary: true,
      resFile: [],
      showhandleTypeList: false,
      handleTypeList: [
        { type: 1, text: '当场整改' },
        { type: 2, text: '简易处罚' },
      ],
    }
  },
  computed: {
    action() {
      // #ifdef H5
      return `/prod-api/system/file/upload`
      // #endif
      // #ifndef H5
      return `${this.vuex_ip}/prod-api/system/file/upload`
      // #endif
    },
    header() {
      return {
        Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token'),
      }
    },
  },
  methods: {
    openMap() {
      if (this.form.shopLatitude && this.form.shopLongitude) {
        const lat = parseFloat(this.form.shopLatitude)
        const lng = parseFloat(this.form.shopLongitude)
        Map.openMap(lat, lng, this.form.businessName, 'wgs84')
      } else {
        this.mToase('未获取店铺经纬度')
      }
    },
  },
  onLoad(params) {
    this.$loading()
    Promise.all([
      this.$u.api.dictList({ dictType: 'case_alert_type' }), // 警情类别
      this.$u.api.dictList({ dictType: 'case_call_type' }), // 警情类型
      this.$u.api.unionList(), // 联合执法
    ])
      .then((res) => {
        this.typeList = res[0].rows
        this.categoryList = res[1].rows
        this.unionLists = res[2].rows
        this.categoryList.forEach((v) => {
          if (v.dictLabel) v.text = v.dictLabel
          if (v.dictValue) v.type = v.dictValue
        })
        this.typeList.forEach((v) => {
          if (v.dictLabel) v.text = v.dictLabel
          if (v.dictValue) v.type = v.dictValue
        })
        if (params.id) {
          Promise.all([
            this.$u.api.getInspection({}, params.id),
            this.$u.api.getFileList({
              tableName: 'case_inspection',
              businessId: params.id,
            }),
          ])
            .then((resAry) => {
              const formData = resAry[0].data
              if (!formData.shopId) this.objValue = 2
              else this.objValue = 1
              const typeTexts = this.typeList.find(
                (item) => item.dictValue == formData.policeType
              )
              const policeCategory = this.categoryList.find(
                (item) => item.dictValue == formData.policeCategory
              )
              const union = this.unionLists.find(
                (item) => item.unionId == formData.unionId
              )
              const lnglat = `${formData.longitude || ''},${
                formData.latitude || ''
              }`
              if (formData.isTemporary == 1) this.isTemporary = true
              else this.isTemporary = false
              if (formData.handleType) {
                formData.handleTypeName = this.handleTypeList.find(
                  (item) => item.type == formData.handleType
                ).text
              }
              this.form = {
                ...formData,
                unionName: union ? union.title : '',
                policeTypeName: typeTexts ? typeTexts.text : '',
                policeCategoryName: policeCategory ? policeCategory.text : '',
                lnglat,
              }
              const happenFile = []
              const resFile = []
              resAry[1].rows.forEach((item) => {
                const img = {
                  url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}`,
                }
                if (item.status == 2) {
                  happenFile.push(img)
                } else if (item.status == 9) {
                  resFile.push(img)
                }
              })
              this.happenFile = happenFile
              this.resFile = resFile
              console.log('this.happenFile', this.happenFile)
              console.log('this.resFile', this.resFile)
              uni.hideLoading()
            })
            .catch((err) => {
              console.log(err)
              uni.hideLoading()
              uni.showToast({ title: '加载失败', icon: 'error' })
            })
          uni.hideLoading()
        } else {
          const timestamp = new Date().getTime()
          const happenDate = this.$u.timeFormat(
            timestamp,
            'yyyy-mm-dd hh:MM:ss'
          )
          this.form = {
            sex: 1,
            isProblem: 0,
            isTemporary: 0,
            isUnion: 0,
            happenDate,
            userName: this.vuex_nickName,
            userId: this.vuex_id,
          }
        }
        uni.hideLoading()
      })
      .catch((err) => {
        console.log(err)
        uni.hideLoading()
        uni.showToast({ title: '加载失败', icon: 'error' })
      })
  },
}
</script>

<style lang="scss">
.p-lr-30 {
  padding: 0 30rpx;
  background-color: #fff;
}
.container {
  padding-bottom: 145rpx;
}
.btn-box {
  width: 100%;
  padding: 14rpx 30rpx;
  position: fixed;
  bottom: 0;
  background-color: #ffffff;
  z-index: 10;
}
.popup {
  // padding: 12rpx 24rpx;
  // margin-bottom: 12rpx;
}
.lp {
  padding: 10rpx;
}
.lp text {
  margin-left: 24rpx;
}
</style>

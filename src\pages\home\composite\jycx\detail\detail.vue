<template>
	<view>
		<top-supervise :caseId="form.punishmentId" caseType="punish" :status="form.status"/>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="250" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="处罚对象:" prop="userType" required>
					<u-radio-group v-model="form.userType" >
						<u-radio name="1">公民</u-radio>
						<u-radio name="2">法人、其他组织</u-radio>
					</u-radio-group>
				</u-form-item>
			</view>
			<view class="p-lr-30" v-if="form.userType == 2">
				<u-form-item label="是否个体工商户:" prop="isSelfEmployed" >
					<u-radio-group v-model="form.isSelfEmployed" >
						<u-radio name="1">是</u-radio>
						<u-radio name="0">否</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="单位名称:" prop="companyName" required>
					<u-input v-model="form.companyName" placeholder="请输入单位名称" />
				</u-form-item>
				<u-form-item label="单位地址:" prop="companyAddress" required>
					<u-input v-model="form.companyAddress" type="text" placeholder="请选择单位地址" />
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseLocation('zz')">选择地址</u-button>
				</u-form-item>
				<u-form-item label="统一社会信用代码:" prop="companyCode" required>
					<u-input v-model="form.companyCode"  placeholder="请输入单位统一社会信用代码" />
				</u-form-item>
			</view>
			<view class="p-lr-30">
				<u-form-item label="当事人姓名:" prop="party">
					<u-input v-model="form.party" placeholder="请输入当事人姓名" />
				</u-form-item>
				<u-form-item label="联系电话:" prop="phone" required>
					<u-input v-model="form.phone" placeholder="请输入当事人联系电话" />
				</u-form-item>
				<u-form-item label="当事人性别:" prop="gender" required>
					<u-radio-group v-model="form.gender" >
						<u-radio name="1">男</u-radio>
						<u-radio name="2">女</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="年龄:" prop="age" >
					<u-input v-model="form.age" type="number" placeholder="请输入当事人年龄" />
				</u-form-item>
				<!-- <u-form-item label="身份证:" prop="identityCard" :border-bottom="false">
					<u-input v-model="form.identityCard" placeholder="请输入当事人身份证" />
				</u-form-item> -->
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="案发时间:" prop="caseTime" required>
					<u-input v-model="form.caseTime" type="popup" placeholder="请选择案发时间" @click="showCaseDate = true"  />
					<u-icon name="calendar"  @click="showCaseDate = true"  size="40"></u-icon>
					<u-picker v-model="showCaseDate" mode="time" :params="params" :default-time="form.caseTime" @confirm="handCaseTime"></u-picker>
				</u-form-item>
				<u-form-item label="案由:" prop="summaryName" required>
					<u-input v-model="form.summaryName" type="popup" placeholder="请选择案由"/>
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('summaryType')">选择案由</u-button>
				</u-form-item>
				<u-form-item label="案发地址:" prop="address" required>
					<u-input v-model="form.address" type="text" placeholder="请选择地址" />
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseLocation">选择地址</u-button>
				</u-form-item>
				<view class="u-rela">
					<u-form-item label="立案简介:" prop="caseContent" label-position="top" :border-bottom="false" required>
						<u-input v-model="form.caseContent" type="textarea" maxlength="300" height="140" placeholder="请输入立案简介..."/>
					</u-form-item>
					<view class="pos-r" @click="handleContentConfirm">回填</view>
				</view>
				<u-upload
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
					:size-type="['compressed']"
					:file-list="happenFile"
					:before-remove="handleRemove"
					@on-uploaded="handleAllUpload"
					@on-error="handleError"
				></u-upload>
			</view>

			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="处罚类型:" prop="punishType" required>
					<u-radio-group v-model="form.punishType" >
						<u-radio name="0">警告</u-radio>
						<u-radio name="1">罚款</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item v-if="form.punishType == 1" label="处罚金额:" prop="punishMoney" required>
					￥<u-input v-model="form.punishMoney" type="number" placeholder="请输入处罚金额" />
				</u-form-item>
				<u-form-item label="主办人:" prop="userName" required>
					<u-input v-model="form.userName" type="select" placeholder="请选择主办人" @click="showUser = true"/>
					<u-select v-model="showUser" :list="userList" @confirm="handleConfirm"></u-select>
				</u-form-item>
				<u-form-item label="协办人:" prop="userIds" :border-bottom="false" >
					<u-input v-model="form.userNames" type="popup" placeholder="请选择协办人"/>
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('userIds')">选择人员</u-button>
				</u-form-item>
				<u-form-item label="处罚结果:" prop="remark" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.remark" type="textarea" maxlength="300" height="140" placeholder="请输入处罚结果..."/>
				</u-form-item>
				<u-upload
					ref="resfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
					:size-type="['compressed']"
					:file-list="resFile"
					:before-remove="handleRemove"
					@on-uploaded="handleAllUpload"
					@on-error="handleError"
				></u-upload>
			</view>
		</u-form>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex u-row-between">
			<view class="u-flex-1">
				<u-button type="primary" shape="circle" :custom-style="subStyle" @click="handleSubmit(2)">暂存</u-button>
			</view>
			<view class="u-flex-1 u-m-l-20">
				<u-button type="primary"  shape="circle" :custom-style="subStyle" @click="handleSubmit(9)">办结</u-button>
			</view>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import gps from '@/common/gps.js'
	import topSupervise from '@/components/top-supervise.vue'
	export default {
		components: {
			topSupervise
		},
		data() {
			return {
				form:{userType:1,isSelfEmployed:1,gender:1,type:0,punishType:0,onlineCarTarnsport:0,onlineCarDriver:0,address:""},
				labelStyle: {color: '#808080',fontSize: '30rpx'},
				showCaseDate:false,
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				subStyle: {height: '86rpx',backgroundColor: '#327BF0'},
				rules: {
					// party: [{ required: true, message: '请输入当事人', trigger: 'blur' }],
					companyName: [{ required: true, message: '请填写单位名称', trigger: 'blur' }],
					companyAddress: [{ required: true, message: '请填写单位地址', trigger: 'change' }],
					companyCode: [{ required: true, message: '请填写单位统一社会信用代码', trigger: 'blur' }],
					// identityCard: [{ required: true, message: '请输入身份证', trigger: 'blur' }],
					caseTime: [{ required: true, message: '请选择案发时间', trigger: 'change' }],
					summaryId: [{ required: true, message: '请选择案由', trigger: 'change' }],
					phone: [
						{ required: true, message: '请输入当事人联系电话', trigger: 'blur' },
						// {validator: (rule, value, callback) => {return this.$u.test.mobile(value);},message: '手机号码不正确',trigger: ['change','blur'],},
					],
					address: [{ required: true, message: '请选择地址', trigger: 'change' }],
					punishMoney: [{ required: true,type:'number',message: '请输入金额', trigger: 'blur' },{validator: (rule, value, callback) => {return this.$u.test.number(value);},message: '金额输入不正确',trigger: ['change','blur'],}],
					caseContent:[{ required: true, message: '请输入立案简介', trigger: 'change' }],
					remark:[{ required: true, message: '请输入处罚结果', trigger: 'change' }],
					summaryName:[{ required: true, message: '请选择案由', trigger: 'change' }],
					userName:[{ required: true, message: '请选择主办人', trigger: 'change' }],
				},
				happenData: {
					tableName: 'case_punishment',
					status: 2
				},
				happenFile:[],
				resData: {
					tableName: 'case_punishment',
					status: 9
				},
				resFile: [],
				userList: [],
				showUser: false
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			handleConfirm(e) {
				const { value: userId, label: userName } = e[0]
				this.form = { ...this.form, userId, userName }
			},
			handleContentConfirm() {
				const text = `${this.form.caseTime || ''}，金华市婺城区综合行政执法局站前中队执法队员${this.form.userName ? this.form.userName + ',' : ''}${this.form.userNames || ''}巡查中发现在${this.form.address || ''}存在${this.form.summaryName || ''}。经初步查实，该行为是${this.form.party || ''}所为。`
				this.form = { ...this.form, caseContent: text }
			},
			handCaseTime(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, caseTime: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			business(){
				let _this = this
				uni.scanCode({
				    success(res) {
						_this.$u.api.shopScanCode({},res.result).then(ress=>{
							console.log(ress.data);
							let data = ress.data
							_this.form  = {..._this.form,companyCode:data.bindCode,companyName:data.shopName,companyAddress:`${data.city}+${data.area}+${data.address}`}
						})
				    },
					fail(err){
						uni.showToast({
							title: '扫码失败，请重试',
							icon: 'err',
							position: 'bottom'
						})
						console.log(err);
					}
				});
			},
			handleChooseHandleUser(type) {
				let params = {}
				if (this.form[type]) {
					params.defaultCheckedKeys = this.form[type]
					params.defaultExpandedKeys = this.form[type]
				}
				if(type == 'summaryType') params.name = 'anyou'
				if(type == 'userIds') params.showRadio = 0
				params.type = type
				this.$u.route({ url: 'pages/common/selectUser/selectUser', params })
			},
			setUserData(checks,type) {
				// 选择好人员后的回调
				let checkData = {}
				if(checks.length == 1){
					checkData = checks[0]
				}else{
					checks.map((v,i)=>{
						if(i==0) {
							checkData.label = v.label+''
							checkData.id = v.id+''
						}else{
							checkData.label += ',' + v.label+''
							checkData.id += ',' + v.id+''
						}
					})
				}
				if(type == 'userIds'){
					if (checkData) this.form = { ...this.form, userNames: checkData.label, userIds: checkData.id }
				}else if(type == 'summaryType'){
					if (checkData) this.form = { ...this.form, summaryName: checkData.label, summaryId: checkData.id }
				}else {
					if (checkData) this.form = { ...this.form, userName: checkData.label, userId: checkData.id }
				}
			},
			handleChooseLocation(type) {
				uni.chooseLocation({
					latitude: 29.110764,
					longitude: 119.635857,
					geocode: true,
					success: res => {
						const { address, longitude, latitude } = res
						const lnglat = gps.gcj_decrypt(parseFloat(latitude), parseFloat(longitude))
						if(type == 'zz') return this.form = { ...this.form, companyAddress:address }
						this.form = { ...this.form, address: "浙江省金华市婺城区城北街道" + address, longitude: lnglat.lng, latitude: lnglat.lat }
					},
					fail: () => {
						uni.showToast({
							title: '地图打开失败',
							icon: 'none',
							position: 'bottom'
						})
					}
				})
			},
			handleSubmit(state) {
				if(state != 9) return this.send(state)
				this.$refs.uForm.validate(valid => {
					if (valid) {
						// 图片验证，没有图片不通过验证
						if (!this.$refs.happenfile.lists.length) {
							this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
							return
						}
						if (!this.$refs.resfile.lists.length) {
							this.$refs.uTips.show({ title: '请上传处罚结果图片', type: 'error', duration: '2300' })
							return
						}
						this.send(state)
					}
				});
			},
			send(state){
				const params = { ...this.form, status: state }
				// 开始上传
				this.$loading('数据上传中')
				this.$u.api.persuasionEdit(params).then(res => {
					console.log(res);
					// 遍历列表，查询是否有未上传的图片
					const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
					const resUploadFile = this.$refs.resfile.lists.some(item => item.progress !== 100)
					this.happenData.businessId = this.form.punishmentId
					if (uploadFile || resUploadFile) {
						this.$loading('图片上传中')
						this.$refs.happenfile.upload()
					} else {
						uni.showToast({title: '修改成功'})
						uni.hideLoading()
						this.$implement()
					}
				}).catch((err) => {
					console.log(err);
					uni.hideLoading()
				})
			},
			handleRemove(index, lists) {
				const fileInfo = lists[index]
				const fileId = fileInfo.url.split('?id=')[1]
				if (fileId) {
						return new Promise((resolve, reject) => {
							uni.showModal({
								title: '提示',
								content: '删除后将无法恢复，是否确认删除？',
								success: ({ confirm }) => {
									if (confirm) {
										this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
									} else {
										reject()
									}
								}
							})
						})
				} else {
					return true
				}
			},
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				if (!isFail && this.happenData.status == 9) {
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					this.$implement()
				} else if (!isFail) {
					this.$loading('图片上传中')
					this.happenData.status = 9
					this.$refs.resfile.upload()
				}
			},
			handleError() {
				// 文件上传失败，弹出提示是否重新上传
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					content: '图片上传失败，是否重新上传？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading('图片上传中')
							this.$refs.happenfile.reUpload()
						} else {
							this.$implement({ immediately: true })
						}
					}
				})
			},

		},
		async onLoad(params) {
			this.$loading()
			await this.$u.api.selectUserByPostKey({ postKeys: "zfzdz" }).then(res => {
				this.userList = res.data.map(item => {
					return { value: item.userId, label: item.nickName }
				})
			})
			if(params.id){
				Promise.all([
					this.$u.api.getPersuasion({}, params.id),
					this.$u.api.getFileList({ tableName: 'case_punishment', businessId: params.id }),
				]).then(resAry => {
					const formData = resAry[0].data
					const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
					this.form = { ...formData, lnglat }
					console.log(resAry[0]);
					resAry[1].rows.map(item => {
						if (item.status == 2) {
							this.happenFile.push({ url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` })
						} else if (item.status == 9) {
							this.resFile.push({ url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` })
						}
					})
					uni.hideLoading()
				}).catch((err) => {
					console.log(err);
					uni.hideLoading()
					uni.showToast({title: '加载失败', icon: 'error'})
				})
			}else{
				const timestamp = new Date().getTime()
				const caseTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = {...this.form,caseTime, userName: this.vuex_username, userId: this.vuex_id }
				uni.hideLoading()
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.pos-r {
	height: 50rpx;
	line-height: 50rpx;
	font-size: 24rpx;
	background-color: #2979ff;
	border-radius: 10rpx;
	position: absolute;
	top: 30rpx;
	right: 0;
	color: #FFFFFF;
	padding: 0 15rpx;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
.w-form ::v-deep .u-form-item--left{
	flex:.5 0 230rpx !important;
}
</style>

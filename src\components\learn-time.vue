<template>
	<view class="learn-time u-border-bottom u-border-top u-flex u-col-center">
		<view class="u-flex-1">
			<u-icon name="info-circle u-m-r-20"></u-icon>
			<text>{{ time | date('hh:MM:ss') }}</text>
		</view>
		<u-button type="error" plain size="mini" @click="handleClick">结束学习</u-button>
	</view>
</template>

<script>
	export default {
		name:"learn-time",
		props: {
			learnContent: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				startTamp: 0,
				startTime: '',
				endTime: '',
				time: 0,
				timer: null,
				isEndStudy: false
			};
		},
		mounted() {
			this.setTime()
			this.interTime()
		},
		destroyed() {
			clearInterval(this.timer)
		},
		methods: {
			setTime() {
				const timestamp = new Date().getTime()
				const time = this.$u.timeFormat(timestamp, 'yyyy/m/dd 00:00:00')
				this.startTamp = timestamp
				this.startTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.time = new Date(time).getTime()
			},
			interTime() {
				if (this.timer) clearInterval(this.timer)
				this.timer = setInterval(() => {
					this.time += 1000
				}, 1000)
			},
			handleClick() {
				uni.showModal({
					title: '提示',
					content: '是否确认结束学习？并退出',
					success: ({ confirm }) => {
						if (confirm) {
							this.addLearnRecord()
						}
					}
				})
			},
			addLearnRecord() {
				this.$loading()
				const timestamp = new Date().getTime()
				const endTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.$u.api.addLearn({
					userId: this.vuex_id,
					startTime: this.startTime,
					endTime, 
					learnContent: this.learnContent,
					deptId: this.vuex_deptId,
					deptName: this.vuex_deptName,
				}).then(res => {
					uni.hideLoading()
					uni.showToast({ title: '操作成功' })
					this.$implement()
				}).catch(() => {
					uni.hideLoading()
				})
			}
		}
	}
</script>

<style lang="scss">
.learn-time {
	position: fixed;
	top: 0;
	/* #ifdef H5 */
	top: 44px;
	/* #endif */
	left: 0;
	width: 100%;
	height: 40px;
	font-size: 36rpx;
	// font-weight: 700;
	background-color: #fff;
	z-index: 999;
	padding: 0 30rpx;
}
</style>

<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<!-- <u-form-item label="标题:" prop="title">
					<u-input v-model="form.title" placeholder="请输入标题" disabled />
				</u-form-item> -->
				<u-form-item label="巡查人员:" prop="userName">
					<u-input v-model="form.userName" type="text" placeholder="巡查人员" disabled />
				</u-form-item>
				<u-form-item label="辅助人员:" prop="userNames" >
					<u-input v-model="form.userNames" type="popup" placeholder="辅助人员"/>
				</u-form-item>
				<u-form-item label="巡查时间:" prop="inspectionTime">
					<u-input v-model="form.inspectionTime" type="text" placeholder="请选择巡查时间" disabled />
				</u-form-item>
				<u-form-item label="当事人:" prop="party">
					<u-input v-model="form.party" type="text" placeholder="请输入当事人姓名" disabled />
				</u-form-item>
				<u-form-item label="车辆车牌:" prop="carNo">
					<u-input v-model="form.carNo" type="text" placeholder="请输入车辆车牌" disabled />
				</u-form-item>
				<u-form-item label="车辆品牌:" prop="models">
					<u-input v-model="form.models" type="text" placeholder="请输入车辆品牌" disabled />
				</u-form-item>
				<u-form-item label="车辆类型:" prop="carTypeName">
					<u-input v-model="form.carTypeName" type="text" placeholder="请选择车辆类型" disabled />
				</u-form-item>
				<u-form-item label="营运公司:" prop="operationCompany">
					<u-input v-model="form.operationCompany" type="text" placeholder="请输入营运公司" />
				</u-form-item>
				<u-form-item label="发生地址:" prop="address">
					<u-input v-model="form.address" type="popup" placeholder="请选择地址" disabled />
				</u-form-item>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="是否有问题:" prop="isProblem" required>
					<u-radio-group v-model="form.isProblem" disabled>
						<u-radio name="1" >有</u-radio>
						<u-radio name="0" >无</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="问题类型:" prop="problemTypeName" v-if="form.isProblem == 1" required>
					<u-input v-model="form.problemTypeName" type="popup" placeholder="请选择问题类型" />
				</u-form-item>
				<u-form-item label="检查概况:" prop="content" label-position="top" :border-bottom="false">
					<u-input v-model="form.content" type="textarea" maxlength="300" height="140" placeholder="请输入检查概况..." disabled />
				</u-form-item>
				<u-upload
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:auto-upload="false"
					:form-data="happenData"
				  :size-type="['compressed']"
					:file-list="happenFile"
					:deletable="false"
					:customBtn="true"
				></u-upload>
			</view>
		</u-form>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				showCarType: false,
				transportCarType: [],
				showInspectionType: false,
				inspectionTypeList: [],
				showInspectionTime: false,
				isShowFTaxiType: false,
				isShowSTaxiType: false,
				form: {address:"浙江省金华市婺城区城北街道"},
				happenData: {
					tableName: 'case_transport',
					status: 1
				},
				happenFile: [],
				showList: false,
				problemTypeList: []
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			}
		},
		async onLoad(params) {
			// 获取车辆类型
			this.$loading('获取系统配置数据')
			await this.$u.api.getDicts({}, 'transport_car_type').then(res => {
				this.transportCarType = res.data.map(item => {
					item.text = item.dictLabel
					return item
				})
				uni.hideLoading()
			}).catch(() => {
				uni.hideLoading()
			})
			// 获取业务数据
			if (params.id) {
				this.$loading()
				Promise.all([
					this.$u.api.getTransport({}, params.id),
					this.$u.api.getFileList({ tableName: 'case_transport', status: 1, businessId: params.id })
				]).then(resAry => {
					const formData = resAry[0].data
					const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
					this.form = { ...formData, lnglat }
					this.happenFile = resAry[1].rows.map(item => {
						return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
					})
					uni.hideLoading()
				}).catch(() => {
					uni.hideLoading()
				})
			} else {
				const timestamp = new Date().getTime()
				const inspectionTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = { inspectionTime, userName: this.vuex_username, userId: this.vuex_id }
			}
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="150" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="督查人:" prop="userName" required>
					<u-input v-model="form.userName" type="popup" placeholder="督查人" />
				</u-form-item>
				<u-form-item label="事件描述:" prop="content" label-position="top" :border-bottom="false" required>
					<u-input v-model="form.content" type="textarea" placeholder="请输入事件描述" />
				</u-form-item>

				<view class="u-rela">
					<u-form-item label="考核条款:" prop="checkStandardName" label-position="top" required>
						<u-input v-model="form.checkStandardName" type="textarea" placeholder="请选择考核条款" />
					</u-form-item>
					<view class="pos-r" @click="handleChooseStandar">选择项目</view>
					<!-- <u-button class="pos-r" size="mini" type="primary" ></u-button> -->
				</view>
				<u-form-item label="考核标准:" prop="checkStandardContent">
					<u-input v-model="form.checkStandardContent" type="popup" placeholder="请选择考核标准" />
				</u-form-item>
				<u-form-item label="积分类型:" prop="type" required>
					<u-radio-group v-model="form.type" disabled @change="typeRadioGroupChange">
						<u-radio name="1">扣分</u-radio>
						<u-radio name="0">加分</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="积分值:" prop="checkStandardScore" required>
					<u-input v-model="form.checkStandardScore" type="number" placeholder="请输入积分值" />
				</u-form-item>
				<!-- <u-form-item label="考核队员:" prop="checkUserNames" required>
					<u-input v-model="form.checkUserNames" type="popup" placeholder="考核队员" />
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('userIds')">选择人员</u-button>
				</u-form-item> -->
			</view>

			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="考核时间:" prop="happenTime" required>
					<u-input v-model="form.happenTime" type="popup" placeholder="请选择考核时间" @click="showHappenTime = true"  />
					<u-icon name="calendar"  @click="showHappenTime = true"  size="40"></u-icon>
					<u-picker v-model="showHappenTime" mode="time" :default-time="form.happenTime" :params="params"  @confirm="confirmHappenTime"></u-picker>
				</u-form-item>
				<u-form-item label="地址:" prop="address" required>
					<u-input v-model="form.address" type="text" placeholder="请选择地址" />
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseAddress">选择地址</u-button>
				</u-form-item>
				<!-- <u-form-item label="备注:" prop="remark" label-position="top" :border-bottom="false">
					<u-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
				</u-form-item> -->
				<!-- <u-upload


					:file-list="fileList"
					:deletable="false"
					:customBtn="true"

				></u-upload> -->
				<u-upload
					:file-list="fileList"
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
					:size-type="['compressed']"
					:before-remove="handleRemove"
					@on-uploaded="handleAllUpload"
					@on-error="handleError"
				></u-upload>
				<!-- <u-upload
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
					:size-type="['compressed']"
					@on-uploaded="handleAllUpload"
					@on-error="handleError"
				></u-upload> -->
			</view>

			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="班长:" prop="groupUserName">
					<u-input v-model="form.groupUserName" ref="monitor" type="popup" placeholder="班长" />
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser({showRadio: 1, type: 'monitor'})">选择人员</u-button>
				</u-form-item>
				<u-form-item label="责任人:" prop="checkUserNames">
					<u-input v-model="form.checkUserNames" ref="responsible" type="popup" placeholder="责任人" />
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser({showRadio: 0, type: 'responsible'})">选择人员</u-button>
				</u-form-item>
			</view>
		</u-form>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex">
			<u-button class="u-flex-1 u-m-r-20" type="primary" shape="circle" :loading="aloading" :custom-style="subStyle" @click="handleSubmit(1)">暂存</u-button>
			<u-button class="u-flex-1" type="primary" shape="circle" :loading="aloading" :custom-style="subStyle" @click="handleOver">提交</u-button>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	import gps from '@/common/gps.js'
	export default {
		data() {
			return {
				aloading: false,
				fileList: [],
				// 存储班长/责任人
				role:'',
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				form: {address:"浙江省金华市婺城区城北街道"},
				happenData: {
					tableName: 'supervision_check_record',
					status: 1
				},
				rules: {
					// checkUserNames: [{ required: true, message: '请选择督查人', trigger: 'change' }],
					type: [{ required: true, message: '积分类型', trigger: 'change' }],
					content: [{ required: true, message: '请输入事件描述', trigger: 'blur' }],
					checkStandardName: [{ required: true, message: '请选择考核条款', trigger: ['change', 'blur'] }],
					checkStandardScore: [{
							validator: (rule, value, callback) => {
								return !!value;
							},
							message: '请填写积分值',
							trigger: ['change','blur'],
						}],
					happenTime: [{ required: true, message: '请选择考核时间', trigger: 'change' }],
					address: [{ required: true, message: '请选择地址', trigger: 'change' }],
					groupUserName:[{
						validator: (rule, value, callback) => {
							return !!(value || this.form.checkUserNames);
						},
						message: '班长和责任人其中一个必填',
						trigger: ['change','blur'],
					}],
					checkUserNames:[{
						validator: (rule, value, callback) => {
							return !!(value || this.form.groupUserName);
						},
						message: '班长和责任人其中一个必填',
						trigger: ['change','blur'],
					}]
				},
				showHappenTime: false
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				if (!isFail) {
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					this.$implement()
				}
			},
			handleRemove(index, lists) {
				const fileInfo = lists[index]
				const fileId = fileInfo.url.split('?id=')[1]
				if (fileId) {
						return new Promise((resolve, reject) => {
							uni.showModal({
								title: '提示',
								content: '删除后将无法恢复，是否确认删除？',
								success: ({ confirm }) => {
									if (confirm) {
										this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
									} else {
										reject()
									}
								}
							})
						})
				} else {
					return true
				}
			},
			handleError() {
				// 文件上传失败，弹出提示是否重新上传
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					content: '图片上传失败，是否重新上传？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading('图片上传中')
							this.$refs.happenfile.reUpload()
						} else {
							this.$implement({ immediately: true })
						}
					}
				})
			},
			handleChooseAddress() {
				uni.chooseLocation({
					latitude: 29.110764,
					longitude: 119.635857,
					geocode: true,
					success: res => {
						const { address, longitude, latitude } = res
						const lnglat = gps.gcj_decrypt(parseFloat(latitude), parseFloat(longitude))
						this.form = { ...this.form, address, longitude: lnglat.lng, latitude: lnglat.lat }
					},
					fail: () => {
						uni.showToast({
							title: '地图打开失败',
							icon: 'none',
							position: 'bottom'
						})
					}
				})
			},
			confirmHappenTime(time){
				const { year, month, day, hour, minute, second} = time
				// this.form = { ...this.form, businessStartTime: `${year}-${month}-${day}` }
				this.form = { ...this.form, happenTime: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleChooseStandar() {
				let params = { }
				if (this.form['checkStandardId']) {
					params.defaultCheckedKeys = this.form['checkStandardId']
					params.defaultExpandedKeys = this.form['checkStandardId']
				}
				this.$u.route({ url: 'pages/common/selectStandard/selectStandard', params })
			},
			setStandard(checks) {
				let names = [], ids = [], content = [], type = []
				if (checks && checks.length) checks.forEach(item => {
					names.push(item.title)
					content.push(item.content)
					ids.push(item.checkStandardId)
					type.push(item.type)
				})
				this.form = { ...this.form, checkStandardName: names.join(','), checkStandardContent: content.join(','), checkStandardId: ids.join(','), type: type.join(',') }
			},
			handleChooseHandleUser(obj) {
				let params = {...obj}
				this.role = params.type
				if (params.type == 'monitor' && this.form.groupUserId) {
					params.defaultCheckedKeys = this.form['groupUserId']
					params.defaultExpandedKeys = this.form['groupUserId']
				} else if (params.type == 'responsible' && this.form.checkUserIds) {
					params.defaultCheckedKeys = this.form['checkUserIds']
					params.defaultExpandedKeys = this.form['checkUserIds']
				}
				this.$u.route({ url: 'pages/common/selectUser/selectUser', params })
			},
			setUserData(checks,type,parent) {
				let names = [], ids = [], parents = {}
				if (checks && checks.length) checks.forEach(item => {
					names.push(item.label)
					ids.push(item.id)
					parents = parent
				})

				if (type == 'monitor') {
					this.form = { ...this.form, groupUserName: names.join(','), groupUserId: ids.join(',') ,checkUserNames: '', checkUserIds:'', deptId: parents.data.id, deptName: parents.label}
				} else if (type == 'responsible') {
					this.form = { ...this.form, checkUserNames: names.join(','), checkUserIds: ids.join(','), deptId: parents.data.id, deptName: parents.label,groupUserName: '', groupUserId:''}
				}
			},
			typeRadioGroupChange(idx) {
				this.form = { ...this.form, type: idx }
			},
			handleSubmit(status) {
				if(this.form.status){
					if(this.vuex_id != this.form.userId) return this.mToase('您不是督查人，没有操作权限！')
				}
				this.aloading = true
				this.$loading()
				const params = { ...this.form, status }
				let methodsFn = this.form.checkRecordId != null ? this.$u.api.updateStandard : this.$u.api.addStandard
				methodsFn(params).then(res => {
					const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
					this.happenData.businessId = this.form.checkRecordId || res.data.checkRecordId
					if (uploadFile) {
						this.$loading('图片上传中')
						this.$refs.happenfile.upload()
					} else {
						uni.hideLoading()
						uni.showToast({title: '操作成功'})
						this.$implement()
					}
				}).catch(() => {
					uni.hideLoading()
					this.aloading = false
				})
			},
			handleOver() {
				if(this.form.status){
					if(this.vuex_id != this.form.userId) return this.mToase('您不是督查人，没有操作权限！')
				}
				this.$refs.uForm.validate(valid => {
					// 图片验证，没有图片不通过验证
					if (!this.$refs.happenfile.lists.length) {
						this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
						return
					}

					if (valid) {
						if(this.form.checkUserNames){
							this.handleSubmit(3)
						}else{
							this.handleSubmit(2)
						}
					}
				})

			}
		},
		async onLoad(params) {
			// console.log(this.vuex_postKeys)
			if (params.id) {
				this.$loading()
				const [standardRes, fileRes] = await Promise.all([
					this.$u.api.getStandard({}, params.id),
					this.$u.api.getFileList({ tableName: 'supervision_check_record', businessId: params.id })
				])

				/* 数据请求完毕 */
				uni.hideLoading()
				if (standardRes.code == 200 && fileRes.code == 200) {
					/* 表单数据 */
					this.form = standardRes.data
					/* 文件数据 */
					this.fileList = fileRes.rows.map(item => {
            return { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
					})
				}
			} else {
				const timestamp = new Date().getTime()
				const happenTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = { type: '1', happenTime, userId:this.vuex_id, userName: this.vuex_nickName }
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.pos-r {
	height: 50rpx;
	line-height: 50rpx;
	font-size: 24rpx;
	background-color: #2979ff;
	border-radius: 10rpx;
	position: absolute;
	top: 30rpx;
	right: 0;
	color: #FFFFFF;
	padding: 0 15rpx;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>

const api = process.env["VUE_APP_BASE_API"];
const apiList = [
	{ url: 'getAPPList', method: 'get', fnName: 'getAPPList' },
	{ url: 'system/dict/data/type/', method: 'get', fnName: 'getDicts' }, // 获取数据字典
	{ url: 'key', method: 'get', fnName: 'getKey' }, // 获取登录密钥
	{ url: 'user/register', method: 'post', fnName: 'register' },
	{ url: 'login', method: 'post', fnName: 'login' },
	{ url: 'logout', method: 'post', fnName: 'logout' },
	{ url: 'getInfo', method: 'get', fnName: 'getInfo' },
	{ url: 'captchaImage', method: 'get', fnName: 'getCaptchaImage' },
	{ url: 'getInfo', method: 'get', fnName: 'getInfo' },
	{ url: 'system/dept/deptUsertreeselect', method: 'get', fnName: 'deptUsertreeselect' },//人员树
	{ url: 'police/case/one/add', method: 'post', fnName: 'caseAdd' }, // 新增四位一体
	{ url: 'police/case/one/edit', method: 'post', fnName: 'caseEdit' }, // 新增四位一体
	{ url: 'police/case/one/list', method: 'get', fnName: 'getCaseList' }, // 获取四位一体列表
	{ url: 'police/case/one/myCases', method: 'get', fnName: 'getMyCaseList' }, // 获取我的四位一体列表
	{ url: 'police/case/one/revoke', method: 'post', fnName: 'caseRevoke' }, // 驳回四位一体列
	{ url: 'police/case/one/', method: 'get', fnName: 'getCaseDetail' }, // 获取四位一体详情
	{ url: 'system/file/list', method: 'get', fnName: 'getFileList' }, // 获取已上传图片
	{ url: 'system/file/remove/', method: 'post', fnName: 'deleteFileList' }, // 删除已上传图片
	{ url: 'system/dict/data/list', method: 'get', fnName: 'dictList' }, // 类型字典
	{ url: 'business/summary/treeselect', method: 'get', fnName: 'summaryList' }, // 案由
	{ url: 'business/shop/scanCode/', method: 'get', fnName: 'shopScanCode' }, // 二维码
	{ url: 'system/dept/treeselect', method: 'get', fnName: 'deptTreeselect' }, // 部门树
	{ url: 'system/dept/', method: 'get', fnName: 'getDeptArea' }, // 获取部门打卡范围

	{ url: 'business/inspection/list', method: 'get', fnName: 'inspectionList' }, // 巡查发现列表
	{ url: 'business/inspection/myCases', method: 'get', fnName: 'myInspectionList' }, // 我的巡查发现列表
	{ url: 'business/inspection/', method: 'get', fnName: 'getInspection' }, // 单个巡查发现列表
	{ url: 'business/inspection/add', method: 'post', fnName: 'inspectionAdd' }, // 新增巡查发现
	{ url: 'business/inspection/edit', method: 'post', fnName: 'inspectionEdit' }, // 编辑巡查发现

	{ url: 'business/punishment/add', method: 'post', fnName: 'persuasionAdd' }, // 添加简易程序
	{ url: 'business/punishment/myCases', method: 'get', fnName: 'myPersuasionList' }, // 添加简易程序
	{ url: 'business/punishment/', method: 'get', fnName: 'getPersuasion' }, // 单个查询简易程序
	{ url: 'business/punishment/edit', method: 'post', fnName: 'persuasionEdit' }, // 编辑简易程序
	{ url: 'business/punishment/list', method: 'get', fnName: 'persuasionList' }, // 简易程序列表

	{ url: 'business/case/general/add', method: 'post', fnName: 'generalAdd' }, // 添加一般案件
	{ url: 'business/case/general/myCases', method: 'get', fnName: 'myGeneralList' }, // 添加一般案件
	{ url: 'business/case/general/', method: 'get', fnName: 'getGeneral' }, // 单个查询一般案件
	{ url: 'business/case/general/edit', method: 'post', fnName: 'generalEdit' }, // 编辑一般案件
	{ url: 'business/case/general/list', method: 'get', fnName: 'generalList' }, // 一般案件列表

	{ url: 'business/case/dynamic/list', method: 'get', fnName: 'dynamicList' }, // 工作动态列表
	{ url: 'business/case/dynamic/add', method: 'post', fnName: 'addDynamic' }, // 新增工作动态
	{ url: 'business/case/dynamic/edit', method: 'post', fnName: 'editDynamic' }, // 编辑工作动态
	{ url: 'business/case/dynamic/', method: 'get', fnName: 'getDynamic' }, // 获取工作动态详情
	{ url: 'business/case/dynamic/delete/', method: 'post', fnName: 'removeDynamic' }, // 删除工作动态

	{ url: 'business/violation/record/myCases', method: 'get', fnName: 'violationList' }, // 柔性执法列表
	{ url: 'business/violation/record/', method: 'get', fnName: 'getViolation' }, // 获取柔性执法详情
	{ url: 'business/violation/record/add', method: 'post', fnName: 'addViolation' }, // 新增工作动态
	{ url: 'business/violation/record/edit', method: 'post', fnName: 'editViolation' }, // 编辑工作动态

	{ url: 'business/sidewalk/list', method: 'get', fnName: 'sidewalkList' }, // 人行道违停列表
	{ url: 'business/sidewalk/', method: 'get', fnName: 'getSidewalk' }, // 获取人行道违停详情
	{ url: 'business/sidewalk/add', method: 'post', fnName: 'addSidewalk' }, // 新增人行道违停
	{ url: 'business/sidewalk/edit', method: 'post', fnName: 'editSidewalk' }, // 编辑人行道违停

	{ url: 'workflow/leave/list', method: 'get', fnName: 'leaveList' }, // 我的请假列表
	{ url: 'workflow/leave', method: 'post', fnName: 'addLeave' }, // 新增请假
	{ url: 'workflow/leave/', method: 'get', fnName: 'getLeave' }, // 获取请假详情
	{ url: 'historyFromData/ByInstanceId/', method: 'get', fnName: 'getLeavApprove' }, // 获取请假详情页面的审批详情

	{ url: 'workflow/leave/listWait', method: 'get', fnName: 'approveList' }, // 请假审批列表
	{ url: 'task/formDataShow/', method: 'get', fnName: 'formDataShow' }, // 查询请假审批表单类型
	{ url: 'task/formDataSave/', method: 'post', fnName: 'formDataSave' }, // 提交请假审批

	{ url: 'business/violation/record/generateByCase', method: 'get', fnName: 'getExam' }, // 生成试卷id,名称，二维码
	{ url: 'business/violation/record/getQrCode/', method: 'get', fnName: 'getExamDetail' }, // 通过试卷id获取试卷二维码
	{ url: 'business/violation/record/infoByCase', method: 'get', fnName: 'getExamAnswer' }, // 通过试卷id查看试卷详情

	{ url: 'business/capture/add', method: 'post', fnName: 'captureAdd' }, // 添加监控抓拍
	{ url: 'business/capture/', method: 'get', fnName: 'getcapture' }, // 单个查询
	{ url: 'business/capture/edit', method: 'post', fnName: 'captureEdit' }, // 编辑
	{ url: 'business/capture/list', method: 'get', fnName: 'captureList' }, // 列表
	{ url: 'business/capture/myCases', method: 'get', fnName: 'myCaptureList' }, // 我的列表
	{ url: 'business/capture/myCasesWait', method: 'get', fnName: 'myCasesWait' }, // 监控抓拍待处理列表
	{ url: 'business/capture/revoke', method: 'post', fnName: 'casesrevoke' }, // 撤回

	{ url: 'business/transport/myCases', method: 'get', fnName: 'getMyTransportList' }, // 获取我的日常巡查列表
	{ url: 'business/transport/', method: 'get', fnName: 'getTransport' }, // 获取日常巡查详情
	{ url: 'business/transport/list', method: 'get', fnName: 'getTransportList' }, // 获取日常巡查列表
	{ url: 'business/transport/add', method: 'post', fnName: 'addTransport' }, // 新增日常巡查
	{ url: 'business/transport/edit', method: 'post', fnName: 'editTransport' }, // 编辑日常巡查
	{ url: 'business/transport/remove/', method: 'post', fnName: 'removeTransport' }, // 删除日常巡查

	{ url: 'business/tout/list', method: 'get', fnName: 'toutList' }, // 列表黄牛信息
	{ url: 'business/tout/', method: 'get', fnName: 'getTout' }, // 黄牛信息
	{ url: 'business/tout/add', method: 'post', fnName: 'toutAdd' }, // 新增黄牛信息
	{ url: 'business/tout/edit', method: 'post', fnName: 'toutEdit' }, // 编辑黄牛信息

	{ url: 'business/case/tout/list', method: 'get', fnName: 'caseToutList' }, // 列表黄牛处置
	{ url: 'business/case/tout/', method: 'get', fnName: 'getCaseTout' }, // 黄牛处置
	{ url: 'business/case/tout/add', method: 'post', fnName: 'caseToutAdd' }, // 新增黄牛处置
	{ url: 'business/case/tout/edit', method: 'post', fnName: 'caseToutEdit' }, // 编辑黄牛处置

	{ url: 'business/union/add', method: 'post', fnName: 'unionAdd' }, // 新增任务发起
	{ url: 'business/union/list', method: 'get', fnName: 'unionList' }, // 列表
	{ url: 'business/union/', method: 'get', fnName: 'getUnion' }, // 获取
	{ url: 'business/union/edit', method: 'post', fnName: 'unionEdit' }, // 编辑
	{ url: 'business/union/detailList', method: 'get', fnName: 'unionDetailList' }, // 获取子任务

	{ url: 'business/traffic/capture/list', method: 'get', fnName: 'trafficList' }, // 列表电子抓拍
	{ url: 'business/traffic/capture/', method: 'get', fnName: 'gettraffic' }, // 获取
	{ url: 'business/traffic/capture/add', method: 'post', fnName: 'addTraffic' }, // 获取
	{ url: 'business/traffic/capture/edit', method: 'post', fnName: 'editTraffic' }, // 获取



	{ url: 'business/shop/add', method: 'post', fnName: 'shopAdd' }, // 新增店铺信息
	{ url: 'business/shop/list', method: 'get', fnName: 'shopList' }, // 列表
	{ url: 'business/shop/', method: 'get', fnName: 'getShop' }, // 获取
	{ url: 'business/shop/edit', method: 'post', fnName: 'shopEdit' }, // 编辑
	{ url: 'business/punchIn/config/list', method: 'get', fnName: 'getPunchInConfig' }, // 获取打卡配置
	{ url: 'business/punchIn/config/getByDeptId/', method: 'get', fnName: 'getPunchInConfigByDept' }, // 根据部门ID获取打卡配置
	{ url: 'business/punchIn/add', method: 'post', fnName: 'addPunchIn' }, // 新增打卡
	{ url: 'business/punchIn/getOfficeTodayInfo/', method: 'get', fnName: 'getOfficeTodayInfo' }, // 获取机关人员打卡详情
	{ url: 'business/punchIn/getGridTodayInfo/', method: 'get', fnName: 'getGridTodayInfo' }, // 获取网格人员打卡详情
	{ url: 'business/punchIn/edit', method: 'post', fnName: 'editPunchIn' }, // 修改打卡状态

	{ url: 'business/law/list', method: 'get', fnName: 'getLawList' }, // 获取法律法规列表数据
	{ url: 'business/law/', method: 'get', fnName: 'getLawDetail' }, // 获取法律法规详情数据

	{ url: 'business/case/message/list', method: 'get', fnName: 'getMsgList' }, // 获取消息列表
	{ url: 'business/case/message/edit', method: 'post', fnName: 'editMsg' }, // 修改消息列表
	{ url: 'business/case/message/editAll', method: 'post', fnName: 'editAllMsg' }, // 修改全部消息

	{ url: 'system/user/', method: 'get', fnName: 'getUserInfo' }, // 获取个人信息
	{ url: 'system/user/profile', method: 'get', fnName: 'getUserProfile' }, // 获取个人信息
	{ url: 'system/user/profile/updatePwd', method: 'post', fnName: 'updatePwd' }, // 修改登录密码

	{ url: 'business/case/message/listMark', method: 'get', fnName: 'getlistMark' }, // 获取首页数字角标
	{ url: 'business/app/version/1', method: 'get', fnName: 'getAppVersion' }, // 获取当前app版本
	{ url: 'business/statistics/listRealTimeCase', method: 'get', fnName: 'listRealTimeCase' }, // 实时警情
	{ url: 'business/statistics/caseType', method: 'post', fnName: 'caseTypeCount' }, // 案件类型统计
	{ url: 'business/taxi/inOut/list', method: 'get', fnName: 'taxiInOut' }, // 出租车进出场情况
	{ url: 'business/punchIn/count', method: 'post', fnName: 'inCount' }, // 考勤打卡统计
	{ url: 'business/punchIn/toDayList', method: 'get', fnName: 'getTodayAttendance' }, // 今日考勤列表
	{ url: 'business/statistics/countVol', method: 'get', fnName: 'countVolCount' }, // 志愿者统计
	{ url: 'business/statistics/caseTrend', method: 'post', fnName: 'caseTrendCount' }, // 案件趋势

	{ url: 'business/licensing/list', method: 'get', fnName: 'licensingList' }, // 行政许可

	{ url: 'business/temporary/add', method: 'post', fnName: 'temporaryAdd' }, // 新增任务发起
	{ url: 'business/temporary/list', method: 'get', fnName: 'temporaryList' }, // 列表
	{ url: 'business/temporary/', method: 'get', fnName: 'getTemporary' }, // 获取
	{ url: 'business/temporary/edit', method: 'post', fnName: 'temporaryEdit' }, // 编辑

	{ url: 'business/recorder/listRecorderGps', method: 'post', fnName: 'listRecorderGps' }, // 人员定位列表

	/* 题库试卷 */
	{ url: 'business/exam/list', method: 'get', fnName: 'getExamList' }, // 获取试卷列表
	{ url: 'business/question/info', method: 'get', fnName: 'getQuestionInfo' }, // 获取试卷
	{ url: 'business/question/finish', method: 'post', fnName: 'finishQuestion' }, // 提交答案
	{ url: 'business/answer/list', method: 'get', fnName: 'answerList' }, // 常见问答

	/* 典型案例列表 */
	{ url: 'business/classic/case/list', method: 'get', fnName: 'getClassicCaseList' }, // 常见问答
	{ url: 'business/classic/case/', method: 'get', fnName: 'getClassicCaseDetail' }, // 常见问答

	{ url: 'business/learn/record/add', method: 'post', fnName: 'addLearn' }, // 新增学习记录

	{ url: 'business/car/taxi/list', method: 'get', fnName: 'getTaxiList' }, // 获取出租车信息列表
	{ url: 'business/car/taxi/', method: 'get', fnName: 'getTaxiDetail' }, // 获取出租车信息
	{ url: 'business/screen/listAllCase', method: 'get', fnName: 'getListAllCase' }, // 案卷分布
	{ url: 'oa/leader/task/list', method: 'get', fnName: 'getLeaderTaskList' }, // 领导交办列表
	{ url: 'oa/leader/task/add', method: 'post', fnName: 'addLeaderTask' }, // 新增领导交办
	{ url: 'oa/leader/task/edit', method: 'post', fnName: 'editLeaderTaskList' }, // 编辑领导交办
	{ url: 'oa/leader/task/', method: 'get', fnName: 'getLeaderTask' }, // 领导交办详情

	{ url: 'business/common/words/add', method: 'post', fnName: 'addWords' }, // 案卷分布
	{ url: 'business/common/words/edit', method: 'post', fnName: 'editWords' }, // 案卷分布
	{ url: 'business/common/words/remove/', method: 'post', fnName: 'removeWords' }, // 案卷分布
	{ url: 'business/common/words/list', method: 'get', fnName: 'getWordsList' }, // 案卷分布

	{ url: 'business/urge/listAll', method: 'get', fnName: 'getUrgelistAll' }, // 案卷查询
	{ url: 'business/chart/workReport', method: 'get', fnName: 'getWorkReport' }, // 案卷查询
	{ url: 'business/urge/myUrges', method: 'get', fnName: 'getMyUrges' }, // 案卷查询
	{ url: 'business/urge/list', method: 'get', fnName: 'getUrgesList' }, // 案卷查询
	{ url: 'business/urge/add', method: 'post', fnName: 'addUrges' }, // 案卷查询
	{ url: 'business/urge/remove/', method: 'post', fnName: 'removeUrges' }, // 案卷查询

	{ url: 'business/oa/score/record/count', method: 'get', fnName: 'getRecordCount' }, // 总分数
	{ url: 'business/oa/score/record/list', method: 'get', fnName: 'getScoreRecord' }, // 查看积分
	{ url: 'business/learn/record/list', method: 'get', fnName: 'getLearnRecord' }, // 查看学习时长

	{ url: 'business/notice/detail/list', method: 'get', fnName: 'getNoticeDetailList' }, // 查看未读的通知公告
	{ url: 'business/notice/', method: 'get', fnName: 'getNoticeDetail' }, // 查看通知公告
	{ url: 'business/notice/detail/edit', method: 'post', fnName: 'editNoticeDetail' }, // 查看通知公告

	{ url: 'business/fence/patrol/add', method: 'post', fnName: 'addFencePatrol' }, // 巡查新增
	{ url: 'system/user/selectUserByPostKey', method: 'post', fnName: 'selectUserByPostKey' }, // 人员

	/* 督查考核 */
	{ url: 'business/supervision/record/list', method: 'get', fnName: 'getRecordList' }, // 获取督查考核列表
	{ url: 'business/supervision/record/myCases', method: 'get', fnName: 'getMyRecordList' }, // 获取我的督查考核列表
	{ url: 'business/supervision/record/myCasesV2', method: 'get', fnName: 'getMyRecordListV2' }, // 获取申述列表
	{ url: 'business/supervision/standard/list', method: 'get', fnName: 'getStandardList' }, // 获取我的考核标准列表
	{ url: 'business/supervision/record/add', method: 'post', fnName: 'addStandard' }, // 新增考核标准
	{ url: 'business/supervision/record/', method: 'get', fnName: 'getStandard' }, // 获取考核标准详情
	{ url: 'business/supervision/record/edit', method: 'post', fnName: 'updateStandard' }, // 编辑考核标准
	{ url: 'business/appeal/record/add', method: 'post', fnName: 'addAppeal' }, // 添加申诉
	{ url: 'business/supervision/record/confirm', method: 'post', fnName: 'confirmAppeal' }, // 确认申诉
	{ url: 'business/appeal/record/myCases', method: 'get', fnName: 'getAppealList' }, // 获取申诉列表
	{ url: 'business/appeal/record/edit', method: 'post', fnName: 'updateAppeal' }, // 编辑申诉
	{ url: 'business/appeal/record/', method: 'get', fnName: 'getAppeal' }, // 获取申诉详情

	/* 代班 */
	{ url: 'business/substitute/record/list', method: 'get', fnName: 'getSubstituteList' }, // 获取代班列表
	{ url: 'business/substitute/record/', method: 'get', fnName: 'getSubstitute' }, // 根据id获取代班详情
	{ url: 'business/substitute/record/add', method: 'post', fnName: 'addSubstituteList' }, // 代班申请
	{ url: 'business/substitute/record/edit', method: 'post', fnName: 'updateSubstituteList' }, // 编辑代班申请
	{ url: 'business/substitute/record/editBySupervision', method: 'post', fnName: 'editBySupervision' }, // 代班审核

	/* 工作日志 */
	{ url: 'business/workLog/list', method: 'get', fnName: 'getWorkLogList' }, // 获取工作日志列表
	{ url: 'business/workLog/', method: 'get', fnName: 'getWorkLogDetail' }, // 获取工作日志详情
	{ url: 'business/workLog/edit', method: 'post', fnName: 'editWorkLog' }, // 编辑工作日志详情
	{ url: 'business/workLog/add', method: 'post', fnName: 'addWorkLog' }, // 新增工作日志详情

	/* 排班点位表 */
	{ url: 'system/user/list', method: 'get', fnName: 'getUserList' }, // 获取当前部门下所有人员
	{ url: 'business/hour/header/add', method: 'post', fnName: 'addHourHeader' }, // 新增排班点位表
	{ url: 'business/hour/header/edit', method: 'post', fnName: 'editHourHeader' }, // 更新排班点位表
	{ url: 'business/hour/header/', method: 'get', fnName: 'getHourHeader' }, // 获取排班点位表
	{ url: 'business/hour/header/list', method: 'get', fnName: 'getHourHeaderList' }, // 获取排班点位表


  /* 案件录入列表 */
	{ url: 'business/ajll/list', method: 'get', fnName: 'getAjList' },
]

// 此处第二个参数vm，就是我们在页面使用的this，你可以通过vm获取vuex等操作
const install = (Vue, vm) => {
	let apiObj = {}
	apiList.forEach(item => {
		let extraParams = item.extra ? item.extra : {};
		apiObj[item.fnName] = (params = {}, urlData = '') => {
			let dept = {}
			if (Array.isArray(params)) {
				return vm.$u[item.method](api + item.url + urlData, params)
			} else {
				if(item.url.includes('add') && !params.deptId) {
					dept.deptId = vm.vuex_deptId
					dept.deptName = vm.vuex_deptName
				}
				return vm.$u[item.method](api + item.url + urlData, { ...params, ...extraParams, ...dept })
			}
		}
	})
	// 整合接口
	vm.$u.api = apiObj;
}

export default {
	install
}

/**
 * 将对象中所有值为 null 的字段修改为空字符串
 * @param {Object} obj - 需要处理的对象
 * @returns {Object} - 处理后的对象
 */
export const replaceNullWithEmptyString = (obj) => {
  if (!obj || typeof obj !== 'object') return obj;

  const newObj = { ...obj };
  Object.keys(newObj).forEach(key => {
    if (newObj[key] === null) {
      newObj[key] = '';
    } else if (typeof newObj[key] === 'object') {
      newObj[key] = replaceNullWithEmptyString(newObj[key]);
    }
  });
  return newObj;
};

import * as dd from 'dingtalk-jsapi';
 
/**
 * 判断当前是否为钉钉环境
 * @returns {boolean}
 */
export const isDingTalkEnv = () => {
    return dd.env.platform !== 'notInDingTalk';
};
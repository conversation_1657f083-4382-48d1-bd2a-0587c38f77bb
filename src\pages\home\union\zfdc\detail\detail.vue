<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="任务名称:" prop="title" >
					<u-input v-model="form.title" disabled placeholder="请输入任务名称" />
				</u-form-item>
				<!-- <u-form-item label="联合执法类型:" prop="typeName" >
					<u-input v-model="form.typeName" type="select" :select-open="showTypeList" placeholder="请选择联合执法类型" @click="showTypeList = true" />
					<u-action-sheet v-model="showTypeList" :list="typeList" @click="handleTypeClick"></u-action-sheet>
				</u-form-item> -->
				<!-- <u-form-item label="发起人员:" prop="launchName" >
					<u-input v-model="form.launchName" type="popup" placeholder="请选择发起人员"/>
				</u-form-item> -->
				<!-- <u-form-item label="发起部门名称:" prop="launchDeptName" >
					<u-input v-model="form.launchDeptName" type="popup" placeholder="请选择发起部门名称"/>
					<u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('launchDeptId','unit')">选择部门</u-button>
				</u-form-item> -->
				<u-form-item label="负责人:" prop="userName" >
					<u-input v-model="form.userName" type="popup" placeholder="请选择负责人"/>
					<!-- <u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('userId','user')">选择人员</u-button> -->
				</u-form-item>
				<u-form-item label="执行部门:" prop="deptNames" >
					<u-input v-model="form.deptNames" type="popup" placeholder="请选择发起部门与人员"/>
					<!-- <u-button size="mini" type="primary" v-slot="right" @click="handleChooseUser">选择部门人员</u-button> -->
				</u-form-item>
				<u-form-item label="执行人员:" prop="userNames" >
					<u-input v-model="form.userNames" type="popup" placeholder="请选择发起部门名称"/>
					<!-- <u-button size="mini" type="primary" v-slot="right" @click="handleChooseHandleUser('deptIds','unit')">选择部门</u-button> -->
				</u-form-item>
				<u-form-item label="开始时间:" prop="startTime" >
					<u-input v-model="form.startTime" type="popup" placeholder="请选择开始时间" />
					<!-- <u-icon name="calendar"  @click="showStartTime = true"  size="40"></u-icon> -->
					<!-- <u-picker v-model="showStartTime" mode="time" :params="params"  @confirm="handStartTime"></u-picker> -->
				</u-form-item>
				<u-form-item label="结束时间:" prop="endTime" border-bottom="false" >
					<u-input v-model="form.endTime" type="popup" placeholder="请选择结束时间"  />
					<!-- <u-icon name="calendar"  @click="showEndTime = true"  size="40"></u-icon>
					<u-picker v-model="showEndTime" mode="time" :params="params"  @confirm="handEndTime"></u-picker> -->
				</u-form-item>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="任务内容:" prop="content" label-position="top" :border-bottom="false" >
					<u-input v-model="form.content" type="textarea" disabled maxlength="300" height="140" placeholder="请输入任务内容..."/>
				</u-form-item>
				<u-upload
					ref="happenfile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					upload-text="任务方案"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
					:size-type="['compressed']"
					:file-list="happenFile"
					:before-remove="handleRemove"
					:customBtn="true"
					:deletable="false"
				></u-upload>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="检查结果:" prop="doResult" label-position="top" :border-bottom="false" >
					<u-input v-model="form.doResult" type="textarea" disabled maxlength="300" height="140" placeholder="请输入检查结果..."/>
				</u-form-item>
				<u-upload
					ref="vicefile"
					name="files"
					max-count="4"
					width="157"
					height="157"
					upload-text="检查结果图片"
					:header="header"
					:auto-upload="false"
					:action="action"
					:form-data="happenData"
					:size-type="['compressed']"
					:file-list="viceFileList"
					:before-remove="handleRemove"
					:customBtn="true"
					:deletable="false"
				></u-upload>
			</view>
			<view class="list" v-for="(item, idx) in unionList" :key="idx" @click="toUnion(item)">
				<u-gap height="20" bg-color="#F5F5F5"></u-gap>
				<view class="p-lr-30">
					<view class="lists">
						<view class="list-item u-flex u-col-top u-row-between"  >
							<u-image class="img" src="@/static/img/xcfx_icon.png" width="60rpx" height="60rpx"></u-image>
							<view class="list-item-content u-flex u-flex-col u-flex-1 u-col-top">
								<text class="title u-line-1">案件名称: {{ item.title }}</text>
								<text class="text u-line-1">执行人员: {{ item.userName }}</text>
								<text class="text u-line-1">执行部门: {{ item.deptName }}</text>
								<text class="text u-line-1">类型: {{ item.type | typeNames}}</text>
								<text class="text u-line-1">案件详情: {{ item.content }}</text>
							</view>
							<view class="list-item-state u-flex u-col-center u-row-right">
								<view class="circle" :style="{ backgroundColor: circleColor[item.status] }"></view>
								<text>{{ item.status | statusName }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</u-form>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	export default {
		filters:{
			typeNames(type){
				let data ={1:'巡查发现', 2:'监控抓拍', 3:'日常巡查', 4:'简易处罚', 5:'四位一体'}
				return data[type]
			},
			statusName(status) {
				const statusObj = { 1: '待处理', 2: '处理中', 3 : '下发当班组长' ,4 : '待出警', 5 : '已出警', 6 : '到达现场', 9: '已办结' }
				return statusObj[status]
			},
		},
		data() {
			return {
				unionList:[],
				form:{},
				showStartTime:false,
				showEndTime:false,
				showTypeList:false,
				typeList:[],
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				circleColor: {
					2: '#25c548',
					9: '#bdc3bf'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				happenData: {
					tableName: 'case_union',
					status: 2
				},
				happenFile: [],
				viceFileList: [],
				rules: {
					title: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
					typeName: [{ required: true, message: '请选择类型', trigger: 'change' }],
					launchName: [{ required: true, message: '请选择发起人员', trigger: 'change' }],
					launchDeptName: [{ required: true, message: '请选择发起部门名称', trigger: 'change' }],
					userName: [{ required: true, message: '请选择发起人员', trigger: 'change' }],
					deptNames: [{ required: true, message: '请选择发起部门名称', trigger: 'change' }],
					startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
					endTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
					content: [{ required: true, message: '请输入内容描述', trigger: 'blur' }],
				},
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			toUnion(item){
				let {status , type , id} = item
				let types = {1:'xcfx',2:'jkzp',3:'rcxc',4:'jycf',5:'swyt'}
				let parend = item == 'rcxc'?'ygzf':item == 'swyt'?'inspection':'composite'
				if(status == 9){
					this.$u.route({ url: `pages/home/<USER>/${types[type]}/his/his`, params: { id } })
				}else{
					this.$u.route({ url: `pages/home/<USER>/${types[type]}/detail/detail`, params: { id } })
				}
			},
			handleSubmit(state) {
				const params = { ...this.form, status: state }
				this.$refs.uForm.validate(valid => {
					if (valid) {
						// 图片验证，没有图片不通过验证
						if (!this.$refs.happenfile.lists.length) {
							this.$refs.uTips.show({ title: '请上传图片', type: 'error', duration: '2300' })
							return
						}
						// 开始上传
						this.$loading('数据上传中')
						this.$u.api.unionEdit(params).then(res => {
							console.log(res);
							// 遍历列表，查询是否有未上传的图片
							const uploadFile = this.$refs.happenfile.lists.some(item => item.progress !== 100)
							this.happenData.businessId = this.form.unionId
							if (uploadFile) {
								this.$loading('图片上传中')
								this.$refs.happenfile.upload()
							} else {
								uni.showToast({title: '操作成功'})
								uni.hideLoading()
								this.$implement()
							}
						}).catch((err) => {
							console.log(err);
							uni.hideLoading()
						})
					}
				});
			},
			handleTypeClick(idx) {
				const { dictSort, dictLabel } = this.typeList[idx]
				this.form = { ...this.form, type:dictSort, typeName: dictLabel }
			},
			handStartTime(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, startTime: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handEndTime(res) {
				const { year, month, day, hour, minute, second} = res
				this.form = { ...this.form, endTime: `${year}-${month}-${day} ${hour}:${minute}:${second}` }
			},
			handleChooseHandleUser(type,tity) {
				let params = {}
				if (this.form[type]) {
					params.defaultCheckedKeys = this.form[type]
					params.defaultExpandedKeys = this.form[type]
				}
				if(tity == 'unit')	params.name = 'bumen'

				params.type = type
				this.$u.route({ url: 'pages/common/selectUser/selectUser', params })
			},
			setUserData(checks,type) {
				// 选择好人员后的回调
				const checkData = checks[0]
				console.log(type);
				if(type == 'launchId'){
					if (checkData) this.form = { ...this.form, launchName: checkData.label, launchId: checkData.id }
				}else if(type == 'launchDeptId'){
					if (checkData) this.form = { ...this.form, launchDeptName: checkData.label, launchDeptId: checkData.id }
				}else if(type == 'deptIds'){
					if (checkData) this.form = { ...this.form, deptNames: checkData.label, deptIds: checkData.id }
				}else {
					if (checkData) this.form = { ...this.form, userName: checkData.label, userId: checkData.id }
				}
			},
			handleRemove(index, lists) {
				const fileInfo = lists[index]
				const fileId = fileInfo.url.split('?id=')[1]
				if (fileId) {
						return new Promise((resolve, reject) => {
							uni.showModal({
								title: '提示',
								content: '删除后将无法恢复，是否确认删除？',
								success: ({ confirm }) => {
									if (confirm) {
										this.$u.api.deleteFileList({}, fileId).then(resolve).catch(reject)
									} else {
										reject()
									}
								}
							})
						})
				} else {
					return true
				}
			},
			handleAllUpload(lists) {
				// 所有文件上传成功，返回上一级页面
				const isFail = lists.some(item => item.progress !== 100)
				if (!isFail) {
					uni.showToast({title: '操作成功'})
					uni.hideLoading()
					this.$implement()
				}
			},
			handleError() {
				// 文件上传失败，弹出提示是否重新上传
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					content: '图片上传失败，是否重新上传？',
					success: ({ confirm }) => {
						if (confirm) {
							this.$loading('图片上传中')
							this.$refs.happenfile.reUpload()
						} else {
							this.$implement({ immediately: true })
						}
					}
				})
			},

		},
		onLoad(params) {
			this.$loading()
			Promise.all([
				this.$u.api.dictList({dictType:'case_union_type'}),
				this.$u.api.getUnion({},params.id),
				this.$u.api.getFileList({ tableName: 'case_union',  businessId: params.id }),
				this.$u.api.unionDetailList({ unionId: params.id })
			]).then(res=>{
				this.typeList= res[0].rows
				this.form = res[1].data
				this.typeList.forEach(v=>{
					if(v.dictSort&&v.dictSort == this.form.type) this.form = {...this.form,typeName:v.dictLabel}
					if(v.dictLabel) v.text = v.dictLabel
					if(v.dictSort) v.type = v.dictSort
				})
				res[2].rows.map(item => {
					const obj = { url: `https://ygf.xzzfj.jinhua.gov.cn/oss${item.filePath}?id=${item.fileId}` }
					if (item.status == 2) {
						this.happenFile.push(obj)
					} else if (item.status == 9) {
						this.viceFileList.push(obj)
					}
				})
				this.unionList = res[3].rows
				uni.hideLoading()
			}).catch(err=>{
				uni.hideLoading()
				uni.showToast({title: '加载失败', icon: 'error'})
			})
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
.list {
		// padding-bottom: 209rpx;
		.list-item {
			// margin: 20rpx 30rpx 0;
			background-color: #FFFFFF;
			border-radius: 12rpx;
			// box-shadow: 0px 2px 10px 0px rgba(46, 92, 167, 0.1);
			padding: 20rpx 20rpx 30rpx;
			.img {
				flex-shrink: 0;
				margin-right: 20rpx;
			}
			&-content {
				width: 360rpx;
				color: #808080;
				line-height: 32rpx;
				font-size: 24rpx;
				.title {
					width: 100%;
					line-height: 60rpx;
					font-weight: 700;
					font-size: 34rpx;
					color: #333333;
				}
				.text {
					width: 100%;
				}
			}
			&-state {
				width: 180rpx;
				flex-shrink: 0;
				font-size: 28rpx;
				font-weight: 700;
				line-height: 60rpx;
				margin-left: 20rpx;
				.circle {
					width: 16rpx;
					height: 16rpx;
					background-color: #EC5656;
					border-radius: 50%;
					margin-right: 10rpx;
				}
			}
		}
	}
</style>
